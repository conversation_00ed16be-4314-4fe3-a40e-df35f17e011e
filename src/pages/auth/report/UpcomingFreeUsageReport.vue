<template>
    <div>
        <adminheader
            :title="$t('report.upcomingfreeusagereport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="bg-white rounded border p-3">
              <div class="text-xs">Filter</div>

              <!-- <div class="grid grid-cols-2 text-xs  mt-10 text-gray-500">
                <div>Start Date</div>
                <div>End Date</div>
              </div>
              <div class="grid grid-cols-2">
                <DatePicker
                    v-model="startdate"
                    :clearable="true"
                    defaultColor="blue"></DatePicker>
                  <DatePicker
                  v-model="enddate"
                  :clearable="true"
                  defaultColor="blue"></DatePicker>
              </div> -->
              <div class="m-5 flex justify-between">
                <div class="flex items-center">
                  <label for="filternumber" class="text-xs">Days Remain</label>
                  <input v-model="daysfilter" type="number" name="filternumber" class="w-24 mx-2 px-2 py-1 bg-white border border-gray-300 text-sm text-gray-700" placeholder="Days">
                  <button title="Number of days remaining for the upcoming billable report (Default: 31 days)">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                    </svg>
                  </button>
                </div>
                <div class="flex">
                  <button @click="generateReport" class="mr-4 bg-gray-300 w-full rounded px-2 py-1 hover:bg-gray-500 hover:text-white">
                    {{$t('report.generate')}}
                  </button>
                  <button v-if="lists && lists.length > 0" @click="exportCSV" class="bg-gray-300 w-full rounded px-2 py-1 hover:bg-gray-500 hover:text-white">
                    {{$t('report.exportcsv')}}
                  </button>      
                </div>
              </div>
              <div class="border-t border-b py-2">
                <div class="pl-8 grid grid-cols-6 gap-4 text-xs font-bold">
                  <div>Name</div>
                  <div>Activation Date</div>
                  <div>Subscribe Date</div>
                  <div>Trial End Date</div>
                  <div class="truncate">Days Until Billable</div>
                  <div>Free Usage</div>
                </div>
              </div>
              <div class="">
                <div v-if="lists && lists.length > 0" class="border-b py-2" v-for="(vp,vi) in lists" :key="`s_ss_${addvu(vi)}`">
                  <div class="pl-8 grid grid-cols-6 gap-4 text-xs">
                    <div>{{ vp.name }}</div>
                    <div>{{ formatdate(vp.activationdate) }}</div>
                    <div>{{ formatdate(vp.subscribedate) }}</div>
                    <div>{{ formatdate(vp.trialEndDate) }}</div>
                    <div>{{ roundNumber(vp.daysUntilBillable) }}</div>
                    <div>{{ vp.freeusage }}</div>
                  </div>
                </div>
                <div v-else>
                  <div class="text-center py-2">No records found</div>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getPlan, getUserName, getUpcomingBillableReport } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    console.log(authStore)
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
    DatePicker,
  },
  data () {
    let lists: any = undefined
    let plans: any = {}
    let planids: any = []
    let agentsid: any = []
    let agents: any = {}
    let activeonly: boolean = false
    let startdate: any = ''
    let enddate: any = ''
    let daysfilter: any = null
    return {
        lists,
        plans,
        planids,
        agentsid,
        agents,
        activeonly,
        startdate,
        enddate,
        daysfilter
    }
  },
  methods: {
    getAgent (s: string) {
        if (!this.agents[s] && s.trim().length > 0) {
            getUserName({token: this.token, id: s}).then((res: any) => {
                if (res && res.id) {
                    this.agents[res.id] = res
                }
            })
        }
    },
    strnum (p: any) {
      return (p + 1).toString() + '.  '
    },
    getPlans () {
      for (var k = 0; k < this.planids.length; k++ ) {
        if (!this.plans[this.planids[k]]) {
          getPlan({token: this.token, id: this.planids[k]}).then((p: any) => {
            this.plans[p.data.id] = p.data
          })
        }   
      }
    },
    getAgents () {
      for (var k = 0; k < this.agentsid.length; k++ ) {
        if (!this.agents[this.agentsid[k]]) {
          this.getAgent(this.agentsid[k])
        }   
      }
    },
    formatdate (v: any) {
      if (v) {
        return moment(v).format('YYYY-MM-DD')
      } else {
        return '----'
      }
      
    },
    roundNumber (number: any) {
      return Math.round(number);
    },
    packagePlan (p: any) {
      return this.plans[p] ? this.plans[p] : null
    },
    addvu (v: any) {
      return v + 1
    },
    generateReport () {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    escapeQuotes(str: string) {
      return str.replace(/\\/g, '\\\\')  // First escape any existing backslashes
          .replace(/'/g, '\\\'')   // Then escape single quotes
          .replace(/"/g, '\\"') // Finally escape double quotes
          .replace(/\n/g, '   ');   // new line replacement
    },
    exportCSV () {
        let csvRows: any = ['No,Name,Activationdate,Subscribedate,TrialEndDate,Days Until Billable,Freeusage']

        for (var i = 0; i < this.lists.length; i++) {
            let s: any = this.lists[i]
            csvRows.push(`"${i+1}","${s.name}",${this.formatdate(s.activationdate)},${this.formatdate(s.subscribedate)}, ${this.formatdate(s.trialEndDate)}, ${this.roundNumber(s.daysUntilBillable)} days, ${s.freeusage || 0},`);
        }
                    
        var csvString = csvRows.join("\n");
        let csvFile = new Blob([csvString], { type: "text/csv" });
        let downloadLink = document.createElement("a");
        downloadLink.download = `upcomingFreeUsageReportList.csv`
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
    },
    doGetReport () {
      let limit: number = 20
      let v1: any = {
        token: this.token, skip: this.lists.length, limit: limit, daysremain: this.daysfilter
      }

      getUpcomingBillableReport(v1).then((res: any) => {
        this.lists = this.lists.concat(res.data)
        if (res && res.total > this.lists.length) {
          setTimeout(this.doGetReport, 300)
        }
      })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
  }
})
</script>