<template>
    <div>
        <adminheader
            :title="$t('report.billwithagent')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <svgCollection icon="report" dclass="inline-block w-5 h-5" />
                    {{$t('report.filterbybilldate')}}
                    <div class="place-content-center mt-10">
                        <div class="mr-5 inline-block w-full lg:w-1/3 ">
                            <div class="text-xs my-1">{{$t('report.startdate')}}</div>
                            <DatePicker
                                v-model="startdate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 inline-block w-full lg:w-1/3 ">
                            <div class="text-xs my-1">{{$t('report.enddate')}}</div>
                            <DatePicker
                                v-model="enddate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="inline-block w-full lg:w-1/5 mt-2">
                            <button @click="generateReport" class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{$t('report.generate')}}</button>
                        </div>                        
                    </div>
                    <div v-if="reportdata != undefined">
                        <div class="text-center p-10 text-sm" v-if="!reportdata.data || reportdata.count == 0">{{$t('report.emptylist')}}</div>
                        <div v-else class="mt-5">
                            <div class="text-right">
                                <button @click="exportReport" class="rounded shadow px-5 py-1 hover:bg-gray-300 text-sm">{{$t('report.exportcsv')}}</button>
                            </div>
                            <div class="text-sm py-1 border-b">
                                <div class="inline-block w-1/6">{{$t('report.billno')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.billdate')}}/{{$t('subscriptions.activationDate')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.name')}}/{{$t('report.deposit')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.totalamount')}}/{{$t('report.plan')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.amountpaid')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.agent')}}</div>
                            </div>
                             <div class="text-sm py-1 hover:bg-gray-100 border-b" v-for="(l, li) in reportdata.data" :key="l._id">
                                <div @click="copy(l._id)" class="inline-block w-1/6 text-xs cursor-pointer pl-2 hover:text-indigo-600">{{String(Number(li) + 1)}}. {{l.billno}}</div>
                                <div class="inline-block w-1/6">{{formatdate(l.billdate2)}}/{{formatdate(showSubscribeActdate(l.items[0].subscription))}}</div>
                                <div class="inline-block w-1/6 text-xs">
                                    <div @click="copy(l.customeritem[0].name)" class="inline-block cursor-pointer hover:text-indigo-600">{{l.customeritem[0].name}}</div>
                                    <div>{{showSubscribeDeposit(l.items[0].subscription)}}</div>
                                </div>
                                <div class="inline-block w-1/6">{{formatmoney(l.totalamount)}} <br/> {{l.items && l.items.length > 0 && showSubscribePlan(l.items[0].subscription)}}</div>
                                <div class="inline-block w-1/6">{{formatmoney(l.amountpaid)}}</div>
                                <div class="inline-block w-1/6">{{l.items && l.items.length > 0 && showSubscribeAgent(l.items[0].subscription)}}</div>
                            </div>
                            <div class="border-t mt-2 text-left p-2 font-bold">{{$t('report.total')}}</div>
                            <div class="text-right">
                                <div class="" :key="String(ci)" v-for="(cc,ci) in charges">
                                    <div class="w-24 inline-block">{{cc[0]}}</div>
                                    <div class="px-5 inline-block">{{formatmoney(cc[1])}} x {{count1()}} = </div>
                                    <div class="inline-block">{{formatmoney(cc[1] * count1())}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillingReport, getSubscription, getUserName, getPlan } from '../../../api'
export default defineComponent({
    setup () {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
    },
    data() {
        let startdate: any = ''
        let enddate: any = ''
        let reportdata: any = undefined
        let charges: any = [
            ["JNX", 8],
            ["JNXBB", 4],
            ["JCV", 2],
        ]
        let subs: any = {}
        let agents: any = {}
        let plans: any = {}
        return {
            startdate,
            enddate,
            reportdata,
            charges,
            subs,
            agents,
            plans,
        }
    },
    methods: {
        count1 () {
            return (this.reportdata && this.reportdata.data && this.reportdata.data.length) || 0
        },
        back () {
            this.$router.back()
        },
        showSubscribeAgent (s: string) {
            if (this.subs[s]) {
                return (this.subs[s].agent && this.showAgent(this.subs[s].agent)) || 'No Agent'
            } else {
                return 'No Sub'
            }
        },
        showSubscribeActdate (s: string) {
            if (this.subs[s]) {
                return this.subs[s] && this.subs[s].activationdate
            } else {
                return false
            }
        },
        showSubscribePlan (s: string) {
            if (this.subs[s]) {
                return (this.subs[s].plan && this.showPlan(this.subs[s].plan)) || 'No Plan'
            } else {
                return 'No Plan'
            }
        },
        showSubscribeDeposit(s: string) {
            if (this.subs[s]) {
                return (this.subs[s].deposit && this.formatmoney(this.subs[s].deposit)) || '??.??'
            } else {
                return '--.--'
            }
        },
        showSubscribePlanPrice (s: string) {
            if (this.subs[s]) {
                return (this.subs[s].plan && this.showPlanPrice(this.subs[s].plan)) || 'No Plan'
            } else {
                return 'No Plan'
            }
        },
        showPlanPrice (s: string) {
            return (this.plans && this.plans[s] && this.plans[s].price) || 'No Plan Price' 
        },
        showPlan (s: string) {
            return (this.plans && this.plans[s] && this.plans[s].title) || 'No Plan' 
        },
        showAgent (s: string) {
            return (this.agents && this.agents[s] && this.agents[s].name) || 'No Agent2' 
        },
        getAgent (s: string) {
            if (!this.agents[s]) {
                getUserName({token: this.token, id: s}).then((res: any) => {
                    if (res && res.id) {
                        this.agents[res.id] = res
                    }
                })
            }
        },
        getPlan (s: string) {
            if (!this.plans[s]) {
                getPlan ({token: this.token, id: s}).then((res: any) => {
                    if (res && res.data) {
                        this.plans[res.data.id] = res.data
                    }
                })
            }            
        },
        getSubscription (s: string) {
            getSubscription({token: this.token, id: s}).then((res: any) => {
                if (res && res.data && res.data.id) {
                    this.subs[res.data.id] = res.data
                    if (this.subs[res.data.id]['agent']) {
                        this.getAgent(this.subs[res.data.id]['agent'])
                    }
                    if (this.subs[res.data.id]['plan']) {
                        this.getPlan(this.subs[res.data.id]['plan'])
                    }
                }
            })
        },
        loadSubs () {
            // console.log(this.reportdata)
            if (this.reportdata && this.reportdata.data) {
                for (var j=0; j < this.reportdata.data.length; j++) {
                    let td: any = this.reportdata.data[j]
                    if (td.items && td.items.length > 0 && td.items[0].subscription) {
                        this.getSubscription(td.items[0].subscription)
                    }  
                }
            } else {

            }            
        },
        generateReport () {
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate)
                let edate = moment(this.enddate)
                if (sdate.isAfter(edate)) {
                    // #TODO
                } else {
                    getBillingReport({
                        token: this.token,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate),
                    }).then((res: any) => {
                        this.reportdata = res
                        this.loadSubs()
                    })
                }
            } else {
                // #TODO
            }
        },
        exportReport () {
            var Head: any = [[this.$t('report.billno'), this.$t('report.billdate'), this.$t('report.name'), this.$t('report.contact'),this.$t('report.deposit'), this.$t('report.plan'),this.$t('report.price'), this.$t('report.totalamount') ,this.$t('report.amountpaid'), this.$t('report.agent'), this.$t('subscriptions.activationDate')]];
            let row: any = this.reportdata.data
            let sdate = moment(this.startdate)
            let edate = moment(this.enddate)
            if (row) {
                for (var item = 0; item < row.length; ++item) {
                    let l = row[item]
                    let bd = moment(l.billdate2).format('YYYY-MM-DD')
                    let bcontact: any = l.customeritem[0].contact
                    let bname: any = l.customeritem[0].name
                    let totalamount: any = l.totalamount
                    let amountpaid: any = l.amountpaid

                    Head.push([
                        row[item].billno,
                        bd,
                        bname,
                        bcontact,
                        this.showSubscribeDeposit(l.items[0].subscription),               
                        this.showSubscribePlan(l.items[0].subscription),
                        this.showSubscribePlanPrice(l.items[0].subscription),
                        totalamount,
                        amountpaid,
                        this.showSubscribeAgent(l.items[0].subscription),
                        this.formatdate(this.showSubscribeActdate(l.items[0].subscription)),
                    ]);
                }
            }
            

            var csvRows = [];
            for (var cell = 0; cell < Head.length; ++cell) {
                csvRows.push(Head[cell].join(','));
            }
                        
            var csvString = csvRows.join("\n");
            let csvFile = new Blob([csvString], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `jnxreport_${sdate.format('YYYY-MM-DD')}_${edate.format('YYYY-MM-DD')}.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        formatdate (p: any) {
            return (p && moment(p).format('YYYY-MM-DD')) || 'NO-DATE'
        },
        formatdate2 (p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney (p: any) {
            return p && p.toFixed(2)
        },
        copy (p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
    }
})
</script>
