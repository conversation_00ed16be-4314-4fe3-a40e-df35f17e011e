<template>
    <div>
        <adminheader :title="$t('report.ticketsummaryreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back"
                class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{ $t('c.back') }}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <svgCollection icon="report" dclass="inline-block w-5 h-5" />
                    {{ $t('report.filterbybilldate') }}
                    <div class="place-content-center mt-10">
                        <div class="mr-5 inline-block w-full lg:w-1/3">
                            <div class="text-xs my-1">{{ $t('report.startdate') }}</div>
                            <DatePicker v-model="startdate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 inline-block w-full lg:w-1/3 ">
                            <div class="text-xs my-1">{{ $t('report.enddate') }}</div>
                            <DatePicker v-model="enddate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="inline-block w-full lg:w-1/5 mt-2">
                            <button @click="generateReport"
                                class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{ $t('report.generate') }}</button>
                        </div>
                    </div>
                    <div v-if="reportdata != undefined">
                        <div class="text-center p-10 text-sm" v-if="!reportdata.data || reportdata.total == 0">
                            {{ $t('report.emptylist') }}</div>
                        <div v-else class="mt-5">
                            <div class="w-full lg:flex">
                                <!-- <div class="w-full lg:w-1/2 px-2">
                                    <div class="border shadow" id="chart">
                                        <VueApexCharts type="line" height="350" :options="chartOptions"
                                        :series="series"></VueApexCharts>
                                    </div>
                                </div> -->
                                <div class="w-full lg:w-full px-2">
                                    <div class="border shadow" id="chart2">
                                        <VueApexCharts type="bar" height="350" :options="chartOptionsBar"
                                            :series="seriesBar"></VueApexCharts>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right mt-2">
                                <button @click="exportReport"
                                    class="rounded border shadow px-5 py-1 hover:bg-gray-300 text-sm">{{ $t('report.exportcsv') }}</button>
                            </div>
                            <div class="p-4 mt-4">
                                <table class="w-full table-auto shadow">
                                    <thead>
                                        <tr class="border-b">
                                            <th class="p-1">{{ $t('report.date') }}</th>
                                            <th class="p-1">{{ $t('report.averageresolutiontime') }}</th>
                                            <th class="p-1">{{ $t('report.ticketno') }}</th>
                                            <th class="p-1">{{ $t('report.ticketcreatedat') }}</th>
                                            <th class="p-1">{{ $t('report.ticketclosing') }}</th>
                                            <th class="p-1">{{ $t('report.resolvingtime') }} (Days)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b" v-for="data in reportdata.data" :key="data._id">
                                            <td>{{ formatdate3(data.date) }}</td>
                                            <td>{{ Math.round(data.averageResolutionTime * 10) / 10 }}</td>
                                            <td>
                                                <p class="text-blue-500 cursor-pointer hover:text-blue-700" @click="gotoTicket(ticket.ticketNo)" v-for="ticket in data.tickets">
                                                    {{ ticket.ticketNo }}
                                                </p>
                                            </td>
                                            <td>
                                                <p v-for="ticket in data.tickets">{{ formatdate(ticket.createdAt) }}</p>
                                            </td>
                                            <td>
                                                <p v-for="ticket in data.tickets">{{ formatdate(ticket.closingDate) }}
                                                </p>
                                            </td>
                                            <td>
                                                <p v-for="ticket in data.tickets">{{ calculateDaysDifference(ticket.createdAt, ticket.closingDate) }}
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getOpenTicketReport, getTicketReport, getTickets } from '../../../api'
import config from '../../../config'
import VueApexCharts from "vue3-apexcharts";
export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
        VueApexCharts
    },
    data() {
        let startdate: any = ''
        let enddate: any = ''
        let reportdata: any = undefined
        let reportdata2: any = undefined
        let building: any = ''
        return {
            startdate,
            enddate,
            reportdata,
            reportdata2,
            building,
            series: [{
                name: "Avg Time",
                data: [] as any
            }],
            chartOptions: {
                chart: {
                    height: 350,
                    type: 'line',
                    zoom: {
                        enabled: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'straight'
                },
                title: {
                    text: 'Average Resolution Time',
                    align: 'left'
                },
                grid: {
                    row: {
                        colors: ['#f3f3f3', 'transparent'],
                        opacity: 0.5
                    },
                },
                xaxis: {
                    categories: [] as any,
                }
            },
            seriesBar: [{
                name: 'Resolved Tickets',
                data: [] as any
            },
            {
                name: 'Unresolve Tickets',
                data: [] as any
            } 
            ],
            chartOptionsBar: {
                chart: {
                    height: 350,
                    type: 'bar',
                },
                plotOptions: {
                    bar: {
                        borderRadius: 10,
                        columnWidth: '55%',
                        dataLabels: {
                            position: 'top',
                        },
                    }
                },
                dataLabels: {
                    enabled: true,
                    offsetY: -20,
                    style: {
                        fontSize: '12px',
                        colors: ["#304758"]
                    }
                },
                xaxis: {
                    categories: [] as any,
                    position: 'bottom',
                    axisBorder: {
                        show: false
                    },
                    axisTicks: {
                        show: false
                    },
                    crosshairs: {
                        fill: {
                            type: 'gradient',
                            gradient: {
                                colorFrom: '#D8E3F0',
                                colorTo: '#BED1E6',
                                stops: [0, 100],
                                opacityFrom: 0.4,
                                opacityTo: 0.5,
                            }
                        }
                    },
                    tooltip: {
                        enabled: true,
                    }
                },
                yaxis: {
                    axisBorder: {
                        show: true
                    },
                    axisTicks: {
                        show: false,
                    },
                    labels: {
                        show: false,
                    }
                },
                stroke: {
                    show: true,
                    width: 2,
                    colors: ['transparent']
                },
                fill: {
                    opacity: 1,
                },
                colors: ['#0088FB', '#FF4560'],
                title: {
                    text: 'Number of Tickets',
                    align: 'left',
                }
            },
        }
    },
    methods: {
        back() {
            this.$router.back()
        },
        generateReport() {
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate)
                let edate = moment(this.enddate)
                if (sdate.isAfter(edate)) {
                    // #TODO
                } else {
                    this.generateReportUnresolve()
                    getTicketReport({
                        token: this.token,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate),
                    }).then((res: any) => {
                        this.reportdata = res
                        res.data.forEach((item: any) => {
                            let roundedDay = Math.round(item.averageResolutionTime * 24 * 10) / 10;
                            this.series[0].data.push(roundedDay)
                            this.chartOptions.xaxis.categories.push(item.date)
                            this.seriesBar[0].data.push(item.tickets.length)
                            this.chartOptionsBar.xaxis.categories.push(item.date)
                        });
                    })
                }
            }
        },
        generateReportUnresolve() {
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate)
                let edate = moment(this.enddate)
                if (sdate.isAfter(edate)) {
                    // #TODO
                } else {
                    getOpenTicketReport({
                        token: this.token,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate)
                    }).then((res: any) => {
                        this.reportdata2 = res
                        res.data.forEach((item: any) => {
                            let count = item.count
                            this.seriesBar[1].data.push(count)
                            // let roundedDay = Math.round(item.averageResolutionTime * 24 * 10) / 10;
                            // this.series[0].data.push(roundedDay)
                            // this.chartOptions.xaxis.categories.push(item.date)
                            // this.seriesBar[0].data.push(item.tickets.length)
                            this.chartOptionsBar.xaxis.categories.push(item._id.date)
                        });
                    })
                }
            }
        },
        exportReport() {
            var Head: any = [[this.$t('report.date'), this.$t('report.averageresolutiontime'), this.$t('report.ticketno'), this.$t('report.ticketcreatedat'), this.$t('report.ticketclosing'), this.$t('report.resolvingtime')]];
            let row: any = this.reportdata.data
            let sdate = moment(this.startdate)
            let edate = moment(this.enddate)
            if (row) {
                for (var item = 0; item < row.length; ++item) {
                    let data = row[item]
                    let date = moment(data.date).format('YYYY-MM-DD')

                    const tickets = data.tickets.map((ticket: any) => ({
                        ticketNo: `${ticket.ticketNo}`,
                        createdAt: moment(ticket.createdAt).format('YYYY-MM-DD HH:mm'),
                        closingDate: moment(ticket.closingDate).format('YYYY-MM-DD HH:mm')
                    }));
                    for (var i = 0; i < tickets.length; ++i) {
                        if (i === 0) {
                            Head.push([
                                date,
                                Math.round(data.averageResolutionTime * 10) / 10,
                                tickets[i].ticketNo,
                                tickets[i].createdAt,
                                tickets[i].closingDate,
                                moment(tickets[i].closingDate).diff(moment(tickets[i].createdAt), 'days')
                            ]);
                        } else {
                            Head.push([
                                '',
                                '',
                                tickets[i].ticketNo,
                                tickets[i].createdAt,
                                tickets[i].closingDate,
                                moment(tickets[i].closingDate).diff(moment(tickets[i].createdAt), 'days')
                            ]);
                        }
                    }
                }
            }

            var csvRows = [];
            for (var cell = 0; cell < Head.length; ++cell) {
                csvRows.push(Head[cell].join(','));
            }

            var csvString = csvRows.join("\n");
            let csvFile = new Blob([csvString], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `ticketreport_${sdate.format('YYYY-MM-DD')}_${edate.format('YYYY-MM-DD')}.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        formatdate(p: any) {
            return moment(p).format('YYYY-MM-DD HH:mm')
        },
        formatdate2(p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatdate3(p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        formatmoney(p: any) {
            return p && p.toFixed(2)
        },
        calculateDaysDifference(createdAt: any, closingDate: any) {
            let created = moment(createdAt);
            let closed = moment(closingDate);
            return closed.diff(created, 'days');
        },
        copy(p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
        gotoTicket(ticketNo: any) {
            getTickets({token: this.token, $keywords: ticketNo}).then((res: any) => {
                const ticket = res.data[0]
                const url = this.$router.resolve({
                    name: 'ticketsCRUD',
                    params: { id: ticket.id }
                    }).href;

                window.open(url, '_blank');
            })
        }
    }
})
</script>
