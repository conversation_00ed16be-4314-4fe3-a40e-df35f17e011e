<template>
    <div>
        <adminheader
            :title="$t('report.billbyplan')" :backButton="true" :backTitle="'Back to reports'" :backFunction="back">
        </adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <div class="place-content-center mt-10 flex items-center">
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{ $t('report.startdate') }}</div>
                            <DatePicker v-model="startdate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{ $t('report.enddate') }}</div>
                            <DatePicker v-model="enddate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{ $t('report.plan') }}</div>
                            <select v-model="type"
                                class="w-full border border-gray-300 rounded px-2 py-1 focus:outline-none">
                                <option value="all">All</option>
                                <option value="FL">FL</option>
                                <option value="HFCD">HFCD</option>
                            </select>
                        </div>
                        <div class="w-1/6 mt-5">
                            <button @click="generateReport"
                                class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{ $t('report.generate') }}</button>
                        </div>
                    </div>
                    <div style="height: 500px;" class="p-4 my-4 shadow">
                        <VueApexCharts v-if="reportdata && reportdata.data && reportdata.data.length > 0" type="bar"
                            height="100%" width="100%" :options="chartOptions" :series="series"></VueApexCharts>
                        <div v-else class="text-center p-10 text-sm">No data available</div>
                    </div>
                    <div v-if="reportdata != undefined">
                        <div class="text-center p-10 text-sm" v-if="!reportdata.data || reportdata.count == 0">
                            {{ $t('report.emptylist') }}</div>
                        <div v-else class="mt-5">
                            <div v-if="loadingSubs">
                                <loading />
                            </div>
                            <template v-else>
                                <div class="text-right">
                                    <button @click="exportReport"
                                        class="rounded shadow px-5 py-1 hover:bg-gray-300 text-sm">{{ $t('report.exportcsv') }}</button>
                                </div>
                                <div class="text-sm py-1 border-b">
                                    <div class="inline-block w-1/3">{{ $t('report.plan') }}</div>
                                    <div class="inline-block w-1/3">{{ $t('report.count') }}</div>
                                    <div class="inline-block w-1/3">{{ $t('report.total') }}</div>
                                </div>
                                <div class="text-sm py-1 hover:bg-gray-100 border-b" v-for="(l, li) in reportdata.data"
                                    :key="l._id">
                                    <div @click="copy(l._id)"
                                        class="inline-block w-1/3 text-xs cursor-pointer pl-2 hover:text-indigo-600">{{
                                        l._id }}</div>
                                    <div class="inline-block w-1/3">{{ l.count }}</div>
                                    <div class="inline-block w-1/3">{{ formatmoney(l.total) }}</div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillingByPlanReport, getSubscription, getUserName, getPlan } from '../../../api'
import loading from '../../../components/cvui/loading.vue'
import VueApexCharts from "vue3-apexcharts";
export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
        loading,
        VueApexCharts
    },
    data() {
        let startdate: any = ''
        let enddate: any = ''
        let reportdata: any = undefined
        let loadingSubs: any = false
        let type: any = 'all'
        let sortBy: any = 'revenue'
        let displayCount: any = 15
        return {
            startdate,
            enddate,
            reportdata,
            loadingSubs,
            type,
            sortBy,
            displayCount
        }
    },
    computed: {
        processedData() {
            return this.reportdata.data.map((item: any) => ({
                plan: item._id,
                subscribers: item.count,
                revenue: item.total,
                arpu: item.count > 0 ? Number((item.total / item.count).toFixed(2)) : 0
            }));
        },
        sortByLabel() {
            if (this.sortBy === 'revenue') return 'Revenue';
            if (this.sortBy === 'subscribers') return 'Subscribers';
            return 'Average Revenue Per User';
        },
        displayData() {
            return [...this.processedData]
                .sort((a, b) => {
                    if (this.sortBy === 'revenue') return b.revenue - a.revenue;
                    if (this.sortBy === 'subscribers') return b.subscribers - a.subscribers;
                    return b.arpu - a.arpu;
                })
                .slice(0, this.displayCount);
        },
        series() {
            return [{
                name: this.sortByLabel,
                data: this.displayData.map(item => item[this.sortBy])
            }];
        },
        chartOptions() {
            return {
                chart: {
                    type: 'bar',
                    toolbar: {
                        show: false
                    },
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif'
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 4,
                        dataLabels: {
                            position: 'top'
                        }
                    }
                },
                colors: ['#6366f1'],
                dataLabels: {
                    enabled: true,
                    formatter: (value) => {
                        if (this.sortBy === 'revenue') {
                            return `RM ${(value / 1000).toFixed(1)}K`;
                        } else if (this.sortBy === 'arpu') {
                            return `RM ${parseFloat(value).toFixed(2)}`;
                        }
                        return this.formatmoney(value);
                    },
                    offsetY: -20,
                    style: {
                        fontSize: '12px',
                        colors: ['#304758']
                    }
                },
                xaxis: {
                    categories: this.displayData.map(item => {
                        // Truncate long plan names
                        const name = item.plan;
                        return name.length > 22 ? name.substring(0, 22) + '...' : name;
                    }),
                },
                yaxis: {
                    title: {
                        text: this.sortByLabel
                    },
                    labels: {
                        formatter: (value) => {
                            if (this.sortBy === 'revenue') {
                                return `RM ${(value / 1000).toFixed(1)}K`;
                            } else if (this.sortBy === 'arpu') {
                                return `RM ${parseFloat(value).toFixed(2)}`;
                            }
                            return this.formatmoney(value);
                        }
                    }
                },
                tooltip: {
                    custom: ({ series, seriesIndex, dataPointIndex }) => {
                        const data = this.displayData[dataPointIndex];
                        const planName = data.plan;
                        const value = series[seriesIndex][dataPointIndex];

                        let formattedValue;
                        if (this.sortBy === 'revenue' || this.sortBy === 'arpu') {
                            formattedValue = 'RM ' + this.formatmoney(value);
                        } else {
                            formattedValue = value;
                        }

                        const subscribers = data.subscribers;

                        const arpu = this.formatmoney(data.arpu)

                        return `
                        <div class="apexcharts-tooltip-box" style="padding: 8px;">
                            <div style="margin-bottom: 5px; font-weight: bold;">${planName}</div>
                            <div>${this.sortByLabel}: ${formattedValue}</div>
                            <div>Subscribers: ${subscribers}</div>
                            <div>Average Revenue Per User: RM ${arpu}</div>
                        </div>
                        `;
                    }
                },
                title: {
                    text: `Plans Analysis - Top ${this.displayCount} by ${this.sortByLabel}`,
                    align: 'center',
                    style: {
                        fontSize: '18px',
                        fontWeight: 'bold'
                    }
                }
            };
        }
    },
    methods: {
        count1() {
            return (this.reportdata && this.reportdata.data && this.reportdata.data.length) || 0
        },
        back() {
            this.$router.back()
        },
        generateReport() {
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate)
                let edate = moment(this.enddate)
                if (sdate.isAfter(edate)) {
                    // #TODO
                } else {
                    getBillingByPlanReport({
                        token: this.token,
                        type: this.type,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate),
                    }).then((res: any) => {
                        this.reportdata = res
                        this.loadingSubs = false
                    })
                }
            } else {
                // #TODO
            }
        },
        exportReport() {
            var Head: any = [[this.$t('report.plan'), this.$t('report.count'), this.$t('report.total')]];
            let row: any = this.reportdata.data
            let sdate = moment(this.startdate)
            let edate = moment(this.enddate)
            let type: any = this.type

            const addBackslashes = (str: any) => {
                // Add a backslash before commas, single quotes, and double quotes
                return str.replace(/\\/g, '\\\\') // escape existing backslashes
                    //.replace(/,/g, '\\,')
                    .replace(/'/g, '\\\'')
                    .replace(/"/g, '\\"');
            }
            if (row) {
                for (var item = 0; item < row.length; ++item) {
                    Head.push([
                        row[item]._id,
                        row[item].count,
                        row[item].total,
                    ]);
                }
            }


            var csvRows = [];
            for (var cell = 0; cell < Head.length; ++cell) {
                csvRows.push(Head[cell].join(','));
            }

            var csvString = csvRows.join("\n");
            let csvFile = new Blob([csvString], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `jnxreport_${sdate.format('YYYY-MM-DD')}_${edate.format('YYYY-MM-DD')}_${type}.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        formatdate(p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        formatdate2(p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney(p: any) {
            return p && p.toFixed(2)
        },
        copy(p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
    }
})
</script>
