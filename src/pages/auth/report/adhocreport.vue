<template>
    <div>
        <adminheader
            :title="$t('report.adhocreport')" :backButton="true" :backTitle="'Back to reports'" :backFunction="back"></adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="space-y-6">
                    <!-- Filter Section -->
                    <div class="bg-slate-50 border border-slate-200 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-slate-700 mb-3">Filters</h3>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input 
                                type="checkbox" 
                                v-model="activeonly"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                            />
                            <span class="text-sm text-slate-600">Show active users only</span>
                        </label>
                    </div>
                    <!-- Actions Section -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button 
                            @click="generateReport"
                            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            :disabled="loading"
                        >
                            {{$t('report.generate')}}
                        </button>
                        <button 
                            v-if="lists?.length && !loading" 
                            @click="exportCSV"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                        >
                            {{$t('report.exportcsv')}}
                        </button>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-12">
                    <div class="text-center max-w-md">
                        
                        <h3 class="text-lg font-medium text-slate-900 mb-2">Loading Report Data</h3>
                        
                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div 
                                class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                                :style="{ width: loadingProgress.percentage + '%' }"
                            ></div>
                        </div>
                        
                        <!-- Progress Info -->
                        <div class="space-y-1">
                            <p class="text-slate-600 text-sm">
                                Loading {{ loadingProgress.loaded }} of {{ loadingProgress.total }} records
                            </p>
                            <p class="text-blue-600 text-sm font-medium">
                                {{ loadingProgress.percentage }}% Complete
                            </p>
                            <p class="text-slate-400 text-xs">
                                {{ loadingProgress.total - loadingProgress.loaded }} remaining
                            </p>
                        </div>
                    </div>
                </div>

                <div v-else-if="lists?.length" class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-slate-200 rounded-lg">
                        <thead class="bg-slate-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Building
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Name
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Activation Date
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Plan
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Free Usage
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Price
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Agent
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider border-b border-slate-200">
                                    Status
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-200">
                            <tr v-for="(vp, vi) in lists" :key="vi" class="hover:bg-slate-50 transition-colors duration-150">
                                <td class="px-4 py-3 text-sm text-slate-900">
                                    {{vi + 1}}. {{vp.address.building || 'The Grand Subang Jaya SS15'}}
                                </td>
                                <td class="px-4 py-3 text-sm text-slate-900">
                                    {{vp.name}}
                                </td>
                                <td class="px-4 py-3 text-sm text-slate-600">
                                    {{formatDate(vp.activationdate)}}
                                </td>
                                <td class="px-4 py-3 text-sm text-slate-900">
                                    {{plans[vp.plan]?.title || '---'}}
                                </td>
                                <td class="px-4 py-3 text-sm text-slate-600">
                                    {{vp.freeusage}}
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-slate-900">
                                    {{plans[vp.plan]?.price?.toFixed(2) || '--.--'}}
                                </td>
                                <td class="px-4 py-3 text-sm text-slate-600">
                                    {{agents[vp.agent]?.name || ''}}
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <span :class="vp.statustxt === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" 
                                          class="px-2 py-1 rounded-full text-xs font-medium">
                                        {{vp.statustxt}}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import { getSubscriptions, getPlan, getUserName } from '../../../api'

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        
        return {
            token: computed(() => authState.token),
            authStore,
            authState,
        }
    },
    components: {
        adminheader,
    },
    data() {
        return {
            lists: [],
            plans: {},
            agents: {},
            activeonly: false,
            loading: false,
            loadingProgress: {
                loaded: 0,
                total: 0,
                percentage: 0
            }
        }
    },
    methods: {
        async getAgent(id: string) {
            if (!this.agents[id] && id.trim().length > 0) {
                const res = await getUserName({ token: this.token, id })
                if (res?.id) {
                    this.agents[res.id] = res
                }
            }
        },
        async getPlanData(id: string) {
            if (!this.plans[id]) {
                const res = await getPlan({ token: this.token, id })
                if (res?.data) {
                    this.plans[res.data.id] = res.data
                }
            }
        },
        formatDate(date: any) {
            return moment(date).format('YYYY-MM-DD')
        },
        async generateReport() {
            this.loading = true
            this.lists = []
            this.loadingProgress = { loaded: 0, total: 0, percentage: 0 }
            try {
                await this.fetchAllData()
            } finally {
                this.loading = false
            }
        },
        exportCSV() {
            const csvRows = this.lists.map((item: any, index: number) => {
                const building = item.address.building || 'The Grand Subang Jaya SS15'
                const planTitle = this.plans[item.plan]?.title || ''
                const price = this.plans[item.plan]?.price || 0
                const agentName = this.agents[item.agent]?.name || ''
                
                return `${index + 1},${building},${item.name},${this.formatDate(item.activationdate)},${planTitle},${item.freeusage},${price},${agentName}`
            })
            
            const csvString = csvRows.join("\n")
            const csvFile = new Blob([csvString], { type: "text/csv" })
            const downloadLink = document.createElement("a")
            downloadLink.download = 'userList.csv'
            downloadLink.href = window.URL.createObjectURL(csvFile)
            downloadLink.style.display = "none"
            document.body.appendChild(downloadLink)
            downloadLink.click()
        },
        async fetchAllData() {
            const limit = 20
            let hasMore = true
            
            // First call to get total count
            const initialParams = {
                token: this.token,
                skip: 0,
                limit,
                ...(this.activeonly && { statustxt: 'active' })
            }
            
            const initialRes = await getSubscriptions(initialParams)
            this.loadingProgress.total = initialRes.total
            this.lists = initialRes.data
            this.loadingProgress.loaded = initialRes.data.length
            this.loadingProgress.percentage = Math.round((this.loadingProgress.loaded / this.loadingProgress.total) * 100)
            
            // Process initial batch
            const planIds = [...new Set(initialRes.data.map((item: any) => item.plan))]
            const agentIds = [...new Set(initialRes.data.map((item: any) => item.agent))]
            
            await Promise.all([
                ...planIds.map(id => this.getPlanData(id)),
                ...agentIds.map(id => this.getAgent(id))
            ])
            
            hasMore = initialRes.total > this.lists.length
            
            // Continue fetching remaining data
            while (hasMore) {
                const params = {
                    token: this.token,
                    skip: this.lists.length,
                    limit,
                    ...(this.activeonly && { statustxt: 'active' })
                }
                
                const res = await getSubscriptions(params)
                this.lists = this.lists.concat(res.data)
                
                // Update progress
                this.loadingProgress.loaded = this.lists.length
                this.loadingProgress.percentage = Math.round((this.loadingProgress.loaded / this.loadingProgress.total) * 100)
                
                const planIds = [...new Set(res.data.map((item: any) => item.plan))]
                const agentIds = [...new Set(res.data.map((item: any) => item.agent))]
                
                await Promise.all([
                    ...planIds.map(id => this.getPlanData(id)),
                    ...agentIds.map(id => this.getAgent(id))
                ])
                
                hasMore = res.total > this.lists.length
            }
        },
        back() {
            this.$router.back()
        },
    }
})
</script>