<template>
    <div>
        <adminheader
            :title="$t('navigations.upcomingreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <svgCollection icon="report" dclass="inline-block w-5 h-5" />
                    {{$t('report.duebilldays')}}
                    <input class="text-center rounded shadow ml-5 p-2" type="number" v-model="billdays" />
                    <button @click="generateReport" class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{$t('report.generate')}}</button>
                </div>
                <div v-if="lists != undefined">
                    <div class="text-center p-10 text-sm" v-if="lists.length == 0">{{$t('report.emptylist')}}</div>
                    <div v-else class="mt-5">
                        <div class="text-right">
                            <button @click="exportReport" class="rounded shadow px-5 py-1 hover:bg-gray-300 text-sm">{{$t('report.exportcsv')}}</button>
                        </div>
                        <div class="text-sm py-1 border-b">
                            <div class="inline-block w-1/6">{{$t('report.id')}}</div>
                            <div class="inline-block w-1/6">{{$t('report.lastactdate')}}</div>
                            <div class="inline-block w-2/6">{{$t('report.name')}}/{{$t('report.contact')}}</div>
                            <div class="inline-block w-2/6">{{$t('report.address')}}</div>
                        </div>
                        <div class="text-sm py-1 hover:bg-gray-100 border-b" v-for="l in lists" :key="l._id">
                            <div @click="copy(l._id)" class="inline-block w-1/6 text-xs cursor-pointer pl-2 hover:text-indigo-600">{{l._id}}</div>
                            <div class="inline-block w-1/6">{{formatDate2(l.lastbilldate2)}} | {{formatDate2(l.activationdate2)}}</div>
                            <div class="inline-block w-2/6 text-xs"><div @click="copy(l.customeritem[0].name)" class="inline-block cursor-pointer hover:text-indigo-600">{{l.customeritem[0].name}}</div> / <div @click="copy(l.customeritem[0].contact)" class="inline-block cursor-pointer hover:text-indigo-600">{{l.customeritem[0].contact}}</div></div>
                            <div class="inline-block w-2/6 text-xs">{{l.address.address}}</div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-center p-10 text-sm">
                    {{$t('report.generatelisthints')}}
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import { getUnbillReport } from '../../../api'
import { crossStore } from '../../../store/cross-store'

export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
  },
  data () {
    let billdays: any = 30
    let lists: any = undefined
    return {
        billdays,
        lists,
    }
  },
  methods: {
    generateReport () {
        getUnbillReport({ token: this.token, days: this.billdays }).then((res: any) => {
            if (res && res.data) {
                this.lists = res.data
            } else {
                this.lists = []
            }
        })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    formatDate2 (p: any) {
        return (p && moment(p).format('DD/MM')) || '00/00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
    exportReport () {

        // #TODO adding customerID

        var Head: any = [['#ID', 'Last Bill Date', 'Name/Contact', 'Address']];
        let row: any = this.lists
        for (var item = 0; item < row.length; ++item) {
            let l = row[item]
            let bdate: any = this.formatDate(l.lastbilldate2 || l.activationdate2)
            let bcontact: any = `${l.customeritem[0].contact} / ${l.customeritem[0].name}`
            let baddress: any = l.address.address
            Head.push([
                row[item]._id,
                bdate,
                bcontact,
                baddress
            ]);
        }

        var csvRows = [];
        for (var cell = 0; cell < Head.length; ++cell) {
            csvRows.push(Head[cell].join(','));
        }
                    
        var csvString = csvRows.join("\n");
        let csvFile = new Blob([csvString], { type: "text/csv" });
        let downloadLink = document.createElement("a");
        downloadLink.download = `upcomingBills_${moment().format('DD.MM.YY')}_${this.billdays}.csv`
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
    }
  }
})
</script>