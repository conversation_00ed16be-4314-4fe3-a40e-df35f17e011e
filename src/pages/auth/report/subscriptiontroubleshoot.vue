<template>
  <div>
    <adminheader :title="$t('report.subscriptiontroubleshoot')" :backButton="true" :backTitle="'Back to reports'"
            :backFunction="back" />

    <div class="mx-1 my-2 mr-2">
      <div class="bg-white rounded border p-3">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-5">
          <div class="flex-1">
            <select v-model="status"
              class="w-full text-sm border rounded px-3 py-2 bg-white focus:ring focus:ring-blue-200">
              <option value="" disabled>{{ $t('report.selectstatus') }}</option>
              <option v-for="statusKey in statuses" :key="statusKey" :value="statusKey">
                {{ $t(`subscriptions.${statusKey}`) }}
              </option>
            </select>
          </div>

          <div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <button @click="generateReport"
              class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
              :disabled="loading">
              {{ $t('report.generate') }}
            </button>

            <button v-if="lists.length && !loading" @click="exportCSV"
              class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition">
              {{ $t('report.exportcsv') }}
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="text-center max-w-md">
            <h3 class="text-lg font-medium text-slate-900 mb-2">Loading Report Data</h3>
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div 
                class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                :style="{ width: loadingProgress.percentage + '%' }"
              ></div>
            </div>
            <!-- Progress Info -->
            <div class="space-y-1">
              <p class="text-slate-600 text-sm">
                Loading {{ loadingProgress.loaded }} of {{ loadingProgress.total }} records
              </p>
              <p class="text-blue-600 text-sm font-medium">
                {{ loadingProgress.percentage }}% Complete
              </p>
              <p class="text-slate-400 text-xs">
                {{ loadingProgress.total - loadingProgress.loaded }} remaining
              </p>
            </div>
          </div>
        </div>

        <table v-else class="w-full text-xs border-collapse border mt-5">
          <thead class="bg-gray-100">
            <tr>
              <th>#</th>
              <th>Building</th>
              <th>Name</th>
              <th>Subscribe Date</th>
              <th>Contract Months</th>
              <th>Plan</th>
              <th>Free Usage</th>
              <th>Price</th>
              <th>Special Price</th>
              <th>Agent</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!lists.length">
              <td colspan="11" class="text-center py-2">No records found</td>
            </tr>
            <tr v-for="(vp, i) in lists" :key="vp.id">
              <td>{{ i + 1 }}</td>
              <td>{{ vp.address.building || 'The Grand Subang Jaya SS15' }}</td>
              <td>{{ vp.name }}</td>
              <td>{{ formatDate(vp.subscribedate) }}</td>
              <td>{{ vp.contractmonths }}</td>
              <td>{{ plans[vp.plan]?.title || '---' }}</td>
              <td>{{ vp.freeusage }}</td>
              <td>{{ plans[vp.plan]?.price?.toFixed(2) || '--.--' }}</td>
              <td>{{ vp.price }}</td>
              <td>{{ agents[vp.agent]?.name || '---' }}</td>
              <td>{{ vp.statustxt }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getPlan, getUserName } from '../../../api'
import config from '../../../config'

export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    return {
      token: computed(() => authState.token),
    }
  },
  components: { adminheader },
  data() {
    return {
      lists: [] as any[],
      plans: {} as Record<string, any>,
      agents: {} as Record<string, any>,
      fetchedPlanIds: new Set<string>(),
      fetchedAgentIds: new Set<string>(),
      status: 'active',
      statuses: config.subscriptionStatus,
      loading: false,
      loadingProgress: {
        loaded: 0,
        total: 0,
        percentage: 0
      }
    }
  },
  methods: {
    back() {
      this.$router.back()
    },
    formatDate(date: any) {
      return date ? moment(date).format('YYYY-MM-DD') : '----'
    },
    async fetchPlan(id: string) {
      if (!this.fetchedPlanIds.has(id)) {
        const res = await getPlan({ token: this.token, id })
        if (res?.data) {
          this.plans[res.data.id] = res.data
          this.fetchedPlanIds.add(res.data.id)
        }
      }
    },
    async fetchAgent(id: string) {
      if (!this.fetchedAgentIds.has(id) && id.trim()) {
        const res = await getUserName({ token: this.token, id })
        if (res?.id) {
          this.agents[res.id] = res
          this.fetchedAgentIds.add(res.id)
        }
      }
    },
    generateReport() {
      this.lists = []
      this.loading = true
      this.loadingProgress = { loaded: 0, total: 0, percentage: 0 }
      this.loadReport()
    },
    async loadReport() {
      const limit = 20
      const params: any = {
        token: this.token,
        skip: this.lists.length,
        limit,
        statustxt: this.status,
        params: ['statustxt']
      }

      try {
        const res = await getSubscriptions(params)
        if (res?.total && this.loadingProgress.total === 0) {
          this.loadingProgress.total = res.total
        }

        if (res?.data?.length) {
          this.lists.push(...res.data)
          this.loadingProgress.loaded = this.lists.length
          this.loadingProgress.percentage = Math.round((this.loadingProgress.loaded / this.loadingProgress.total) * 100)

          await Promise.all([
            ...res.data.map((sub: any) => sub.plan ? this.fetchPlan(sub.plan) : Promise.resolve()),
            ...res.data.map((sub: any) => sub.agent ? this.fetchAgent(sub.agent) : Promise.resolve())
          ])

          if (res.total > this.lists.length) {
            setTimeout(this.loadReport, 300)
          } else {
            this.loading = false
          }
        } else {
          this.loading = false
        }
      } catch (error) {
        this.loading = false
        crossStore.SetNotmsg({ title: 'Error', msg: 'Failed to load report data', type: 'error' })
      }
    },
    escapeCSV(str: string) {
      return String(str || '')
        .replace(/\\/g, '\\\\')
        .replace(/"/g, '\\"')
        .replace(/\n/g, ' ')
    },
    exportCSV() {
      const header = [
        'No,Building,Unit,Address,Username,Name,SubscriptionID,Subscribedate,ContractMonths,Activationdate,Plan,Freeusage,Price,SpecialPrice,Agent,Deposit,Advancedpayment,Status'
      ]
      const rows = this.lists.map((s, i) => [
        i + 1,
        this.escapeCSV(s.address.building || 'The Grand Subang Jaya SS15'),
        this.escapeCSV(s.address.unit),
        this.escapeCSV(s.address.address),
        s.username,
        s.name,
        s.sid || '----',
        this.formatDate(s.subscribedate),
        s.contractmonths,
        this.formatDate(s.activationdate),
        this.plans[s.plan]?.title || '----',
        s.freeusage || 0,
        s.price || this.plans[s.plan]?.price || 0,
        s.price,
        this.agents[s.agent]?.name || '---',
        s.deposit,
        s.advancedpayment,
        s.statustxt
      ].join(','))

      const csvContent = [header, ...rows].join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = 'userList.csv'
      link.click()
    }
  }
})
</script>
