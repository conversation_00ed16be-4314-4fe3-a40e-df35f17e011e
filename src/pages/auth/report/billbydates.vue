<template>
    <div>
        <adminheader
            :title="$t('report.billbydate')" :backButton="true" :backTitle="'Back to reports'" :backFunction="back"></adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <div class="place-content-center mt-10 flex items-center">
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{$t('report.startdate')}}</div>
                            <DatePicker
                                v-model="startdate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{$t('report.enddate')}}</div>
                            <DatePicker
                                v-model="enddate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="w-1/6 mt-2">
                            <button @click="generateReport" class="mx-2 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{$t('report.generate')}}</button>
                        </div>    
                        <div class="w-1/6 mt-2">
                            <button @click="exportReport" class="mx-2 p-2 rounded bg-green-200 text-sm px-5 cursor-pointer hover:bg-green-300">Export</button>
                        </div>
                    </div>
    
                    <div v-if="reportdata != undefined && !loading">
                        <!-- Summary Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="summary-card bg-white p-4 rounded-lg shadow">
                                <h3 class="text-sm font-medium text-gray-500">Total Bills</h3>
                                <p class="text-2xl font-bold text-gray-800">{{ reportdata.billCount }}</p>
                            </div>
                            
                            <div class="summary-card bg-white p-4 rounded-lg shadow">
                                <h3 class="text-sm font-medium text-gray-500">Unpaid Bills</h3>
                                <p class="text-2xl font-bold text-blue-600">{{ reportdata.unpaidBillsCount }}</p>
                            </div>
                            
                            <div class="summary-card bg-white p-4 rounded-lg shadow">
                                <h3 class="text-sm font-medium text-gray-500">Total Current Amount</h3>
                                <p class="text-2xl font-bold text-gray-800">RM {{ formatmoney(reportdata.totalCurrentAmount) }}</p>
                            </div>
                        </div>

                        <!-- Financial Overview -->
                        <div class="bg-white rounded-lg shadow p-4 mb-6">
                            <h2 class="text-lg font-semibold text-gray-700 mb-4">Financial Overview</h2>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="p-3 bg-gray-50 rounded-md">
                                <p class="text-sm text-gray-500">Brought Forward</p>
                                <p class="text-xl font-bold text-gray-800">RM {{ formatmoney(reportdata.totalBf) }}</p>
                                </div>
                                <div class="p-3 bg-gray-50 rounded-md">
                                <p class="text-sm text-gray-500">Current Amount</p>
                                <p class="text-xl font-bold text-blue-600">RM {{ formatmoney(reportdata.totalCurrentAmount) }}</p>
                                </div>
                                <div class="p-3 bg-gray-50 rounded-md">
                                <p class="text-sm text-gray-500">Total Paid</p>
                                <p class="text-xl font-bold text-blue-600">RM {{ formatmoney(reportdata.totalPaid) }}</p>
                                </div>
                                <div class="p-3 bg-gray-50 rounded-md">
                                <p class="text-sm text-gray-500">Balance</p>
                                <p class="text-xl font-bold text-red-600">RM {{ formatmoney(reportdata.totalBf + reportdata.totalCurrentAmount - reportdata.totalPaid) }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="flex justify-between items-center p-4 border-b">
                                <h2 class="text-lg font-semibold text-gray-700">Unpaid Bills</h2>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Bill No
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Customer
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Current Amount
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total Amount
                                    </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="bill in reportdata.unpaidBills" :key="bill._id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-black font-semibold">
                                        {{ bill.billno }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ formatdate(bill.billdate) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ bill.customerName }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        RM {{ formatmoney(bill.amountcurrent) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        RM {{ formatmoney(bill.totalamount) }}
                                    </td>
                                    </tr>
                                    <tr v-if="reportdata.unpaidBills.length === 0">
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No unpaid bills found matching your search
                                        </td>
                                    </tr>
                                </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="loading">
                        <div class="text-center p-10 font-semibold animate-pulse">Loading report...</div>
                        <div class="text-center p-10">Progress: {{ progress }}%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillSummary, getUserById } from '../../../api'
import loading from '../../../components/cvui/loading.vue'
export default defineComponent({
    setup () {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
        loading
    },
    data() {
        let startdate: any = ''
        let enddate: any = ''
        let reportdata: any = undefined
        let subs: any = {}
        let agents: any = {}
        let plans: any = {}
        let loading: any = false
        let progress: any = 0
        return {
            startdate,
            enddate,
            reportdata,
            subs,
            agents,
            plans,
            loading,
            progress
        }
    },
    methods: {
        count1 () {
            return (this.reportdata && this.reportdata.data && this.reportdata.data.length) || 0
        },
        back () {
            this.$router.back()
        },
        async generateReport () {
            this.loading = true
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate);
                let edate = moment(this.enddate);
                if (sdate.isAfter(edate)) {
                    crossStore.SetNotmsg({
                        title: 'Wrong date selection.',
                        msg: 'Start date is after end date.',
                        type: 'error',
                    })
                } else {
                    const res: any = await getBillSummary({
                        token: this.token,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate),
                    });

                    res.unpaidBills = await Promise.all(
                        res.unpaidBills.map(async (bill: any, index: number) => {
                            this.progress = Math.floor((index / res.unpaidBills.length) * 100);
                            const customerName = await this.getCustomer(bill.customer);
                            return {
                                ...bill,
                                customerName,
                            };
                        })
                    );

                    this.reportdata = res;
                }
            } else {
                crossStore.SetNotmsg({
                    title: 'Error.',
                    msg: 'Error. Please contact support.',
                    type: 'error',
                })
            }
            this.loading = false
        },
        async getCustomer (customerId: any) {
            const user = await getUserById({token: this.token, id: customerId}) || {} as any;
            return user.name || user.email || customerId;
        },
        exportReport () {
            const headers = ['Bill No', 'Date', 'Customer', 'Current Amount', 'Total Amount', 'Outstanding Balance'];

            const csvRows = this.reportdata.unpaidBills.map(bill => {
                return [
                bill.billno,
                this.formatdate(bill.billdate),
                bill.customerName,
                bill.amountcurrent,
                bill.totalamount,
                (bill.totalamount - bill.amountcurrent).toFixed(2)
                ].join(',');
            });

            const reportSummary = [
                ['Billing Report Summary'],
                [`Report Period: ${this.formatdate(this.reportdata.startdate)} to ${this.formatdate(this.reportdata.enddate)}`],
                [''],
                ['Summary Metrics'],
                [`Total Bills,${this.reportdata.billCount}`],
                [`Unpaid Bills,${this.reportdata.unpaidBillsCount}`],
                [`Total Current Amount,${this.formatmoney(this.reportdata.totalCurrentAmount)}`],
                [`Total Paid Amount,${this.formatmoney(this.reportdata.totalPaid)}`],
                [`Total Brought Forward,${this.formatmoney(this.reportdata.totalBf)}`],
                [`Outstanding Balance,${this.formatmoney(this.reportdata.totalBf + this.reportdata.totalCurrentAmount - this.reportdata.totalPaid)}`],
                [''],
                ['Unpaid Bills Detail']
            ];

            const csvContent = [
                ...reportSummary,
                headers.join(','),
                ...csvRows
            ].join('\n');

            let csvFile = new Blob([csvContent], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `billing_report_${this.formatdate(this.startdate)}_${this.formatdate(this.enddate)}.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        formatdate (p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        formatdate2 (p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney (p: any) {
            return p && p.toFixed(2)
        },
        copy (p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
    }
})
</script>
