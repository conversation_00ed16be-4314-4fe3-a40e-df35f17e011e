<template>
    <div>
        <adminheader
            :title="$t('navigations.activeuserreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="bg-white rounded border p-3">
              <div class="m-5">
                <button @click="generateReport" class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white">
                  {{$t('report.generate')}}
                </button>
                <button v-if="lists && lists.length > 0" @click="exportCSV" class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white mt-5">
                  {{$t('report.exportcsv')}}
                </button>
                
              </div>
              <div class="border-t">
                <div class="border-b py-2" v-for="(vp,vi) in lists" :key="`s_ss_${addvu(vi)}`">
                  <div class="grid grid-cols-5 gap-4">
                    <div>{{addvu(vi)}}. {{vp.address.building || 'The Grand Subang Jaya SS15'}} </div>
                    <div>{{vp.address.block}}-{{vp.address.level}}-{{vp.address.unit}}</div>
                    <div>{{vp.name}}</div>
                    <div>{{vp.contact}}</div>
                    <div>{{formatdate(vp.activationdate)}}</div>
                  </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    console.log(authStore)
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
  },
  data () {
    let lists: any = undefined
    return {
        lists,
    }
  },
  methods: {
    formatdate (v: any) {
      return moment(v).format('YYYY-MM-DD')
    },
    addvu (v: any) {
      return v + 1
    },
    generateReport () {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    exportCSV () {
        let csvRows: any = []
        for (var i = 0; i < this.lists.length; i++) {
            let s: any = this.lists[i]
            csvRows.push(`${i+1},${s.address.building || 'The Grand Subang Jaya SS15'},${s.address.block}-${s.address.level}-${s.address.unit},${s.name},${s.contact},${this.formatdate(s.activationdate)}`);
        }
                    
        var csvString = csvRows.join("\n");
        let csvFile = new Blob([csvString], { type: "text/csv" });
        let downloadLink = document.createElement("a");
        downloadLink.download = `userList.csv`
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
    },
    doGetReport () {
      let limit: number = 20
      getSubscriptions({
        token: this.token, skip: this.lists.length, limit: limit, statustxt: 'active'
      }).then((res: any) => {
        this.lists= this.lists.concat(res.data)
        if (res && res.total > this.lists.length) {
          setTimeout(this.doGetReport, 300)
        }
      })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
  }
})
</script>