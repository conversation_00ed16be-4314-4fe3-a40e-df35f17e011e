<template>
    <div>
        <adminheader :title="$t('report.ticketsummary')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back"
                class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{ $t('c.back') }}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5"></div>
                    <div class="p-6 bg-white rounded-lg shadow-md">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Ticket Tracking Dashboard</h2>
                        <p class="text-gray-600">Daily comparison of open vs. new tickets</p>
                    </div>
                    
                    <div class="flex flex-wrap items-center justify-end gap-4 mb-4">
                        <select 
                        v-model="dateRange" 
                        class="px-3 py-2 text-sm border rounded-md border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200"
                        >
                        <option :value="7">7 days</option>
                        <option :value="14">14 days</option>
                        <option :value="30">30 days</option>
                        </select>
                    </div>
                    
                    <div class="h-[400px]">
                        <div v-if="loading" class="flex items-center justify-center h-full">
                            <div class="text-center">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
                                <p class="mt-2 text-gray-600">Loading ticket data...</p>
                            </div>
                        </div>
                        <div v-else-if="!ticketData.length" class="flex items-center justify-center h-full">
                            <p class="text-gray-600">No ticket data available for the selected period.</p>
                        </div>
                        <apexchart
                        v-else
                        type="line"
                        height="100%"
                        :options="chartOptions"
                        :series="series"
                        ></apexchart>
                    </div>
                    
                    <div class="grid grid-cols-1 gap-4 mt-6 sm:grid-cols-3">
                        <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600">Average Open Tickets</p>
                        <p class="text-2xl font-bold text-gray-800">{{ averageOpen }}</p>
                        </div>
                        <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600">Average New Tickets</p>
                        <p class="text-2xl font-bold text-gray-800">{{ averageNew }}</p>
                        </div>
                        <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600">Ticket Growth Rate</p>
                        <p class="text-2xl font-bold" :class="growthRate >= 0 ? 'text-emerald-600' : 'text-red-600'">
                            {{ growthRate >= 0 ? '+' : '' }}{{ growthRate }}%
                        </p>
                        </div>
                    </div>
                    </div>
                </div>  
            </div>
        </div>

  </template>
  
  <script setup>
  import { ref, computed, inject, onMounted, watch } from 'vue';
  import VueApexCharts from 'vue3-apexcharts';
  import adminheader from '@/components/AdminHeader.vue'
  import { getTicketSummary } from '../../../api'
  import moment from 'moment'
  import { useRouter } from 'vue-router'

  const apexchart = VueApexCharts;
  const router = useRouter()
  const authStore = inject("authStore")
  const authState = authStore.getState()
  const token = computed(() => authState.token);
  const limit = ref(50);
  const dateRange = ref(14)
  
  const ticketData = ref([]);
  const loading = ref(false);
  
  const back = () => {
    router.push('/auth/report')
  }

  const generateData = () => {
    loading.value = true;
    getTicketSummary({
      token: token.value,
      limit: limit.value,
      startdate: moment().subtract(dateRange.value, 'days').format('YYYY-MM-DD'),
      enddate: moment().format('YYYY-MM-DD')
    }).then((response) => {
      if (response && response.data) {
        ticketData.value = response.data;
      }
      loading.value = false;
    }).catch(error => {
      console.error('Error fetching ticket data:', error);
      loading.value = false;
    })
  }

  watch(dateRange, (newValue) => {
    generateData();
  });

  onMounted(() => {
    generateData()
  })
  
  const filteredData = computed(() => {
    if (!ticketData.value || ticketData.value.length === 0) {
      return [];
    }
    
    const sortedData = [...ticketData.value].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    return sortedData.slice(-Math.min(dateRange.value, sortedData.length));
  });
  
  const dates = computed(() => filteredData.value.map(item => moment(item.timestamp).format('YYYY-MM-DD')));
  
  const openTickets = computed(() => filteredData.value.map(item => item.data.open || 0));
  const newTickets = computed(() => filteredData.value.map(item => item.data.new || 0));
  const closedTickets = computed(() => filteredData.value.map(item => item.data.close || 0));
  
  const averageOpen = computed(() => {
    if (openTickets.value.length === 0) return 0;
    const sum = openTickets.value.reduce((acc, val) => acc + val, 0);
    return Math.round(sum / openTickets.value.length);
  });
  
  const averageNew = computed(() => {
    if (newTickets.value.length === 0) return 0;
    const sum = newTickets.value.reduce((acc, val) => acc + val, 0);
    return Math.round(sum / newTickets.value.length);
  });
  
  const growthRate = computed(() => {
    if (filteredData.value.length < 2) return 0;
    
    const firstWeekData = filteredData.value.slice(0, Math.min(7, Math.floor(filteredData.value.length / 2)));
    const lastWeekData = filteredData.value.slice(-Math.min(7, Math.ceil(filteredData.value.length / 2)));
    
    const firstWeekTotal = firstWeekData.reduce((acc, item) => acc + (item.data.new || 0), 0);
    const lastWeekTotal = lastWeekData.reduce((acc, item) => acc + (item.data.new || 0), 0);
    
    if (firstWeekTotal === 0) return 0;
    
    return Math.round(((lastWeekTotal - firstWeekTotal) / firstWeekTotal) * 100);
  });
  
  const series = computed(() => [
    {
      name: 'Open Tickets',
      data: openTickets.value
    },
    {
      name: 'New Tickets',
      data: newTickets.value
    },
    {
      name: 'Closed Tickets',
      data: closedTickets.value
    }
  ]);
  
  const chartOptions = computed(() => ({
    chart: {
      type: 'line',
      toolbar: {
        show: true,
        tools: {
          download: false,
          selection: false,
          zoom: false,
          zoomin: false,
          zoomout: false,
          pan: false,
          reset: false
        }
      },
      fontFamily: 'Inter, sans-serif',
    },
    colors: ['#10b981', '#8b5cf6', '#f87171'],
    stroke: {
      width: 3,
      curve: 'smooth'
    },
    xaxis: {
      categories: dates.value,
      labels: {
        style: {
          fontSize: '12px'
        },
        formatter: function(value) {
          return moment(value).format('MMM DD');
        }
      }
    },
    yaxis: {
      title: {
        text: 'Number of Tickets'
      },
      labels: {
        formatter: function(value) {
          return Math.round(value);
        }
      }
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      fontSize: '14px',
      markers: {
        radius: 12
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function(value) {
          return value + ' tickets';
        }
      }
    },
    grid: {
      borderColor: '#f1f1f1',
      row: {
        colors: ['transparent', 'transparent'],
        opacity: 0.5
      }
    },
    markers: {
      size: 5
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: '300px'
          },
          legend: {
            position: 'bottom',
            horizontalAlign: 'center'
          }
        }
      }
    ]
  }));
  </script>