<template>
  <div>
    <adminheader :title="$t('report.latestbillreport')"></adminheader>
    <div class="mx-2 my-2 flex justify-between">
      <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{ $t('c.back')
        }}</button>
      <select v-model="statustxt" multiple
        class="text-sm placeholder-gray-500 border rounded bg-white p-2 focus:ring-blue-500">
        <option value="new">New</option>
        <option value="pendingdeposit">Pending Deposit</option>
        <option value="pendinginstall">Pending Installation</option>
        <option value="pendingactivation">Pending Activation</option>
        <option value="active">Active</option>
        <option value="suspended">Suspended</option>
        <option value="cancelled">Cancelled</option>
        <option value="terminated">Terminated</option>
        <option value="migration">Migration</option>
        <option value="terminated">Return Order</option>
        <option value="migration">Relocation</option>
      </select>
    </div>
    <div class="mx-1 my-2 mr-2">
      <div class="bg-white rounded border p-3">
        <div class="m-5">
          <button @click="generateReport" class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white">
            {{ $t('report.generate') }}
          </button>
          <button v-if="lists && lists.length > 0 && !loading" @click="exportCSV"
            class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white mt-5">
            {{ $t('report.exportcsv') }}
          </button>

        </div>
        <div v-if="!loading" class="border-t">
          <div class="border-b py-2">
            <div class="grid grid-cols-12 gap-4 font-bold">
              <div>#</div>
              <div class="col-span-2">Address</div>
              <div>Name</div>
              <div>Contact</div>
              <div>Activation Date</div>
              <div>Bill Date</div>
              <div>Price</div>
              <div>Current Amount</div>
              <div>Total Amount</div>
              <div>Amount Paid</div>
              <div>Status</div>
            </div>
          </div>
          <div class="border-b py-2">
            <div class="grid grid-cols-12 gap-4 font-bold">
              <div>Total Summary</div>
              <div class="col-span-7">MYR-RM</div>
              <div>{{ formatMoney(totalCurrentAmount) }}</div>
              <div>{{ formatMoney(totalAmount) }}</div>
              <div class="col-span-2">{{ formatMoney(totalAmountPaid) }}</div>
            </div>
          </div>
          <div v-if="lists && lists.length > 0" class="border-b py-2 text-xs hover:bg-gray-200" v-for="(vp, vi) in lists" :key="`s_ss_${addvu(vi)}`">
            <div class="grid grid-cols-12 gap-4">
              <div>{{ addvu(vi) }}</div>
              <div>{{ vp.address.building || 'The Grand Subang Jaya SS15' }} </div>
              <div>{{ vp.address.block }}-{{ vp.address.level }}-{{ vp.address.unit }}</div>
              <div>{{ vp.name }}</div>
              <div>{{ vp.contact }}</div>
              <div>{{ formatdate(vp.activationdate) }}</div>
              <div>{{ formatdate(vp.billdate) }}</div>
              <div>{{ formatMoney(vp.price) }}</div>
              <div>{{ formatMoney(vp.amountcurrent) }}</div>
              <div>{{ formatMoney(vp.totalamount) }}</div>
              <div>{{ formatMoney(vp.amountpaid) }}</div>
              <div>{{ vp.totalamount == vp.amountpaid || vp.amountpaid >= vp.totalamount ? 'Paid' : 'Unpaid' }}</div>
            </div>
          </div>
          <div v-else class="text-center py-2 border-b">
            <div>No Records Found</div>
          </div>
        </div>
        <div v-else>
          <dloading />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions } from '../../../api'
import { getBillings, getPlan } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()

    return {
      token: computed(() => authState.token),
      authStore: authStore,
      authState: authState,
    }
  },
  computed: {
    totalCurrentAmount () {
      // sum all amountcurrent in the list
      const totalAmountCurrent = (this.lists &&this.lists.reduce((acc: Number, vp:any) => acc + vp.amountcurrent, 0))|| 0;
      return totalAmountCurrent;
    },
    totalAmount () {
      const totalamount = (this.lists &&this.lists.reduce((acc: Number, vp:any) => acc + vp.totalamount, 0))|| 0;
      return totalamount;
    },
    totalAmountPaid () {
      const amountpaid = (this.lists &&this.lists.reduce((acc: Number, vp:any) => acc + vp.amountpaid, 0))|| 0;
      return amountpaid;
    }
  },
  components: {
    adminheader,
    svgCollection,
    dloading
  },
  data() {
    let lists: any = undefined
    let loading = false
    let statustxt = ['active','suspended']
    let plans: any = {}
    return {
      lists,
      loading,
      statustxt,
      plans,
    }
  },
  methods: {
    formatdate(v: any) {
      return moment(v).format('YYYY-MM-DD')
    },
    addvu(v: any) {
      return v + 1
    },
    async generateReport() {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    exportCSV() {
      let csvRows: any = []
      csvRows.push(`#,Address 1,Address 2,Name,Contact,Activation Date,Price,Current Amount,Total Amount,Amount Paid,Status`);
      for (var i = 0; i < this.lists.length; i++) {
        let s: any = this.lists[i]
        csvRows.push(`${i + 1},"${s.address.building || 'The Grand Subang Jaya SS15'}","${s.address.block}-${s.address.level}-${s.address.unit}","${s.name}","${s.contact}",${this.formatdate(s.activationdate)},${this.formatMoney(s.price)},${this.formatMoney(s.amountcurrent)},${this.formatMoney(s.totalamount)},${this.formatMoney(s.amountpaid)},${s.totalamount == s.amountpaid || s.amountpaid >= s.totalamount ? 'Paid' : 'Unpaid'}`);
      }

      var csvString = csvRows.join("\n");
      let csvFile = new Blob([csvString], { type: "text/csv" });
      let downloadLink = document.createElement("a");
      downloadLink.download = `latest_user_bill.csv`
      downloadLink.href = window.URL.createObjectURL(csvFile);
      downloadLink.style.display = "none";
      document.body.appendChild(downloadLink);
      downloadLink.click();
    },
    async doGetReport() {
      let limit = 20;
      this.loading = true;
      try {
        const fetchSubscriptions = async () => {
          const res:any = await getSubscriptions({
            token: this.token,
            skip: this.lists && this.lists.length || 0,
            limit: limit,
            statustxts: this.statustxt.join(',')
          });

          if (res && res.data) {
            this.lists = this.lists.concat(res.data);

            if (res.total > this.lists.length) {
              await fetchSubscriptions();
            }
          }
        };

        await fetchSubscriptions();

        await this.fetchBillsForSubscriptions();
        await this.fetchPlanForSubscriptions();
      } catch (error) {
        console.error('Error fetching subscriptions:', error);
      } finally {
        this.loading = false;
      }
    },
    async fetchBillsForSubscriptions() {
      try {
        const billRequests = this.lists.map(async (subscription: any) => {
          const response = await getBillings({
            token: this.token,
            limit: 1,
            customer: subscription.customer,
            latestbillfirst: 'true'
          })
          const bill = response.data?.[0];
          subscription.totalamount = bill?.totalamount ?? 0;
          subscription.amountpaid = bill?.amountpaid ?? 0;
          subscription.billdate = bill?.billdate ?? '';
          subscription.amountcurrent = bill?.amountcurrent ?? 0;
        });
        await Promise.all(billRequests);
      } catch (error) {
        console.error('Error fetching bills:', error);
      }
    },
    async fetchPlanForSubscriptions() {
      try {
        const planRequests = this.lists.map(async (subscription: any) => {
          if (!subscription.price) {
            if (this.plans[subscription.plan]) {
              subscription.price = this.plans[subscription.plan]?.price ?? 0;
            } else {
              const response = await getPlan({
                token: this.token,
                limit: 1,
                id: subscription.plan,
              })
              const plan: any = response.data;
              this.plans[plan.id] = plan;
              subscription.price = plan?.price ?? 0;
            }
            
          }
          
        });
        await Promise.all(planRequests);
      } catch (error) {
        console.error('Error fetching plans:', error);
      }
    },
    back() {
      this.$router.back()
    },
    formatDate(p: any) {
      return (p && moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    formatMoney(p: any) {
      if (Number.isNaN(p) || p == undefined) {
        return '0.00*'
      } else {
        return p.toFixed(2)
      }
    },
    copy(p: any) {
      crossStore.SetNotmsg({
        title: this.$t('c.textcopied'),
        msg: p,
        type: 'success',
      })
      navigator.clipboard.writeText(p)
    },
  }
})
</script>