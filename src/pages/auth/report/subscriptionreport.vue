<template>
    <div>
        <adminheader
            :title="$t('navigations.subscriptionsreport')" :backButton="true" :backTitle="'Back to reports'" :backFunction="back"></adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="bg-white rounded border p-4 py-4">
              <div class="flex items-end gap-2">
                <div class="w-1/2 flex items-end gap-4">
                  <div class="w-1/2">
                    <span class="text-xs">Start Date</span>
                    <DatePicker
                        v-model="startdate"
                        :clearable="true"
                        defaultColor="blue"></DatePicker>
                  </div>
                  <div class="w-1/2">
                    <span class="text-xs">End Date</span>
                      <DatePicker
                      v-model="enddate"
                      :clearable="true"
                      defaultColor="blue"></DatePicker>
                  </div>
                </div>
                <div class="w-1/2 flex gap-2 items-end">
                  <button @click="generateReport" :class="loading ? 'cursor-not-allowed bg-gray-100 text-gray-500' : 'bg-green-500 hover:bg-green-700 text-white'" 
                    class="w-1/2 rounded px-4 py-2" :disabled="loading">
                    {{$t('report.generate')}}
                  </button>
                  <button v-if="lists && lists.length > 0" @click="exportXLSX" :class="loading ? 'cursor-not-allowed bg-gray-100 text-gray-500' : 'bg-blue-500 hover:bg-blue-700 text-white'"
                    class="w-1/2 rounded px-4 py-2" :disabled="loading">
                    Export
                  </button>
                </div>
              </div>

              <div class="border-t border-b py-2 mt-5">
                <div class="grid grid-cols-9 gap-4 text-xs font-bold">
                  <div>Building</div>
                  <div>Name</div>
                  <div>SubscribeDate</div>
                  <div class="truncate">ContractMonths</div>
                  <div>Plan</div>
                  <div>FreeUsage</div>
                  <div>Price</div>
                  <div>Agent</div>
                  <div>Status</div>
                </div>
              </div>
              <div v-if="loading">
                <div class="text-center p-10 font-semibold animate-pulse">Loading report...</div>
                <div class="text-center p-10">Progress: {{progress}}%</div>
              </div>
              <div v-else>
                <div v-if="lists && lists.length > 0" class="border-b py-2" v-for="(vp,vi) in lists" :key="`s_ss_${addvu(vi)}`">
                  <div class="grid grid-cols-9 gap-4 text-xs">
                    <!-- <div>{{addvu(vi)}}. {{vp.address.building || 'The Grand Subang Jaya SS15'}} </div> -->
                    <!-- <div>{{vp.address.block}}-{{vp.address.level}}-{{vp.address.unit}}</div> -->
                    <div>{{strnum(vi)}}{{vp.address.building || 'The Grand Subang Jaya SS15'}}</div>
                    <div>{{vp.name}}</div>
                    <!-- <div>{{vp.contact}}</div> -->
                    <div>{{formatdate(vp.subscribedate)}}</div>
                    <div>{{ vp.contractmonths }}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).title) || '---'}}</div>
                    <div>{{vp.freeusage}}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).price.toFixed(2)) || '--.--' }}</div>
                    <div>{{vp.agent && agents[vp.agent]&& agents[vp.agent].name}}</div>
                    <div>{{vp.statustxt}}</div>
                  </div>
                </div>
                <div v-else>
                  <div class="text-center py-2">No records found</div>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getPlan, getUserName } from '../../../api'
import * as XLSX from 'xlsx'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
    DatePicker,
  },
  data () {
    let lists: any = undefined
    let plans: any = {}
    let planids: any = []
    let agentsid: any = []
    let agents: any = {}
    let activeonly: boolean = false
    let startdate: any = ''
    let enddate: any = ''
    let loading: boolean = false
    let progress: number = 0
    return {
        lists,
        plans,
        planids,
        agentsid,
        agents,
        activeonly,
        startdate,
        enddate,
        loading,
        progress
    }
  },
  methods: {
    getAgent (s: string) {
        if (!this.agents[s] && s.trim().length > 0) {
            getUserName({token: this.token, id: s}).then((res: any) => {
                if (res && res.id) {
                    this.agents[res.id] = res
                }
            })
        }
    },
    strnum (p: any) {
      return (p + 1).toString() + '.  '
    },
    getPlans () {
      for (var k = 0; k < this.planids.length; k++ ) {
        if (!this.plans[this.planids[k]]) {
          getPlan({token: this.token, id: this.planids[k]}).then((p: any) => {
            this.plans[p.data.id] = p.data
          })
        }   
      }
    },
    getAgents () {
      for (var k = 0; k < this.agentsid.length; k++ ) {
        if (!this.agents[this.agentsid[k]]) {
          this.getAgent(this.agentsid[k])
          // getPlan({token: this.token, id: this.agentsid[k]}).then((p: any) => {
          //   this.plans[p.data.id] = p.data
          // })
        }   
      }
    },
    formatdate (v: any) {
      if (v) {
        return moment(v).format('YYYY-MM-DD')
      } else {
        return '----'
      }
      
    },
    packagePlan (p: any) {
      return this.plans[p] ? this.plans[p] : null
    },
    addvu (v: any) {
      return v + 1
    },
    generateReport () {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    escapeQuotes(str: string) {
      if (!str) return ''
      return str.replace(/\\/g, '\\\\')  // First escape any existing backslashes
          .replace(/'/g, '\\\'')   // Then escape single quotes
          .replace(/"/g, '\\"') // Finally escape double quotes
          .replace(/\n/g, '   ');   // new line replacement
    },
    async exportXLSX() {
        this.loading = true;

        const headers = [
            'No',
            'Building',
            'Unit',
            'Address',
            'Username',
            'Name',
            'Subscription ID',
            'Subscribe Date',
            'Contract Months',
            'Activation Date',
            'Plan',
            'Free Usage',
            'Price',
            'Agent',
            'Deposit',
            'Advanced Payment',
            'Status'
        ];

        const dataRows = this.lists.map((s: any, i: number) => {
            return [
                i + 1,
                this.escapeQuotes(s.address.building) || 'The Grand Subang Jaya SS15',
                this.escapeQuotes(s.address.unit),
                this.escapeQuotes(s.address.address),
                s.username,
                s.name,
                s.sid || '----',
                this.formatdate(s.subscribedate),
                s.contractmonths,
                this.formatdate(s.activationdate),
                (this.plans[s.plan] && this.plans[s.plan].title) || '----',
                s.freeusage || 0,
                s.price || (this.plans[s.plan] && this.plans[s.plan].price) || 0,
                s.agent && this.agents[s.agent] && this.agents[s.agent].name,
                s.deposit,
                s.advancedpayment,
                s.statustxt
            ];
        });

        const worksheetData = [headers, ...dataRows];
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Subscription List');

        const wbout = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([wbout], { type: 'application/octet-stream' });

        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = 'subscription_list.xlsx';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        this.loading = false;
    },
    doGetReport () {
      this.loading = true
      let limit: number = 20
      let v1: any = {
        token: this.token, skip: this.lists.length, limit: limit,// statustxt: 'active'
        params: [],
      }

      if (this.startdate) {
        v1["subscribedatestart"] = moment(this.startdate).format('YYYY-MM-DD')        
        v1["params"].push('subscribedatestart')
      }

      if (this.enddate) {
        v1["subscribedateend"] = moment(this.enddate).format('YYYY-MM-DD')        
        v1["params"].push('subscribedateend')
      }

      // if (this.activeonly) {
      //   v1  = {
      //     token: this.token, skip: this.lists.length, limit: limit, statustxt: 'active'
      //   }
      // }
      getSubscriptions(v1).then((res: any) => {
        this.lists= this.lists.concat(res.data)
        for (var k = 0; k < res.data.length; k++ ) {
          this.planids.push(res.data[k].plan)
          this.agentsid.push(res.data[k].agent)
        }
        this.planids = [...new Set(this.planids)]
        this.agentsid = [...new Set(this.agentsid)]
        this.getPlans()
        this.getAgents()
        if (res && res.total > this.lists.length) {
          setTimeout(this.doGetReport, 300)
          this.progress = Math.floor((this.lists.length / res.total) * 100)
        } else {
          this.loading = false
        }
      })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
  }
})
</script>