<template>
    <div>
        <adminheader
            :title="$t('report.jnxreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <svgCollection icon="report" dclass="inline-block w-5 h-5" />
                    {{$t('report.filterbybilldate')}}
                    <div class="place-content-center mt-10">
                        <div class="mr-5 inline-block w-full lg:w-1/3">
                            <div class="text-xs my-1">{{$t('report.startdate')}}</div>
                            <DatePicker
                                v-model="startdate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 inline-block w-full lg:w-1/3 ">
                            <div class="text-xs my-1">{{$t('report.enddate')}}</div>
                            <DatePicker
                                v-model="enddate"
                                :clearable="true"
                                defaultColor="blue"></DatePicker>
                        </div>
                        <div class="inline-block w-full lg:w-1/5 mt-2">
                            <button @click="generateReport" class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{$t('report.generate')}}</button>
                        </div>                        
                    </div>
                    <div v-if="reportdata != undefined">
                        <div class="text-center p-10 text-sm" v-if="!reportdata.data || reportdata.count == 0">{{$t('report.emptylist')}}</div>
                        <div v-else class="mt-5">
                            <div class="text-right">
                                <button @click="exportReport" class="rounded shadow px-5 py-1 hover:bg-gray-300 text-sm">{{$t('report.exportcsv')}}</button>
                            </div>
                            <div class="text-sm py-1 border-b">
                                <div class="inline-block w-1/6">{{$t('report.billno')}}</div>
                                <div class="inline-block w-1/6">{{$t('report.billdate')}}</div>
                                <div class="inline-block w-2/6">{{$t('report.name')}}/{{$t('report.contact')}}</div>
                                <div class="inline-block w-2/6">{{$t('report.totalamount')}}</div>
                            </div>
                             <div class="text-sm py-1 hover:bg-gray-100 border-b" v-for="(l, li) in reportdata.data" :key="l._id">
                                <div @click="copy(l._id)" class="inline-block w-1/6 text-xs cursor-pointer pl-2 hover:text-indigo-600">{{String(Number(li) + 1)}}. {{l.billno}}</div>
                                <div class="inline-block w-1/6">{{formatdate(l.billdate2)}}</div>
                                <div class="inline-block w-2/6 text-xs"><div @click="copy(l.customeritem[0].name)" class="inline-block cursor-pointer hover:text-indigo-600">{{l.customeritem[0].name}}</div> / <div @click="copy(l.customeritem[0].contact)" class="inline-block cursor-pointer hover:text-indigo-600">{{l.customeritem[0].contact}}</div></div>
                                <div class="inline-block w-2/6 text-xs">{{formatmoney(l.totalamount)}}</div>
                            </div>
                            <div class="border-t mt-2 text-left p-2 font-bold">{{$t('report.total')}}</div>
                            <div class="text-right">
                                <template :key="'bl_'+i" v-for="(b,i) in buildinglist">
                                    <div class="border-b mt-10">{{b.title}}</div>
                                    <div class="" :key="String(ci)" v-for="(cc,ci) in b.charges">
                                        <div class="w-24 inline-block">{{cc[0]}}</div>
                                        <div class="px-5 inline-block">{{formatmoney(cc[1])}} x {{buildingCount(b.key)}} = </div>
                                        <div class="inline-block">{{formatmoney(cc[1] * buildingCount(b.key))}}</div>
                                    </div>
                                    <div>
                                        <p>Free usage 0 x {{ buildingWithZero(b.key) }} = 0</p>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillingWtBuildingReport, getBuildings } from '../../../api'
import config from '../../../config'
export default defineComponent({
    setup () {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
    },
    data() {
        let startdate: any = ''
        let enddate: any = ''
        let reportdata: any = undefined
        let building: any = ''
        let buildinglist: any = []
        let charges: any = config.buildingcharges
        return {
            startdate,
            enddate,
            reportdata,
            building,
            buildinglist,
            charges
        }
    },
    methods: {
        count1 () {
            return (this.reportdata && this.reportdata.data && this.reportdata.data.length) || 0
        },
        buildingCount (pp: string) {
            var k: any = 0
            if (this.reportdata && this.reportdata.data) {
                for (var i = 0; i < this.reportdata.data.length; i++) {
                    let p: any = this.reportdata.data[i]
                    if (p.building == pp && p.amountcurrent > 0) {
                        k++
                    } else if (!p.building || p.building == '') {
                        if (pp == 'The Grand Subang Jaya SS15') {
                            k++
                        }
                    }
                }
            }
            
            return k
        },
        buildingWithZero (pp: string) {
            var k: any = 0
            if (this.reportdata && this.reportdata.data) {
                for (var i = 0; i < this.reportdata.data.length; i++) {
                    let p: any = this.reportdata.data[i]
                    if (p.building == pp && (p.amountcurrent == 0 || p.amountcurrent < 0)) {
                        k++
                    } else if (!p.building || p.building == '') {
                        if (pp == 'The Grand Subang Jaya SS15') {
                            k++
                        }
                    }
                }
            }
            
            return k
        },
        back () {
            this.$router.back()
        },
        generateReport () {
            if (this.startdate && this.enddate) {
                let sdate = moment(this.startdate)
                let edate = moment(this.enddate)
                if (sdate.isAfter(edate)) {
                    // #TODO
                } else {
                    getBillingWtBuildingReport({
                        token: this.token,
                        startdate: this.formatdate2(sdate),
                        enddate: this.formatdate2(edate),
                    }).then((res: any) => {
                        this.reportdata = res
                    })
                }
            } else {
                // #TODO
            }
        },
        exportReport () {
            var Head: any = [[this.$t('report.billno'), this.$t('report.billdate'), this.$t('report.name'), this.$t('report.contact'), this.$t('report.totalamount'), this.$t('report.building')]];
            let row: any = this.reportdata.data
            let sdate = moment(this.startdate)
            let edate = moment(this.enddate)
            if (row) {
                for (var item = 0; item < row.length; ++item) {
                    let l = row[item]
                    let bd = moment(l.billdate2).format('YYYY-MM-DD')
                    let bcontact: any = l.customeritem[0].contact
                    let bname: any = l.customeritem[0].name
                    let totalamount: any = l.totalamount
                    let building: any = l.building || 'The Grand Subang Jaya SS15'

                    Head.push([
                        row[item].billno,
                        bd,
                        bname,
                        bcontact,
                        totalamount,
                        building,
                    ]);
                }
            }
            

            var csvRows = [];
            for (var cell = 0; cell < Head.length; ++cell) {
                csvRows.push(Head[cell].join(','));
            }
                        
            var csvString = csvRows.join("\n");
            let csvFile = new Blob([csvString], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `jnxreport_${sdate.format('YYYY-MM-DD')}_${edate.format('YYYY-MM-DD')}.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        loadBuilding(pg: number, pageSize: number, keywords: string) {
            const fetchData = (skip: number, limit: number) => {
                getBuildings({ token: this.token, skip, limit, keywords }).then((res: any) => {
                if (res.data) {            
                    this.buildinglist = this.buildinglist.concat(res.data);
                    this.buildinglist = this.removeDuplicates(this.buildinglist);

                    const totalRetrieved = this.buildinglist.length;
                    const totalAvailable = res.total;

                    if (totalRetrieved < totalAvailable) {
                    fetchData(totalRetrieved, limit);
                    }
                }
                });
            };

            const initialSkip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0;
            fetchData(initialSkip, pageSize);
        },
        removeDuplicates(array: any) {
            let uniqueObjects: any = [];
            let ids = new Set();
            array.forEach((obj: any) => {
                if (!ids.has(obj.id)) {
                ids.add(obj.id);
                uniqueObjects.push(obj);
                }
            });
            return uniqueObjects;
        },
        formatdate (p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        formatdate2 (p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney (p: any) {
            return p && p.toFixed(2)
        },
        copy (p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
    },
    mounted() {
        this.loadBuilding(1, 10 ,'')
    },
})
</script>
