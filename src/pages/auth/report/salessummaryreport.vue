<template>
    <div>
        <adminheader :title="$t('report.salessummaryreport')" :backButton="true" :backTitle="'Back to reports'"
            :backFunction="back" />
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <div class="place-content-center mt-10 flex items-center">
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{ $t('report.startdate') }}</div>
                            <DatePicker v-model="startdate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="mr-5 w-full">
                            <div class="text-xs my-1">{{ $t('report.enddate') }}</div>
                            <DatePicker v-model="enddate" :clearable="true" defaultColor="blue"></DatePicker>
                        </div>
                        <div class="w-1/6 mt-2">
                            <button @click="generateReport"
                                class="mx-2 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white"
                                :disabled="loading">{{
                                $t('report.generate') }}</button>
                        </div>
                        <div class="w-1/6 mt-2">
                            <button v-if="reportdata && !loading" @click="exportReport"
                                class="mx-2 p-2 rounded bg-green-200 text-sm px-5 cursor-pointer hover:bg-green-300">Export</button>
                        </div>
                    </div>

                    <div v-if="reportdata">
                        <div v-if="!reportdata.data || reportdata.count === 0" class="text-center p-10 text-sm">
                            {{ $t('report.emptylist') }}
                        </div>
                        <div v-else class="mt-5">
                            <!-- Loading State -->
                            <div v-if="loading" class="flex items-center justify-center py-12">
                                <div class="text-center max-w-md">
                                    <h3 class="text-lg font-medium text-slate-900 mb-2">Loading Report Data</h3>
                                    <!-- Progress Bar -->
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                                        <div 
                                            class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                                            :style="{ width: loadingProgress.percentage + '%' }"
                                        ></div>
                                    </div>
                                    <!-- Progress Info -->
                                    <div class="space-y-1">
                                        <p class="text-slate-600 text-sm">
                                            Loading {{ loadingProgress.loaded }} of {{ loadingProgress.total }} records
                                        </p>
                                        <p class="text-blue-600 text-sm font-medium">
                                            {{ loadingProgress.percentage }}% Complete
                                        </p>
                                        <p class="text-slate-400 text-xs">
                                            {{ loadingProgress.total - loadingProgress.loaded }} remaining
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <template v-else>
                                <table class="w-full text-sm border-collapse border">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th>#</th>
                                            <th>Invoice No</th>
                                            <th>Subscriber Name</th>
                                            <th>Package</th>
                                            <th>Price</th>
                                            <th>Amount Current</th>
                                            <th>Amount Paid</th>
                                            <th>Amount BF</th>
                                            <th>Total Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(l, li) in reportdata.data" :key="l._id" class="hover:bg-gray-50">
                                            <td>{{ li + 1 }}</td>
                                            <td>{{ l.billno }}</td>
                                            <td>{{ l.customeritem[0].name }}</td>
                                            <td>{{ showSubscribePlan(l.items[0].subscription) }}</td>
                                            <td>{{ showSubscribePlanPrice(l.items[0].subscription) }}</td>
                                            <td>{{ formatmoney(l.amountcurrent) }}</td>
                                            <td>{{ formatmoney(l.amountpaid) }}</td>
                                            <td>{{ formatmoney(l.amountbf) }}</td>
                                            <td>{{ formatmoney(l.totalamount) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import loading from '@/components/cvui/loading.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillingReport, getSubscription, getPlan } from '../../../api'

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
        loading
    },
    data() {
        return {
            startdate: '',
            enddate: '',
            reportdata: undefined,
            subs: {},
            plans: {},
            loading: false,
            fetchedSubscriptions: new Set<string>(),
            loadingProgress: {
                loaded: 0,
                total: 0,
                percentage: 0
            }
        }
    },
    methods: {
        back() {
            this.$router.back()
        },
        showSubscribePlan(s: string) {
            return this.subs[s]?.plan ? this.showPlan(this.subs[s].plan) : 'No Plan'
        },
        showSubscribePlanPrice(s: string) {
            return this.subs[s]?.plan ? this.showPlanPrice(this.subs[s].plan) : 'No Plan'
        },
        showPlan(s: string) {
            return this.plans[s]?.title || 'No Plan'
        },
        showPlanPrice(s: string) {
            return this.plans[s]?.price || 'No Plan Price'
        },
        async getPlan(s: string) {
            if (!this.plans[s]) {
                const res = await getPlan({ token: this.token, id: s });
                if (res?.data) this.plans[res.data.id] = res.data
            }
        },
        async getSubscription(s: string) {
            if (this.fetchedSubscriptions.has(s)) return
            const res = await getSubscription({ token: this.token, id: s });
            if (res?.data?.id) {
                const subscriptionId = res.data.id
                this.subs[subscriptionId] = res.data
                this.fetchedSubscriptions.add(subscriptionId)
                if (res.data.plan) await this.getPlan(res.data.plan)
                // Update loading progress
                this.loadingProgress.loaded += 1
                this.loadingProgress.percentage = Math.round((this.loadingProgress.loaded / this.loadingProgress.total) * 100)
            }
        },
        async loadSubs() {
            this.loading = true
            this.loadingProgress = { loaded: 0, total: 0, percentage: 0 }
            
            const ids = [...new Set(this.reportdata.data.map(d => d.items[0]?.subscription).filter(Boolean))]
            this.loadingProgress.total = ids.length
            
            for (const batch of this.chunk(ids, 50)) {
                await Promise.all(batch.map(id => this.getSubscription(id)))
                await new Promise(r => setTimeout(r, 1000))
            }
            this.loading = false
        },
        async generateReport() {
            if (!this.startdate || !this.enddate) {
                this.notifyError('Start and end date are required.')
                return
            }
            const sdate = moment(this.startdate), edate = moment(this.enddate)
            const diff = edate.diff(sdate, 'days')
            if (diff > 61) {
                this.notifyError('Please select days no more than 61 days.')
                return
            }
            if (sdate.isAfter(edate)) {
                this.notifyError('Start date is after end date.')
                return
            }
            
            this.loading = true
            this.loadingProgress = { loaded: 0, total: 0, percentage: 0 }
            
            try {
                const res = await getBillingReport({
                    token: this.token,
                    startdate: this.formatdate2(sdate),
                    enddate: this.formatdate2(edate)
                })
                this.reportdata = res
                this.loadingProgress.total = res.data?.length || 0
                this.loadingProgress.loaded = res.data?.length || 0
                this.loadingProgress.percentage = 100
                
                if (res.data?.length) {
                    await this.loadSubs()
                }
            } finally {
                this.loading = false
            }
        },
        exportReport() {
            const header = [['Invoice No', 'Subscriber Name', 'Package', 'Price', 'Amount Current', 'Amount Paid', 'Amount BF', 'Total Amount']]
            const rows = this.reportdata.data.map(l => [
                l.billno,
                `"${l.customeritem[0].name.replace(/"/g, '\\"')}"`,
                this.showSubscribePlan(l.items[0].subscription),
                this.showSubscribePlanPrice(l.items[0].subscription),
                l.amountcurrent,
                l.amountpaid,
                l.amountbf,
                l.totalamount
            ])
            const csv = [...header, ...rows].map(r => r.join(',')).join('\n')
            const blob = new Blob([csv], { type: "text/csv" })
            const link = document.createElement("a")
            link.href = URL.createObjectURL(blob)
            link.download = `jnxreport_${moment(this.startdate).format('YYYY-MM-DD')}_${moment(this.enddate).format('YYYY-MM-DD')}.csv`
            link.click()
        },
        notifyError(msg: string) {
            crossStore.SetNotmsg({ title: 'Error', msg, type: 'error' })
        },
        formatdate2(p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney(p: any) {
            return p?.toFixed(2)
        },
        chunk(arr: any[], size: number) {
            return Array.from({ length: Math.ceil(arr.length / size) }, (_, i) => arr.slice(i * size, i * size + size))
        }
    }
})
</script>