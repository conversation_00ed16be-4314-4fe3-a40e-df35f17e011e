<template>
  <div>
    <adminheader :title="'Orphan AAA User List'" :backButton="true" :backTitle="'Back to reports'" :backFunction="back" />
    <div class="mx-1 my-2 mr-2">
      <div class="w-full rounded bg-white p-3 shadow">
        <div class="text-center mt-5">
          <div v-if="reportdata && !loading" class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created On</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(row, idx) in reportdata.data" :key="row._id || idx">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ idx + 1 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-black font-semibold">{{ row.username }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ row.createdby || '-' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatdate(row.createdon) }}</td>
                  </tr>
                  <tr v-if="reportdata.data && reportdata.data.length === 0">
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">No records found</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div v-else-if="loading" class="text-center p-10 font-semibold animate-pulse">Loading report...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import { crossStore } from '../../../store/cross-store'
import { getOrphanDMAUserList } from '../../../api'

export default defineComponent({
  setup() {
    const authStore: any = inject('authStore')
    const authState = authStore.getState()
    return {
      token: computed(() => authState.token),
    }
  },
  components: { adminheader },
  data() {
    return {
      reportdata: undefined as any,
      loading: false as boolean,
    }
  },
  methods: {
    back() {
      this.$router.back()
    },
    async generateReport() {
      try {
        this.loading = true
        const res: any = await getOrphanDMAUserList({ token: this.token })
        // Normalize to { data: [] }
        this.reportdata = res && res.data ? res : { data: res || [] }
      } catch (e) {
        crossStore.SetNotmsg({ title: 'Error', msg: 'Failed to load report', type: 'error' })
      } finally {
        this.loading = false
      }
    },
    formatdate(p: any) {
      return p ? moment(p).format('YYYY-MM-DD HH:mm') : '-'
    },
  },
  mounted() {
    this.generateReport()
  },
})
</script>

