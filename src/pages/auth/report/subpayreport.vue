<template>
    <div>
        <adminheader
            :title="$t('report.subpayreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
         <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <div @click="ggenerate" class="bg-gray-200 rounded text-sm p-2 hover:bg-blue-500 hover:text-white cursor-pointer">{{$t('report.generate')}}</div>
                </div>
                <div>
                    <div class="w-full text-center mt-5 bg-gray-500 text-white cursor-pointer" @click="exportCSV">ExportCSV</div>
                </div>
            </div>
            <div class="bg-white text-right">
                Generate Date
                <DatePicker
                    class="inline-block p-2 border rounded"
                    v-model="gendate"
                    :clearable="true"
                    defaultColor="blue"></DatePicker>
            </div>
            <div class="mt-3 mb-1 p-5 bg-white rounded" v-for="(k, ki) in subscriptions" :key="`k_${String(ki)}`">
                <div class="border-b">{{k.sid}} {{add1p(ki)}}. {{k.name}} - {{k.contact}} - {{k.address.building || '???'}} {{k.address.block}}-{{k.address.level}}-{{k.address.unit}} ==== {{ k.statustxt }}</div>
                <div v-for="(b,bi) in bills[k.id]" :key="`bi_${String(bi)}`">
                    {{formatDate(b.billdate)}} | 
                    {{b.billno}} | 
                    {{b.totalamount.toFixed(2)}} |
                    {{b.amountpaid.toFixed(2)}} 
                    <div @click="sendBillEmail(k, b)" class="inline-block ml-2 bg-gray-200 hover:bg-gray-400 hover:text-white rounded p-1 text-xs cursor-pointer">Send Email</div>
                </div>
                <div class="text-right">
                    <button class="bg-gray-300 hover:bg-blue-300 rounded p-1 text-xs" @click="genSub(k)">Generate Now</button>
                </div>
            </div>
        </div>

    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getSubscription, getBillings, generatebillspecific, sendBill  } from '../../../api'
export default defineComponent({
    setup(){
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        DatePicker,
    },
    methods: {
        sendBillEmail (h:any, p: any) {
            console.log(p)
            let sid: string = p.subscriptions[0]
            
            let g: any = {
                "item": JSON.parse(JSON.stringify(p)),
                "customer": {
                "email": h.email,
                "name": h.name,
                "title": h.title,
                "contact": h.contact,
                },
            }
            getSubscription({ token: this.token, id: sid }).then((res: any) => {
                let r = res && res.data
                g.item["deposit"] = r.deposit
                g["billingaddress"] = r.billingaddress && r.billingaddress.address
                g["sub"] = r
                if (r.address && r.address["block"]) {
                    g["customer"]["blockinfo"] = `${r.address.building||'The Grand Subang Jaya SS15'}  ${r.address.block}-${r.address.level}-${r.address.unit}`
                }            
                // this.sendbillC(g)
                sendBill({data: g, token: this.token})
            })
        },
        add1p (p: any) {
            return (p + 1).toString()
        },
        exportCSV () {
            let csvRows: any = []
            for (var i = 0; i < this.subscriptions.length; i++) {
                let s: any = this.subscriptions[i]
                csvRows.push(`${i+1},${s.name},${s.contact},${s.address.building || '???'} ${s.address.block}-${s.address.level}-${s.address.unit}`);
                for (var j = 0; j < this.bills[s.id].length; j++) {
                    let b: any = this.bills[s.id][j]
                    csvRows.push(`${this.formatDate(b.billdate)},${b.billno},${b.totalamount.toFixed(2)},${b.amountpaid.toFixed(2)}`)
                }
                csvRows.push('\n')
            }
                        
            var csvString = csvRows.join("\n");
            let csvFile = new Blob([csvString], { type: "text/csv" });
            let downloadLink = document.createElement("a");
            downloadLink.download = `billingSummary.csv`
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = "none";
            document.body.appendChild(downloadLink);
            downloadLink.click();
        },
        formatDate (d: any) {
            return moment(d).format('DD-MM-YYYY')
        },
        loadSubscriptions () {
            getSubscriptions(Object.assign(this.table, { token: this.token })).then((g: any) => {
                this.subscriptions = this.subscriptions.concat(g.data)
                if (g.total > (this.table.page * this.table.limit)) {
                    this.table.page ++
                    setTimeout(() => {
                        this.loadSubscriptions()
                    }, 300)
                } else {
                    this.loadBills()
                }
            })
        },
        genSub(s: any) {
            if (confirm('Are you sure to generate?')) {
                // generate bill
                generatebillspecific({token: this.token, id: s.id, date: moment(this.gendate).format('DD-MM-YYYY')}).then((res: any) => {
                    // alert('bill generated')
                    this.loadSubBills(s)
                })
            }
            
        },

        loadBills () {
            for (var i = 0; i < this.subscriptions.length; i++) {
                let s:any = this.subscriptions[i]
                // this.bills[s.id] = []
                // this.getBillsPage(s, 1)
                this.loadSubBills(s)                
            }
        },
        loadSubBills (s: any) {
            this.bills[s.id] = []
            this.getBillsPage(s, 1)
        },
        getBillsPage (s: any, page: number) {
            const limit = 10
            getBillings({ token: this.token, subscriptions: [s.id], skip: ((page - 1) * limit), limit}).then((res: any) => {
                if (res && res.data && res.data[0]) {
                    let dd: any = res.data
                    this.bills[dd[0].subscriptions[0]] = this.bills[dd[0].subscriptions[0]].concat(dd)
                    this.bills = Object.assign({}, this.bills)
                    if ((res.limit + res.skip) < res.total) {
                        this.getBillsPage(s, page + 1)
                    }
                }
            })
        },
        ggenerate () {
            this.subscriptions = []
            this.table.page = 1
            this.loadSubscriptions()
        },
        back () {
            this.$router.back()
        },
    },
    data () {
        let subscriptions: any = []
        let table: any = {
            limit: 20,
            page: 1,
        }
        let gendate: any = ''
        let bills: any = {}
        return {
            subscriptions,
            gendate,
            table,
            bills,
        }
    }
})
</script>
