<template>
    <div>
        <adminheader
            :title="'Invalid IC User List'" :backButton="true" :backTitle="'Back to reports'" :backFunction="back"></adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="w-full rounded bg-white p-3 shadow">
                <div class="text-center mt-5">
                    <div v-if="reportdata != undefined && !loading">
                        <div class="bg-white rounded-lg shadow overflow-hidden">
                            <div class="flex justify-between items-center p-4 border-b">
                                <div class="flex items-center space-x-4 w-full">
                                    <div class="flex-1">
                                    </div>
                                    <div>
                                        <button
                                            @click="exportReport"
                                            class="px-4 py-2 text-sm text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors"
                                        >
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>                            
                            <div class="px-4 py-2 bg-gray-50 border-b text-sm text-gray-600">
                                <div class="flex justify-between items-center">
                                    <span>
                                        Showing {{ ((currentPage - 1) * limit) + 1 }} - {{ Math.min(currentPage * limit, reportdata.total || reportdata.data.length) }} 
                                        of {{ reportdata.total || reportdata.data.length }} results
                                        <span v-if="keywords" class="ml-2 text-blue-600">for "{{ keywords }}"</span>
                                    </span>
                                    <div class="flex items-center space-x-2">
                                        <span>Show:</span>
                                        <select 
                                            v-model="limit" 
                                            @change="handleLimitChange"
                                            class="border rounded px-2 py-1 text-sm"
                                        >
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                        </select>
                                        <span>per page</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Identity No
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Email
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Name
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="data in reportdata.data" :key="data._id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-left">
                                            {{ data.identityno ? data.identityno : '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-black font-semibold text-left">
                                            {{ data.email }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-left">
                                            {{ data.name }}
                                        </td>
                                    </tr>
                                    <tr v-if="reportdata.data.length === 0">
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <div class="py-8">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                <h3 class="mt-2 text-sm font-medium text-gray-900">No results found</h3>
                                                <p class="mt-1 text-sm text-gray-500">
                                                    {{ keywords ? `No records found matching "${keywords}"` : 'No data available' }}
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                </table>
                            </div>                            
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 flex justify-between sm:hidden">
                                        <!-- Mobile pagination -->
                                        <button
                                            @click="goToPage(currentPage - 1)"
                                            :disabled="currentPage <= 1"
                                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            Previous
                                        </button>
                                        <button
                                            @click="goToPage(currentPage + 1)"
                                            :disabled="currentPage >= totalPages"
                                            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            Next
                                        </button>
                                    </div>
                                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                        <div>
                                            <p class="text-sm text-gray-700">
                                                Page <span class="font-medium">{{ currentPage }}</span> of <span class="font-medium">{{ totalPages }}</span>
                                            </p>
                                        </div>
                                        <div>
                                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                <!-- Previous button -->
                                                <button
                                                    @click="goToPage(currentPage - 1)"
                                                    :disabled="currentPage <= 1"
                                                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                >
                                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                                
                                                <!-- Page numbers -->
                                                <template v-for="page in visiblePages" :key="page">
                                                    <button
                                                        v-if="page !== '...'"
                                                        @click="goToPage(page)"
                                                        :class="[
                                                            page === currentPage
                                                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                                                            'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                                                        ]"
                                                    >
                                                        {{ page }}
                                                    </button>
                                                    <span
                                                        v-else
                                                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                                                    >
                                                        ...
                                                    </span>
                                                </template>
                                                
                                                <!-- Next button -->
                                                <button
                                                    @click="goToPage(currentPage + 1)"
                                                    :disabled="currentPage >= totalPages"
                                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                >
                                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="loading">
                        <div class="text-center p-10 font-semibold animate-pulse">Loading report...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getInvalidIcUserList, getZeroSpecialRateReport } from '../../../api'
import loading from '../../../components/cvui/loading.vue'
import * as XLSX from 'xlsx'
import config from '@/config'

export default defineComponent({
    setup () {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        return {
            token: computed(() => authState.token),
        }
    },
    components: {
        adminheader,
        svgCollection,
        DatePicker,
        loading
    },
    data() {
        return {
            reportdata: undefined,
            statuses: config.subscriptionStatus,
            loading: false,
            limit: 50,
            keywords: '',
            statustxt: '',
            skip: 0,
            searchInput: '', // Separate input field for search
            currentPage: 1
        }
    },
    computed: {
        totalPages() {
            if (!this.reportdata || !this.reportdata.total) return 1;
            return Math.ceil(this.reportdata.total / this.limit);
        },
        visiblePages() {
            const pages = [];
            const total = this.totalPages;
            const current = this.currentPage;
            
            if (total <= 7) {
                // Show all pages if total is 7 or less
                for (let i = 1; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                // Show first page
                pages.push(1);
                
                if (current > 4) {
                    pages.push('...');
                }
                
                // Show pages around current page
                const start = Math.max(2, current - 1);
                const end = Math.min(total - 1, current + 1);
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                if (current < total - 3) {
                    pages.push('...');
                }
                
                // Show last page
                if (total > 1) {
                    pages.push(total);
                }
            }
            
            return pages;
        }
    },
    methods: {
        back () {
            this.$router.back()
        },
        filterFunc() {
            this.statustxt;
            this.generateReport();
        },
        async generateReport () {
            this.loading = true
            this.skip = (this.currentPage - 1) * this.limit;
            
            const res: any = await getInvalidIcUserList({
                token: this.token,
                // $keywords: this.keywords,
                limit: this.limit,
                skip: this.skip,
                // statustxt: this.statustxt
            })
            this.reportdata = res;
            this.loading = false
        },
        handleSearch() {
            this.keywords = this.searchInput.trim();
            this.currentPage = 1; // Reset to first page when searching
            this.generateReport();
        },
        clearSearch() {
            this.searchInput = '';
            this.keywords = '';
            this.statustxt = '';
            this.currentPage = 1;
            this.generateReport();
        },
        handleLimitChange() {
            this.currentPage = 1; // Reset to first page when changing limit
            this.generateReport();
        },
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                this.currentPage = page;
                this.generateReport();
            }
        },
        goToNextPage() {
            if (this.currentPage < this.totalPages) {
                this.goToPage(this.currentPage + 1);
            }
        },
        goToPrevPage() {
            if (this.currentPage > 1) {
                this.goToPage(this.currentPage - 1);
            }
        },
        async exportReport() {
            this.loading = true;

            const allData: any[] = [];
            const limit = 50; // adjust based on your backend limits
            let skip = 0;
            let total = 0;
            let done = false;

            do {
                const res: any = await getInvalidIcUserList({
                    token: this.token,
                    $keywords: this.keywords,
                    limit,
                    skip
                });

                if (!res || !res.data || res.data.length === 0) break;

                allData.push(...res.data);

                if (total === 0 && res.total) {
                    total = res.total;
                }

                skip += limit;
                done = skip >= total;
            } while (!done);

            const headers = ['Identity No', 'Email', 'Name'];

            const dataRows = allData.map((data: any) => {
                return [
                    data.identityno ? data.identityno : '-',
                    data.email,
                    data.name,
                ];
            });

            const worksheetData = [headers, ...dataRows];
            const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Report');

            const wbout = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([wbout], { type: 'application/octet-stream' });

            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = 'invalid_ic_user_list_report.xlsx';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            this.loading = false;
        },
        formatdate (p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        formatdate2 (p: any) {
            return p.format('YYYY-MM-DD')
        },
        formatmoney (p: any) {
            return p && p.toFixed(2)
        },
        copy (p: any) {
            crossStore.SetNotmsg({
                title: this.$t('c.textcopied'),
                msg: p,
                type: 'success',
            })
            navigator.clipboard.writeText(p)
        },
    },
    mounted () {
        this.generateReport()
    }
})
</script>