<template>
  <div>
    <adminheader :title="$t('navigations.dashboard')"></adminheader>
    <template v-if="isDummy">
      <section class="py-8">
        <div class="container px-4 mx-auto">
          <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-1/3 px-4 mb-8 lg:mb-0">
              <div class="p-6 bg-white rounded" style="position: relative;">
                <h3 class="text-xl font-bold">Billings</h3>
                <div class="chart" data-type="radial-bar" style="min-height: 256px;">
                  <div id="apexchartsukt8wifz" class="apexcharts-canvas apexchartsukt8wifz apexcharts-theme-light" style="width: 306px; height: 256px;"><svg id="SvgjsSvg1618" width="306" height="256" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1620" class="apexcharts-inner apexcharts-graphical" transform="translate(25.5, 0)"><defs id="SvgjsDefs1619"><clipPath id="gridRectMaskukt8wifz"><rect id="SvgjsRect1622" width="261" height="257" x="-3" y="-1" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskukt8wifz"></clipPath><clipPath id="nonForecastMaskukt8wifz"></clipPath><clipPath id="gridRectMarkerMaskukt8wifz"><rect id="SvgjsRect1623" width="259" height="259" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><g id="SvgjsG1624" class="apexcharts-radialbar"><g id="SvgjsG1625"><g id="SvgjsG1626" class="apexcharts-tracks"><g id="SvgjsG1627" class="apexcharts-radialbar-track apexcharts-track" rel="1"><path id="apexcharts-radialbarTrack-0" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939" fill="none" fill-opacity="1" stroke="rgba(235,234,252,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.265243902439025" stroke-dasharray="0" class="apexcharts-radialbar-area" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939"></path></g></g><g id="SvgjsG1629"><g id="SvgjsG1634" class="apexcharts-series apexcharts-radial-series" seriesName="seriesx1" rel="1" data:realIndex="0"><path id="SvgjsPath1635" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632" fill="none" fill-opacity="0.85" stroke="rgba(56,44,221,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.76829268292683" stroke-dasharray="0" class="apexcharts-radialbar-area apexcharts-radialbar-slice-0" data:angle="194" data:value="54" index="0" j="0" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632"></path></g><circle id="SvgjsCircle1630" r="71.10518292682929" cx="127.5" cy="127.5" class="apexcharts-radialbar-hollow" fill="transparent"></circle><g id="SvgjsG1631" class="apexcharts-datalabels-group" transform="translate(0, 0) scale(1)" style="opacity: 1;"><text id="SvgjsText1632" font-family="Helvetica, Arial, sans-serif" x="127.5" y="127.5" text-anchor="middle" dominant-baseline="auto" font-size="24" font-weight="600" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-label" style="font-family: Helvetica, Arial, sans-serif;">$16,250</text><text id="SvgjsText1633" font-family="Helvetica, Arial, sans-serif" x="127.5" y="159.5" text-anchor="middle" dominant-baseline="auto" font-size="14px" font-weight="400" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-value" style="font-family: Helvetica, Arial, sans-serif;">Total Unpaid</text></g></g></g></g><line id="SvgjsLine1636" x1="0" y1="0" x2="255" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1637" x1="0" y1="0" x2="255" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line></g><g id="SvgjsG1621" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend"></div></div></div>
              </div>
            </div>
            <div class="w-full lg:w-1/3 px-4 mb-8 lg:mb-0">
              <div class="p-6 bg-white rounded" style="position: relative;">
                <h3 class="text-xl font-bold">Monthly Paid</h3>
                <div class="chart" data-type="radial-bar" data-variant="orange" style="min-height: 256px;"><div id="apexcharts2zdx25rth" class="apexcharts-canvas apexcharts2zdx25rth apexcharts-theme-light" style="width: 306px; height: 256px;"><svg id="SvgjsSvg1638" width="306" height="256" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1640" class="apexcharts-inner apexcharts-graphical" transform="translate(25.5, 0)"><defs id="SvgjsDefs1639"><clipPath id="gridRectMask2zdx25rth"><rect id="SvgjsRect1642" width="261" height="257" x="-3" y="-1" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMask2zdx25rth"></clipPath><clipPath id="nonForecastMask2zdx25rth"></clipPath><clipPath id="gridRectMarkerMask2zdx25rth"><rect id="SvgjsRect1643" width="259" height="259" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><g id="SvgjsG1644" class="apexcharts-radialbar"><g id="SvgjsG1645"><g id="SvgjsG1646" class="apexcharts-tracks"><g id="SvgjsG1647" class="apexcharts-radialbar-track apexcharts-track" rel="1"><path id="apexcharts-radialbarTrack-0" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939" fill="none" fill-opacity="1" stroke="rgba(235,234,252,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.265243902439025" stroke-dasharray="0" class="apexcharts-radialbar-area" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939"></path></g></g><g id="SvgjsG1649"><g id="SvgjsG1654" class="apexcharts-series apexcharts-radial-series" seriesName="seriesx1" rel="1" data:realIndex="0"><path id="SvgjsPath1655" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632" fill="none" fill-opacity="0.85" stroke="rgba(246,122,40,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.76829268292683" stroke-dasharray="0" class="apexcharts-radialbar-area apexcharts-radialbar-slice-0" data:angle="194" data:value="54" index="0" j="0" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632"></path></g><circle id="SvgjsCircle1650" r="71.10518292682929" cx="127.5" cy="127.5" class="apexcharts-radialbar-hollow" fill="transparent"></circle><g id="SvgjsG1651" class="apexcharts-datalabels-group" transform="translate(0, 0) scale(1)" style="opacity: 1;"><text id="SvgjsText1652" font-family="Helvetica, Arial, sans-serif" x="127.5" y="127.5" text-anchor="middle" dominant-baseline="auto" font-size="24" font-weight="600" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-label" style="font-family: Helvetica, Arial, sans-serif;">$16,250</text><text id="SvgjsText1653" font-family="Helvetica, Arial, sans-serif" x="127.5" y="159.5" text-anchor="middle" dominant-baseline="auto" font-size="14px" font-weight="400" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-value" style="font-family: Helvetica, Arial, sans-serif;">Total Paid</text></g></g></g></g><line id="SvgjsLine1656" x1="0" y1="0" x2="255" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1657" x1="0" y1="0" x2="255" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line></g><g id="SvgjsG1641" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend"></div></div></div>
              </div>
            </div>
            <div class="w-full lg:w-1/3 px-4">
              <div class="p-6 bg-white rounded" style="position: relative;">
                <h3 class="text-xl font-bold">New Subscription</h3>
                <apexdemo />
                <!-- <div class="chart" data-type="radial-bar" style="min-height: 256px;"><div id="apexchartsjodueossl" class="apexcharts-canvas apexchartsjodueossl apexcharts-theme-light" style="width: 306px; height: 256px;"><svg id="SvgjsSvg1658" width="306" height="256" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1660" class="apexcharts-inner apexcharts-graphical" transform="translate(25.5, 0)"><defs id="SvgjsDefs1659"><clipPath id="gridRectMaskjodueossl"><rect id="SvgjsRect1662" width="261" height="257" x="-3" y="-1" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskjodueossl"></clipPath><clipPath id="nonForecastMaskjodueossl"></clipPath><clipPath id="gridRectMarkerMaskjodueossl"><rect id="SvgjsRect1663" width="259" height="259" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><g id="SvgjsG1664" class="apexcharts-radialbar"><g id="SvgjsG1665"><g id="SvgjsG1666" class="apexcharts-tracks"><g id="SvgjsG1667" class="apexcharts-radialbar-track apexcharts-track" rel="1"><path id="apexcharts-radialbarTrack-0" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939" fill="none" fill-opacity="1" stroke="rgba(235,234,252,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.265243902439025" stroke-dasharray="0" class="apexcharts-radialbar-area" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 45.97002923838548 174.76127099267939"></path></g></g><g id="SvgjsG1669"><g id="SvgjsG1674" class="apexcharts-series apexcharts-radial-series" seriesName="seriesx1" rel="1" data:realIndex="0"><path id="SvgjsPath1675" d="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632" fill="none" fill-opacity="0.85" stroke="rgba(56,44,221,0.85)" stroke-opacity="1" stroke-linecap="round" stroke-width="16.76829268292683" stroke-dasharray="0" class="apexcharts-radialbar-area apexcharts-radialbar-slice-0" data:angle="194" data:value="54" index="0" j="0" data:pathOrig="M 45.88766697872866 174.6189024390244 A 94.23780487804879 94.23780487804879 0 1 1 218.08719213857756 101.52454064541632"></path></g><circle id="SvgjsCircle1670" r="71.10518292682929" cx="127.5" cy="127.5" class="apexcharts-radialbar-hollow" fill="transparent"></circle><g id="SvgjsG1671" class="apexcharts-datalabels-group" transform="translate(0, 0) scale(1)" style="opacity: 1;"><text id="SvgjsText1672" font-family="Helvetica, Arial, sans-serif" x="127.5" y="127.5" text-anchor="middle" dominant-baseline="auto" font-size="24" font-weight="600" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-label" style="font-family: Helvetica, Arial, sans-serif;">250</text><text id="SvgjsText1673" font-family="Helvetica, Arial, sans-serif" x="127.5" y="159.5" text-anchor="middle" dominant-baseline="auto" font-size="14px" font-weight="400" fill="#373d3f" class="apexcharts-text apexcharts-datalabel-value" style="font-family: Helvetica, Arial, sans-serif;">Total New Subscriber</text></g></g></g></g><line id="SvgjsLine1676" x1="0" y1="0" x2="255" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1677" x1="0" y1="0" x2="255" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line></g><g id="SvgjsG1661" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend"></div></div></div> -->
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="py-8">
        <div class="container px-4 mx-auto">
          <div class="flex flex-wrap -m-4">
            <div class="w-full lg:w-1/2 p-4">
              <div class="bg-white rounded" style="position: relative;">
                <div class="flex items-center py-5 px-6 border-b">
                  <h3 class="text-2xl font-bold">Monthly</h3>
                  <div class="ml-auto inline-block py-2 px-3 border rounded text-xs text-gray-500">
                    <select class="pr-1" name="" id="">
                      <option value="1">Monthly</option>
                      <option value="1">Yearly</option>
                      <option value="1">Weekly</option>
                    </select>
                  </div>
                </div>
                <div class="pt-4 px-6">
                  <div class="flex flex-wrap -m-4 pb-16">
                    <div class="w-full md:w-1/2 p-4">
                      <div class="py-4 px-6 border rounded">
                        <h4 class="text-xs text-gray-500">New Subscription</h4>
                        <div class="flex items-center">
                          <span class="mr-2 text-3xl font-bold">43.41%</span>
                          <span class="py-1 px-2 bg-green-500 text-xs text-white rounded-full">+2.5%</span>
                        </div>
                      </div>
                    </div>
                    <div class="w-full md:w-1/2 p-4">
                      <div class="py-4 px-6 border rounded">
                        <h4 class="text-xs text-gray-500">Sales Value</h4>
                        <div class="flex items-center">
                          <span class="mr-2 text-3xl font-bold">$95,422</span>
                          <span class="py-1 px-2 bg-green-500 text-xs text-white rounded-full">+13.5%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 
                  <div class="chart" data-type="area" style="min-height: 180px;"><div id="apexchartsw09d5neq" class="apexcharts-canvas apexchartsw09d5neq apexcharts-theme-light" style="width: 546px; height: 180px;"><svg id="SvgjsSvg1435" width="546" height="180" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1437" class="apexcharts-inner apexcharts-graphical" transform="translate(0, 0)"><defs id="SvgjsDefs1436"><clipPath id="gridRectMaskw09d5neq"><rect id="SvgjsRect1442" width="552" height="182" x="-3" y="-1" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskw09d5neq"></clipPath><clipPath id="nonForecastMaskw09d5neq"></clipPath><clipPath id="gridRectMarkerMaskw09d5neq"><rect id="SvgjsRect1443" width="550" height="184" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><line id="SvgjsLine1441" x1="0" y1="0" x2="0" y2="180" stroke="#b6b6b6" stroke-dasharray="3" stroke-linecap="butt" class="apexcharts-xcrosshairs" x="0" y="0" width="1" height="180" fill="#b1b9c4" filter="none" fill-opacity="0.9" stroke-width="1"></line><g id="SvgjsG1450" class="apexcharts-xaxis" transform="translate(0, 0)"><g id="SvgjsG1451" class="apexcharts-xaxis-texts-g" transform="translate(0, -4)"></g></g><g id="SvgjsG1482" class="apexcharts-grid"><g id="SvgjsG1483" class="apexcharts-gridlines-horizontal" style="display: none;"><line id="SvgjsLine1485" x1="0" y1="0" x2="546" y2="0" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1486" x1="0" y1="36" x2="546" y2="36" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1487" x1="0" y1="72" x2="546" y2="72" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1488" x1="0" y1="108" x2="546" y2="108" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1489" x1="0" y1="144" x2="546" y2="144" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1490" x1="0" y1="180" x2="546" y2="180" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line></g><g id="SvgjsG1484" class="apexcharts-gridlines-vertical" style="display: none;"></g><line id="SvgjsLine1492" x1="0" y1="180" x2="546" y2="180" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line><line id="SvgjsLine1491" x1="0" y1="1" x2="0" y2="180" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line></g><g id="SvgjsG1444" class="apexcharts-area-series apexcharts-plot-series"><g id="SvgjsG1445" class="apexcharts-series" seriesName="seriesx1" data:longestSeries="true" rel="1" data:realIndex="0"><path id="SvgjsPath1448" d="M 0 180L 0 108L 19.5 122.4L 39 115.2L 58.5 122.4L 78 100.8L 97.5 64.80000000000001L 117 151.2L 136.5 72L 156 50.400000000000006L 175.5 28.80000000000001L 195 50.400000000000006L 214.5 64.80000000000001L 234 43.20000000000002L 253.5 79.2L 273 86.4L 292.5 50.400000000000006L 312 108L 331.5 108L 351 108L 370.5 21.599999999999994L 390 43.20000000000002L 409.5 36L 429 50.400000000000006L 448.5 57.60000000000001L 468 43.20000000000002L 487.5 86.4L 507 50.400000000000006L 526.5 79.2L 546 72L 546 180M 546 72z" fill="rgba(235,234,252,0.85)" fill-opacity="1" stroke-opacity="1" stroke-linecap="butt" stroke-width="0" stroke-dasharray="0" class="apexcharts-area" index="0" clip-path="url(#gridRectMaskw09d5neq)" pathTo="M 0 180L 0 108L 19.5 122.4L 39 115.2L 58.5 122.4L 78 100.8L 97.5 64.80000000000001L 117 151.2L 136.5 72L 156 50.400000000000006L 175.5 28.80000000000001L 195 50.400000000000006L 214.5 64.80000000000001L 234 43.20000000000002L 253.5 79.2L 273 86.4L 292.5 50.400000000000006L 312 108L 331.5 108L 351 108L 370.5 21.599999999999994L 390 43.20000000000002L 409.5 36L 429 50.400000000000006L 448.5 57.60000000000001L 468 43.20000000000002L 487.5 86.4L 507 50.400000000000006L 526.5 79.2L 546 72L 546 180M 546 72z" pathFrom="M -1 180L -1 180L 19.5 180L 39 180L 58.5 180L 78 180L 97.5 180L 117 180L 136.5 180L 156 180L 175.5 180L 195 180L 214.5 180L 234 180L 253.5 180L 273 180L 292.5 180L 312 180L 331.5 180L 351 180L 370.5 180L 390 180L 409.5 180L 429 180L 448.5 180L 468 180L 487.5 180L 507 180L 526.5 180L 546 180"></path><path id="SvgjsPath1449" d="M 0 108L 19.5 122.4L 39 115.2L 58.5 122.4L 78 100.8L 97.5 64.80000000000001L 117 151.2L 136.5 72L 156 50.400000000000006L 175.5 28.80000000000001L 195 50.400000000000006L 214.5 64.80000000000001L 234 43.20000000000002L 253.5 79.2L 273 86.4L 292.5 50.400000000000006L 312 108L 331.5 108L 351 108L 370.5 21.599999999999994L 390 43.20000000000002L 409.5 36L 429 50.400000000000006L 448.5 57.60000000000001L 468 43.20000000000002L 487.5 86.4L 507 50.400000000000006L 526.5 79.2L 546 72" fill="none" fill-opacity="1" stroke="#382cdd" stroke-opacity="1" stroke-linecap="butt" stroke-width="2" stroke-dasharray="0" class="apexcharts-area" index="0" clip-path="url(#gridRectMaskw09d5neq)" pathTo="M 0 108L 19.5 122.4L 39 115.2L 58.5 122.4L 78 100.8L 97.5 64.80000000000001L 117 151.2L 136.5 72L 156 50.400000000000006L 175.5 28.80000000000001L 195 50.400000000000006L 214.5 64.80000000000001L 234 43.20000000000002L 253.5 79.2L 273 86.4L 292.5 50.400000000000006L 312 108L 331.5 108L 351 108L 370.5 21.599999999999994L 390 43.20000000000002L 409.5 36L 429 50.400000000000006L 448.5 57.60000000000001L 468 43.20000000000002L 487.5 86.4L 507 50.400000000000006L 526.5 79.2L 546 72" pathFrom="M -1 180L -1 180L 19.5 180L 39 180L 58.5 180L 78 180L 97.5 180L 117 180L 136.5 180L 156 180L 175.5 180L 195 180L 214.5 180L 234 180L 253.5 180L 273 180L 292.5 180L 312 180L 331.5 180L 351 180L 370.5 180L 390 180L 409.5 180L 429 180L 448.5 180L 468 180L 487.5 180L 507 180L 526.5 180L 546 180"></path><g id="SvgjsG1446" class="apexcharts-series-markers-wrap" data:realIndex="0"><g class="apexcharts-series-markers"><circle id="SvgjsCircle1498" r="0" cx="0" cy="0" class="apexcharts-marker wpgk470ws no-pointer-events" stroke="#ffffff" fill="#382cdd" fill-opacity="1" stroke-width="2" stroke-opacity="0.9" default-marker-size="0"></circle></g></g></g><g id="SvgjsG1447" class="apexcharts-datalabels" data:realIndex="0"></g></g><line id="SvgjsLine1493" x1="0" y1="0" x2="546" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1494" x1="0" y1="0" x2="546" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line><g id="SvgjsG1495" class="apexcharts-yaxis-annotations"></g><g id="SvgjsG1496" class="apexcharts-xaxis-annotations"></g><g id="SvgjsG1497" class="apexcharts-point-annotations"></g></g><rect id="SvgjsRect1440" width="0" height="0" x="0" y="0" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fefefe"></rect><g id="SvgjsG1481" class="apexcharts-yaxis" rel="0" transform="translate(-18, 0)"></g><g id="SvgjsG1438" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend" style="max-height: 90px;"></div><div class="apexcharts-tooltip apexcharts-theme-light"><div class="apexcharts-tooltip-title" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"></div><div class="apexcharts-tooltip-series-group" style="order: 1;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(56, 44, 221);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div></div><div class="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light"><div class="apexcharts-yaxistooltip-text"></div></div></div></div>
                -->
              </div>
            </div>
            <div class="w-full lg:w-1/2 p-4">
              <div class="p-6 mb-8 bg-white shadow rounded">
                <div class="flex mb-3 items-center justify-between">
                  <h3 class="text-gray-500">Total Sales</h3>
                  <button class="focus:outline-none">
                    <svg class="h-4 w-4 text-gray-200" viewBox="0 0 16 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0.333344C7.67037 0.333344 7.34813 0.431092 7.07405 0.614228C6.79997 0.797363 6.58635 1.05766 6.4602 1.36221C6.33406 1.66675 6.30105 2.00186 6.36536 2.32516C6.42967 2.64846 6.5884 2.94543 6.82149 3.17852C7.05458 3.41161 7.35155 3.57034 7.67485 3.63465C7.99815 3.69896 8.33326 3.66596 8.63781 3.53981C8.94235 3.41366 9.20265 3.20004 9.38578 2.92596C9.56892 2.65188 9.66667 2.32965 9.66667 2.00001C9.66667 1.55798 9.49107 1.13406 9.17851 0.821499C8.86595 0.508939 8.44203 0.333344 8 0.333344ZM2.16667 0.333344C1.83703 0.333344 1.5148 0.431092 1.24072 0.614228C0.966635 0.797363 0.753014 1.05766 0.626868 1.36221C0.500722 1.66675 0.467717 2.00186 0.532025 2.32516C0.596334 2.64846 0.755068 2.94543 0.988156 3.17852C1.22124 3.41161 1.51822 3.57034 1.84152 3.63465C2.16482 3.69896 2.49993 3.66596 2.80447 3.53981C3.10902 3.41366 3.36931 3.20004 3.55245 2.92596C3.73559 2.65188 3.83333 2.32965 3.83333 2.00001C3.83333 1.55798 3.65774 1.13406 3.34518 0.821499C3.03262 0.508939 2.6087 0.333344 2.16667 0.333344ZM13.8333 0.333344C13.5037 0.333344 13.1815 0.431092 12.9074 0.614228C12.6333 0.797363 12.4197 1.05766 12.2935 1.36221C12.1674 1.66675 12.1344 2.00186 12.1987 2.32516C12.263 2.64846 12.4217 2.94543 12.6548 3.17852C12.8879 3.41161 13.1849 3.57034 13.5082 3.63465C13.8315 3.69896 14.1666 3.66596 14.4711 3.53981C14.7757 3.41366 15.036 3.20004 15.2191 2.92596C15.4023 2.65188 15.5 2.32965 15.5 2.00001C15.5 1.55798 15.3244 1.13406 15.0118 0.821499C14.6993 0.508939 14.2754 0.333344 13.8333 0.333344Z" fill="currentColor"></path>
                    </svg>
                  </button>
                </div>
                <div class="flex items-center mb-3">
                  <span class="text-4xl font-bold">$124,563.00</span>
                  <span class="inline-block ml-2 py-1 px-2 bg-green-500 text-white text-xs rounded-full">+6.9%</span>
                </div>
                <div class="relative w-full h-1 mb-2 bg-gray-50 rounded">
                  <div class="absolute top-0 left-0 w-4/6 h-full bg-purple-500 rounded"></div>
                </div>
                <p class="text-xs text-gray-200">Yearly Goal</p>
              </div>
              <div class="p-6 bg-white shadow rounded" style="position: relative;">
                <div class="flex mb-3 items-center justify-between">
                  <h3 class="text-gray-500">New Users</h3>
                  <button class="focus:outline-none">
                    <svg class="h-4 w-4 text-gray-200" viewBox="0 0 16 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0.333344C7.67037 0.333344 7.34813 0.431092 7.07405 0.614228C6.79997 0.797363 6.58635 1.05766 6.4602 1.36221C6.33406 1.66675 6.30105 2.00186 6.36536 2.32516C6.42967 2.64846 6.5884 2.94543 6.82149 3.17852C7.05458 3.41161 7.35155 3.57034 7.67485 3.63465C7.99815 3.69896 8.33326 3.66596 8.63781 3.53981C8.94235 3.41366 9.20265 3.20004 9.38578 2.92596C9.56892 2.65188 9.66667 2.32965 9.66667 2.00001C9.66667 1.55798 9.49107 1.13406 9.17851 0.821499C8.86595 0.508939 8.44203 0.333344 8 0.333344ZM2.16667 0.333344C1.83703 0.333344 1.5148 0.431092 1.24072 0.614228C0.966635 0.797363 0.753014 1.05766 0.626868 1.36221C0.500722 1.66675 0.467717 2.00186 0.532025 2.32516C0.596334 2.64846 0.755068 2.94543 0.988156 3.17852C1.22124 3.41161 1.51822 3.57034 1.84152 3.63465C2.16482 3.69896 2.49993 3.66596 2.80447 3.53981C3.10902 3.41366 3.36931 3.20004 3.55245 2.92596C3.73559 2.65188 3.83333 2.32965 3.83333 2.00001C3.83333 1.55798 3.65774 1.13406 3.34518 0.821499C3.03262 0.508939 2.6087 0.333344 2.16667 0.333344ZM13.8333 0.333344C13.5037 0.333344 13.1815 0.431092 12.9074 0.614228C12.6333 0.797363 12.4197 1.05766 12.2935 1.36221C12.1674 1.66675 12.1344 2.00186 12.1987 2.32516C12.263 2.64846 12.4217 2.94543 12.6548 3.17852C12.8879 3.41161 13.1849 3.57034 13.5082 3.63465C13.8315 3.69896 14.1666 3.66596 14.4711 3.53981C14.7757 3.41366 15.036 3.20004 15.2191 2.92596C15.4023 2.65188 15.5 2.32965 15.5 2.00001C15.5 1.55798 15.3244 1.13406 15.0118 0.821499C14.6993 0.508939 14.2754 0.333344 13.8333 0.333344Z" fill="currentColor"></path>
                    </svg>
                  </button>
                </div>
                <div class="flex items-center mb-3">
                  <span class="text-4xl font-bold">94.2%</span>
                  <span class="inline-block ml-2 py-1 px-2 bg-green-500 text-white text-xs rounded-full">+6.9%</span>
                </div>
                <!--
                <div class="chart" data-type="columns-stacked" style="min-height: 100px;"><div id="apexchartspl5c03mj" class="apexcharts-canvas apexchartspl5c03mj apexcharts-theme-light" style="width: 498px; height: 100px;"><svg id="SvgjsSvg1499" width="498" height="100" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1501" class="apexcharts-inner apexcharts-graphical" transform="translate(42.199999999999996, 0)"><defs id="SvgjsDefs1500"><linearGradient id="SvgjsLinearGradient1504" x1="0" y1="0" x2="0" y2="1"><stop id="SvgjsStop1505" stop-opacity="0.4" stop-color="rgba(216,227,240,0.4)" offset="0"></stop><stop id="SvgjsStop1506" stop-opacity="0.5" stop-color="rgba(190,209,230,0.5)" offset="1"></stop><stop id="SvgjsStop1507" stop-opacity="0.5" stop-color="rgba(190,209,230,0.5)" offset="1"></stop></linearGradient><clipPath id="gridRectMaskpl5c03mj"><rect id="SvgjsRect1509" width="502" height="100" x="-40.199999999999996" y="0" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskpl5c03mj"></clipPath><clipPath id="nonForecastMaskpl5c03mj"></clipPath><clipPath id="gridRectMarkerMaskpl5c03mj"><rect id="SvgjsRect1510" width="425.6" height="104" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><rect id="SvgjsRect1508" width="42.16" height="100" x="0" y="0" rx="0" ry="0" opacity="1" stroke-width="0" stroke-dasharray="3" fill="url(#SvgjsLinearGradient1504)" class="apexcharts-xcrosshairs" y2="100" filter="none" fill-opacity="0.9"></rect><g id="SvgjsG1532" class="apexcharts-xaxis" transform="translate(0, 0)"><g id="SvgjsG1533" class="apexcharts-xaxis-texts-g" transform="translate(0, -4)"></g></g><g id="SvgjsG1541" class="apexcharts-grid"><g id="SvgjsG1542" class="apexcharts-gridlines-horizontal" style="display: none;"><line id="SvgjsLine1544" x1="-38.199999999999996" y1="0" x2="459.8" y2="0" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1545" x1="-38.199999999999996" y1="16.666666666666668" x2="459.8" y2="16.666666666666668" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1546" x1="-38.199999999999996" y1="33.333333333333336" x2="459.8" y2="33.333333333333336" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1547" x1="-38.199999999999996" y1="50" x2="459.8" y2="50" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1548" x1="-38.199999999999996" y1="66.66666666666667" x2="459.8" y2="66.66666666666667" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1549" x1="-38.199999999999996" y1="83.33333333333334" x2="459.8" y2="83.33333333333334" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1550" x1="-38.199999999999996" y1="100.00000000000001" x2="459.8" y2="100.00000000000001" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line></g><g id="SvgjsG1543" class="apexcharts-gridlines-vertical" style="display: none;"></g><line id="SvgjsLine1552" x1="0" y1="100" x2="421.6" y2="100" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line><line id="SvgjsLine1551" x1="0" y1="1" x2="0" y2="100" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line></g><g id="SvgjsG1511" class="apexcharts-bar-series apexcharts-plot-series"><g id="SvgjsG1512" class="apexcharts-series" seriesName="seriesx1" rel="1" data:realIndex="0"><path id="SvgjsPath1514" d="M -21.08 100L -21.08 66.66666666666666Q -21.08 66.66666666666666 -21.08 66.66666666666666L 21.08 66.66666666666666Q 21.08 66.66666666666666 21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 100L 21.08 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M -21.08 100L -21.08 66.66666666666666Q -21.08 66.66666666666666 -21.08 66.66666666666666L 21.08 66.66666666666666Q 21.08 66.66666666666666 21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 100L 21.08 100z" pathFrom="M -21.08 100L -21.08 100L 21.08 100L 21.08 100L 21.08 100L 21.08 100L 21.08 100L -21.08 100" cy="66.66666666666666" cx="21.08" j="0" val="20" barHeight="33.333333333333336" barWidth="42.16"></path><path id="SvgjsPath1515" d="M 39.14857142857144 100L 39.14857142857144 16.666666666666657Q 39.14857142857144 16.666666666666657 39.14857142857144 16.666666666666657L 81.30857142857144 16.666666666666657Q 81.30857142857144 16.666666666666657 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 100L 81.30857142857144 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 39.14857142857144 100L 39.14857142857144 16.666666666666657Q 39.14857142857144 16.666666666666657 39.14857142857144 16.666666666666657L 81.30857142857144 16.666666666666657Q 81.30857142857144 16.666666666666657 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 100L 81.30857142857144 100z" pathFrom="M 39.14857142857144 100L 39.14857142857144 100L 81.30857142857144 100L 81.30857142857144 100L 81.30857142857144 100L 81.30857142857144 100L 81.30857142857144 100L 39.14857142857144 100" cy="16.666666666666657" cx="81.30857142857144" j="1" val="50" barHeight="83.33333333333334" barWidth="42.16"></path><path id="SvgjsPath1516" d="M 99.37714285714287 100L 99.37714285714287 70Q 99.37714285714287 70 99.37714285714287 70L 141.53714285714287 70Q 141.53714285714287 70 141.53714285714287 70L 141.53714285714287 70L 141.53714285714287 100L 141.53714285714287 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 99.37714285714287 100L 99.37714285714287 70Q 99.37714285714287 70 99.37714285714287 70L 141.53714285714287 70Q 141.53714285714287 70 141.53714285714287 70L 141.53714285714287 70L 141.53714285714287 100L 141.53714285714287 100z" pathFrom="M 99.37714285714287 100L 99.37714285714287 100L 141.53714285714287 100L 141.53714285714287 100L 141.53714285714287 100L 141.53714285714287 100L 141.53714285714287 100L 99.37714285714287 100" cy="70" cx="141.53714285714287" j="2" val="18" barHeight="30" barWidth="42.16"></path><path id="SvgjsPath1517" d="M 159.60571428571433 100L 159.60571428571433 16.666666666666657Q 159.60571428571433 16.666666666666657 159.60571428571433 16.666666666666657L 201.76571428571432 16.666666666666657Q 201.76571428571432 16.666666666666657 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 100L 201.76571428571432 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 159.60571428571433 100L 159.60571428571433 16.666666666666657Q 159.60571428571433 16.666666666666657 159.60571428571433 16.666666666666657L 201.76571428571432 16.666666666666657Q 201.76571428571432 16.666666666666657 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 100L 201.76571428571432 100z" pathFrom="M 159.60571428571433 100L 159.60571428571433 100L 201.76571428571432 100L 201.76571428571432 100L 201.76571428571432 100L 201.76571428571432 100L 201.76571428571432 100L 159.60571428571433 100" cy="16.666666666666657" cx="201.76571428571432" j="3" val="50" barHeight="83.33333333333334" barWidth="42.16"></path><path id="SvgjsPath1518" d="M 219.83428571428573 100L 219.83428571428573 50Q 219.83428571428573 50 219.83428571428573 50L 261.99428571428575 50Q 261.99428571428575 50 261.99428571428575 50L 261.99428571428575 50L 261.99428571428575 100L 261.99428571428575 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 219.83428571428573 100L 219.83428571428573 50Q 219.83428571428573 50 219.83428571428573 50L 261.99428571428575 50Q 261.99428571428575 50 261.99428571428575 50L 261.99428571428575 50L 261.99428571428575 100L 261.99428571428575 100z" pathFrom="M 219.83428571428573 100L 219.83428571428573 100L 261.99428571428575 100L 261.99428571428575 100L 261.99428571428575 100L 261.99428571428575 100L 261.99428571428575 100L 219.83428571428573 100" cy="50" cx="261.99428571428575" j="4" val="30" barHeight="50" barWidth="42.16"></path><path id="SvgjsPath1519" d="M 280.0628571428572 100L 280.0628571428572 33.33333333333333Q 280.0628571428572 33.33333333333333 280.0628571428572 33.33333333333333L 322.22285714285715 33.33333333333333Q 322.22285714285715 33.33333333333333 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 100L 322.22285714285715 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 280.0628571428572 100L 280.0628571428572 33.33333333333333Q 280.0628571428572 33.33333333333333 280.0628571428572 33.33333333333333L 322.22285714285715 33.33333333333333Q 322.22285714285715 33.33333333333333 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 100L 322.22285714285715 100z" pathFrom="M 280.0628571428572 100L 280.0628571428572 100L 322.22285714285715 100L 322.22285714285715 100L 322.22285714285715 100L 322.22285714285715 100L 322.22285714285715 100L 280.0628571428572 100" cy="33.33333333333333" cx="322.22285714285715" j="5" val="40" barHeight="66.66666666666667" barWidth="42.16"></path><path id="SvgjsPath1520" d="M 340.29142857142864 100L 340.29142857142864 50Q 340.29142857142864 50 340.29142857142864 50L 382.45142857142866 50Q 382.45142857142866 50 382.45142857142866 50L 382.45142857142866 50L 382.45142857142866 100L 382.45142857142866 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 340.29142857142864 100L 340.29142857142864 50Q 340.29142857142864 50 340.29142857142864 50L 382.45142857142866 50Q 382.45142857142866 50 382.45142857142866 50L 382.45142857142866 50L 382.45142857142866 100L 382.45142857142866 100z" pathFrom="M 340.29142857142864 100L 340.29142857142864 100L 382.45142857142866 100L 382.45142857142866 100L 382.45142857142866 100L 382.45142857142866 100L 382.45142857142866 100L 340.29142857142864 100" cy="50" cx="382.45142857142866" j="6" val="30" barHeight="50" barWidth="42.16"></path><path id="SvgjsPath1521" d="M 400.52000000000004 100L 400.52000000000004 33.33333333333333Q 400.52000000000004 33.33333333333333 400.52000000000004 33.33333333333333L 442.68000000000006 33.33333333333333Q 442.68000000000006 33.33333333333333 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 100L 442.68000000000006 100z" fill="rgba(45,112,245,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 400.52000000000004 100L 400.52000000000004 33.33333333333333Q 400.52000000000004 33.33333333333333 400.52000000000004 33.33333333333333L 442.68000000000006 33.33333333333333Q 442.68000000000006 33.33333333333333 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 100L 442.68000000000006 100z" pathFrom="M 400.52000000000004 100L 400.52000000000004 100L 442.68000000000006 100L 442.68000000000006 100L 442.68000000000006 100L 442.68000000000006 100L 442.68000000000006 100L 400.52000000000004 100" cy="33.33333333333333" cx="442.68000000000006" j="7" val="40" barHeight="66.66666666666667" barWidth="42.16"></path></g><g id="SvgjsG1522" class="apexcharts-series" seriesName="seriesx2" rel="2" data:realIndex="1"><path id="SvgjsPath1524" d="M -21.08 66.66666666666666L -21.08 24.666666666666657Q -21.08 16.666666666666657 -13.079999999999998 16.666666666666657L 13.079999999999998 16.666666666666657Q 21.08 16.666666666666657 21.08 24.666666666666657L 21.08 24.666666666666657L 21.08 66.66666666666666L 21.08 66.66666666666666z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M -21.08 66.66666666666666L -21.08 24.666666666666657Q -21.08 16.666666666666657 -13.079999999999998 16.666666666666657L 13.079999999999998 16.666666666666657Q 21.08 16.666666666666657 21.08 24.666666666666657L 21.08 24.666666666666657L 21.08 66.66666666666666L 21.08 66.66666666666666z" pathFrom="M -21.08 66.66666666666666L -21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 66.66666666666666L 21.08 66.66666666666666L -21.08 66.66666666666666" cy="16.666666666666657" cx="21.08" j="0" val="30" barHeight="50" barWidth="42.16"></path><path id="SvgjsPath1525" d="M 39.14857142857144 16.666666666666657L 39.14857142857144 16.666666666666657Q 39.14857142857144 16.666666666666657 39.14857142857144 16.666666666666657L 81.30857142857144 16.666666666666657Q 81.30857142857144 16.666666666666657 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 39.14857142857144 16.666666666666657L 39.14857142857144 16.666666666666657Q 39.14857142857144 16.666666666666657 39.14857142857144 16.666666666666657L 81.30857142857144 16.666666666666657Q 81.30857142857144 16.666666666666657 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657z" pathFrom="M 39.14857142857144 16.666666666666657L 39.14857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 81.30857142857144 16.666666666666657L 39.14857142857144 16.666666666666657" cy="16.666666666666657" cx="81.30857142857144" j="1" val="0" barHeight="0" barWidth="42.16"></path><path id="SvgjsPath1526" d="M 99.37714285714287 70L 99.37714285714287 24.666666666666664Q 99.37714285714287 16.666666666666664 107.37714285714287 16.666666666666664L 133.53714285714287 16.666666666666664Q 141.53714285714287 16.666666666666664 141.53714285714287 24.666666666666664L 141.53714285714287 24.666666666666664L 141.53714285714287 70L 141.53714285714287 70z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 99.37714285714287 70L 99.37714285714287 24.666666666666664Q 99.37714285714287 16.666666666666664 107.37714285714287 16.666666666666664L 133.53714285714287 16.666666666666664Q 141.53714285714287 16.666666666666664 141.53714285714287 24.666666666666664L 141.53714285714287 24.666666666666664L 141.53714285714287 70L 141.53714285714287 70z" pathFrom="M 99.37714285714287 70L 99.37714285714287 70L 141.53714285714287 70L 141.53714285714287 70L 141.53714285714287 70L 141.53714285714287 70L 141.53714285714287 70L 99.37714285714287 70" cy="16.666666666666664" cx="141.53714285714287" j="2" val="32" barHeight="53.333333333333336" barWidth="42.16"></path><path id="SvgjsPath1527" d="M 159.60571428571433 16.666666666666657L 159.60571428571433 16.666666666666657Q 159.60571428571433 16.666666666666657 159.60571428571433 16.666666666666657L 201.76571428571432 16.666666666666657Q 201.76571428571432 16.666666666666657 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 159.60571428571433 16.666666666666657L 159.60571428571433 16.666666666666657Q 159.60571428571433 16.666666666666657 159.60571428571433 16.666666666666657L 201.76571428571432 16.666666666666657Q 201.76571428571432 16.666666666666657 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657z" pathFrom="M 159.60571428571433 16.666666666666657L 159.60571428571433 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 201.76571428571432 16.666666666666657L 159.60571428571433 16.666666666666657" cy="16.666666666666657" cx="201.76571428571432" j="3" val="0" barHeight="0" barWidth="42.16"></path><path id="SvgjsPath1528" d="M 219.83428571428573 50L 219.83428571428573 24.666666666666664Q 219.83428571428573 16.666666666666664 227.83428571428573 16.666666666666664L 253.99428571428575 16.666666666666664Q 261.99428571428575 16.666666666666664 261.99428571428575 24.666666666666664L 261.99428571428575 24.666666666666664L 261.99428571428575 50L 261.99428571428575 50z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 219.83428571428573 50L 219.83428571428573 24.666666666666664Q 219.83428571428573 16.666666666666664 227.83428571428573 16.666666666666664L 253.99428571428575 16.666666666666664Q 261.99428571428575 16.666666666666664 261.99428571428575 24.666666666666664L 261.99428571428575 24.666666666666664L 261.99428571428575 50L 261.99428571428575 50z" pathFrom="M 219.83428571428573 50L 219.83428571428573 50L 261.99428571428575 50L 261.99428571428575 50L 261.99428571428575 50L 261.99428571428575 50L 261.99428571428575 50L 219.83428571428573 50" cy="16.666666666666664" cx="261.99428571428575" j="4" val="20" barHeight="33.333333333333336" barWidth="42.16"></path><path id="SvgjsPath1529" d="M 280.0628571428572 33.33333333333333L 280.0628571428572 24.66666666666666Q 280.0628571428572 16.66666666666666 288.0628571428572 16.66666666666666L 314.22285714285715 16.66666666666666Q 322.22285714285715 16.66666666666666 322.22285714285715 24.66666666666666L 322.22285714285715 24.66666666666666L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 280.0628571428572 33.33333333333333L 280.0628571428572 24.66666666666666Q 280.0628571428572 16.66666666666666 288.0628571428572 16.66666666666666L 314.22285714285715 16.66666666666666Q 322.22285714285715 16.66666666666666 322.22285714285715 24.66666666666666L 322.22285714285715 24.66666666666666L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333z" pathFrom="M 280.0628571428572 33.33333333333333L 280.0628571428572 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 322.22285714285715 33.33333333333333L 280.0628571428572 33.33333333333333" cy="16.66666666666666" cx="322.22285714285715" j="5" val="10" barHeight="16.666666666666668" barWidth="42.16"></path><path id="SvgjsPath1530" d="M 340.29142857142864 50L 340.29142857142864 24.666666666666664Q 340.29142857142864 16.666666666666664 348.29142857142864 16.666666666666664L 374.45142857142866 16.666666666666664Q 382.45142857142866 16.666666666666664 382.45142857142866 24.666666666666664L 382.45142857142866 24.666666666666664L 382.45142857142866 50L 382.45142857142866 50z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 340.29142857142864 50L 340.29142857142864 24.666666666666664Q 340.29142857142864 16.666666666666664 348.29142857142864 16.666666666666664L 374.45142857142866 16.666666666666664Q 382.45142857142866 16.666666666666664 382.45142857142866 24.666666666666664L 382.45142857142866 24.666666666666664L 382.45142857142866 50L 382.45142857142866 50z" pathFrom="M 340.29142857142864 50L 340.29142857142864 50L 382.45142857142866 50L 382.45142857142866 50L 382.45142857142866 50L 382.45142857142866 50L 382.45142857142866 50L 340.29142857142864 50" cy="16.666666666666664" cx="382.45142857142866" j="6" val="20" barHeight="33.333333333333336" barWidth="42.16"></path><path id="SvgjsPath1531" d="M 400.52000000000004 33.33333333333333L 400.52000000000004 24.66666666666666Q 400.52000000000004 16.66666666666666 408.52000000000004 16.66666666666666L 434.68000000000006 16.66666666666666Q 442.68000000000006 16.66666666666666 442.68000000000006 24.66666666666666L 442.68000000000006 24.66666666666666L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333z" fill="rgba(241,245,251,1)" fill-opacity="1" stroke-opacity="1" stroke-linecap="round" stroke-width="0" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskpl5c03mj)" pathTo="M 400.52000000000004 33.33333333333333L 400.52000000000004 24.66666666666666Q 400.52000000000004 16.66666666666666 408.52000000000004 16.66666666666666L 434.68000000000006 16.66666666666666Q 442.68000000000006 16.66666666666666 442.68000000000006 24.66666666666666L 442.68000000000006 24.66666666666666L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333z" pathFrom="M 400.52000000000004 33.33333333333333L 400.52000000000004 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 442.68000000000006 33.33333333333333L 400.52000000000004 33.33333333333333" cy="16.66666666666666" cx="442.68000000000006" j="7" val="10" barHeight="16.666666666666668" barWidth="42.16"></path></g><g id="SvgjsG1513" class="apexcharts-datalabels" data:realIndex="0"></g><g id="SvgjsG1523" class="apexcharts-datalabels" data:realIndex="1"></g></g><line id="SvgjsLine1553" x1="-38.199999999999996" y1="0" x2="459.8" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1554" x1="-38.199999999999996" y1="0" x2="459.8" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line><g id="SvgjsG1555" class="apexcharts-yaxis-annotations"></g><g id="SvgjsG1556" class="apexcharts-xaxis-annotations"></g><g id="SvgjsG1557" class="apexcharts-point-annotations"></g></g><g id="SvgjsG1540" class="apexcharts-yaxis" rel="0" transform="translate(-18, 0)"></g><g id="SvgjsG1502" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend" style="max-height: 50px;"></div><div class="apexcharts-tooltip apexcharts-theme-light"><div class="apexcharts-tooltip-title" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"></div><div class="apexcharts-tooltip-series-group" style="order: 1;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(45, 112, 245);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div><div class="apexcharts-tooltip-series-group" style="order: 2;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(241, 245, 251);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div></div><div class="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light"><div class="apexcharts-yaxistooltip-text"></div></div></div></div>
                -->
              </div>
            </div>
          </div>
        </div>
      </section>
    </template>
    
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import apexdemo from '@/components/apexdemo.vue'
export default defineComponent({
  components: {
    adminheader,
    apexdemo
  },
  data () {
    let isDummy: Boolean = false
    return {
      isDummy
    }
  }
})
</script>
