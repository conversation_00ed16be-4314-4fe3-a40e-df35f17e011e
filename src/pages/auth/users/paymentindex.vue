<template>
    <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('users.payments')"
      :btnNoText="$t('c.close')"
      :btnNoFunction="cancelFunc"
      v-if="item">
      <template v-if="databases && !paymentitem">
          <div class="text-right">
            <button v-if="isDev" class="bg-indigo-500 text-white hover:bg-indigo-700 px-5 py-1 rounded text-xs mr-5" @click="recalculate()">Recalculate Amount Paid</button>
            <button class="bg-yellow-600 text-white hover:bg-yellow-800 px-5 py-1 rounded text-xs" @click="viewReceipt(item)">{{$t('payments.viewReceipt')}}</button>
            <button v-if="isCurrentMonth(item.billdate) || hideAddPayment2" class="bg-blue-400 text-white hover:bg-blue-600 px-5 py-1 rounded text-xs ml-5" @click="addPayment">{{$t('payments.add')}}</button>
            <button class="bg-green-400 text-white hover:bg-green-600 px-5 py-1 rounded text-xs ml-5" @click="loadDatabase">{{$t('c.reload')}}</button>            
          </div>
          <div class="hidden" id="hidepay1">
            <input type="checkbox" v-model="hideAddPayment2" /> Show Payment Btn
          </div>
        
          <template v-if="databases.total == 0">
            <div class="text-center p-20 text-gray-600 italic">
                {{$t('payments.no_payments')}}
            </div>
          </template>
          <template v-else>
            <dtable
                :columns="columns"
                :data="databases"
                columnColor="white">
                <template v-slot:action="slotProps">
                <!-- <button @click="printPDF(slotProps.item, slotProps.index)" class="inline-block h-8 pr-5 bg-blue-500 hover:bg-blue-700 text-white mr-2 rounded-full">
                    <svgicon icon="print" dclass="inline-block w-4 h-4 mx-2" /> PDF
                </button>
                <button @click="payment(slotProps.item)" class="inline-block h-8 pr-5 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
                    <svgicon icon="cash" dclass="inline-block w-4 h-4 mx-2" /> Payment
                </button>               -->
                    <button @click="delitem(slotProps.item)" class="h-8 pr-5 hidden bg-gray-800 hover:bg-gray-600 text-white mr-2 rounded-full">
                        <svgicon icon="trash" dclass="inline-block w-4 h-4 mx-2" /> {{$t('c.delete')}}
                    </button>
                    <button v-if="isCurrentMonth(item.billdate)" @click="edtPayment(slotProps.item)" class="inline-block h-8 pr-5 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
                        <svgicon icon="edit" dclass="inline-block w-4 h-4 mx-2" /> {{$t('c.edit')}}
                    </button>
                    <!-- <div>{{slotProps.item}}</div> -->
                    <div v-if="slotProps.item.attachments">
                        <showattachment :token="token" :attachments="slotProps.item.attachments" />
                    </div>                    
                </template>
            </dtable>
            <dpagination
                :total="databases && databases.total || 0"
                :page="table.page"
                :limit="table.limit"
                :pageChange="pageChange"
                defaultColor="blue"
            />
          </template>
      </template>
      <template v-else>
          <dloading v-if="!paymentitem" />
          <template v-else>
              <PaymentForm :token="token" :item="paymentitem" :cancel="closePaymentItem" :submitfunc="createPayment" />
          </template>
      </template>
    </PopupModal>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import { paymentStore } from '../../../store/payment-store'
import { crossStore } from '../../../store/cross-store'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import showattachment from './showattachment.vue'
import PaymentForm from './paymentform.vue'
import { auth2Store } from '../../../store/auth2-store'
import { createPayment, getSubscription, updateBilling, updatePayment } from '../../../api'
import { imageBase64 } from '@/assets/logo_base64.js';
import type { Font } from '@pdfme/common';
import { generate } from '@pdfme/generator'
import invTemplate from '../billings/template/invoice_v2.json'
import moment from 'moment'
export default defineComponent({
    setup () {
        const authStore: any = inject('authStore')
        const authState = authStore.getState()
        const paymentState = paymentStore.getState()
        const auth2State = auth2Store.getState()
        return {
            token: computed(() => authState.token),
            profile: computed(() => auth2State.profile),
            databases: computed(() => paymentState.payments),
            paymentCreateSuccess: computed(() => paymentState.paymentCreateSuccess),
            paymentCreateError: computed(() => paymentState.paymentCreateError),
            isDev: computed(() => auth2State.profile.scopes && auth2State.profile.scopes.includes('dev')),
        }
    },
    components: {
        PopupModal,
        PaymentForm,
        dtable,
        dpagination,
        svgicon,
        dloading,
        showattachment,
    },
    mounted () {
        (async function (t: any) {
            const kk: Font = {
                Arial: {
                    data: await fetch('/fonts/Arial/Arial2.ttf').then((res) => res.arrayBuffer()),
                    fallback: true,
                },
                Effra: {
                    data: await fetch('/fonts/Effra/Effra.ttf').then((res) => res.arrayBuffer()),
                },
                EffraBold: {
                data: await fetch('/fonts/Effra/EffraBold.ttf').then((res) => res.arrayBuffer()),
                },
                Gotham: {
                data: await fetch('/fonts/Gotham/GothamMedium.ttf').then((res) => res.arrayBuffer()),
                },
                GothamBold: {
                    data: await fetch('/fonts/Gotham/GothamBold.ttf').then((res) => res.arrayBuffer()),
                },
            };
            t.font = kk;
        })(this);

        this.loadDatabase()
    },
    watch: {
        paymentCreateSuccess (p) {
            if (p) {
                this.paymentitem = undefined
                crossStore.SetNotmsg({
                title: this.$t('c.createTitle') + this.$t('payments.title'),
                msg: this.$t('c.createdSuccess'),
                type: 'success'
                })
            }
        },
        paymentCreateError (p) {
            if (p) {
                crossStore.SetModalmsg({
                title: this.$t('c.createTitle') + this.$t('payments.title'),
                msg: this.$t('c.createError'),
                type: 'error',
                proceedTxt: this.$t('c.okay')
                })
            }
        },
    },
    computed: {
        columns () {
            return [
                // { title: 'payments.id', key: 'id', type: 'string', class: 'text-center' },
                { title: 'payments.paymentdate', key: 'paymentdate', type: 'date', class: 'text-center' },
                { title: 'payments.createdate', key: 'created_at', type: 'date', class: 'text-center' },
                { title: 'payments.amount', key: 'amount', type: 'price', class: 'text-center' },        
                { title: 'payments.platform', key: 'platform', type: 'string', class: 'text-center' },
                { title: 'payments.statustxt', key: 'statustxt', type: 'string', class: 'text-center' },
                { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
            ]
        }
    },
    props: {
        item: {
            type: Object,
            required: true
        }, 
        cancel: {
            type: Function
        },
        hideAddPayment: {
            type: Boolean,
            default: true
        },
        user: {
            type: Object,
            required: true
        }
    },
    methods: {
        recalculate () {
            if (this.databases.data.length > 0) {
                const form = {
                    amountpaid: this.databases.data.reduce((sum: any, payment: any) => sum + payment.amount, 0)
                }
                updateBilling({ token: this.token, id: this.item.id, form: form })
            }
        },
        isCurrentMonth (date: string) {
            // return true
            return moment(date).isSame(moment(), 'month') || moment(date).add(7, 'days').isSame(moment().subtract(1, 'month'), 'month')
        },
        shortMonthFormat (p: any) {
            let r = ''
            if (p) {
                r = moment(p).format('MM/YY')
            }
            return r
        },
        delitem (p: any) {          
            this.deleteItem = p
            let tt = this.$t
            crossStore.SetModalmsg({
                title: tt('c.deleteTitle'),
                msg: `${tt('c.confirmDelete')} [${p.platform}] ${tt('payments.title')}  ${tt('payments.dated')} - ${this.formatDate(p.paymentdate)} ${tt('c.withAmount')} ${this.formatMoney(p.amount)} ?`,
                proceedTxt:  this.$t('c.okay'),
                proceedFunc: () => { this.deleteNow(p.id)},
            })
        },
        edtPayment (p: any) {
            if (p) {
                this.paymentitem = JSON.parse(JSON.stringify(p))
            }
        },
        deleteNow(id: any) {
            crossStore.SetModalmsg(null)
            paymentStore.deletePayment({ token: this.token, id })
        },
        formatDate (p: any) {
            return moment(p).format('YYYY-MM-DD')
        },
        loadDatabase () {
            paymentStore.getPayments({...this.table, token: this.token })
        },
        formatMoney (p: any) {
            return p.toFixed(2)
        },
        cancelFunc() {
            if (this.cancel) {
                this.cancel()
            }
        },
        pageChange (p:any) {
            this.table.page = p
            this.loadDatabase()
        },
        savesilentfunc (p: any) {
            if (p && p.id) {
                updatePayment({ token: this.token, form: p, id: p.id })
            }
        },
        addPayment () {
            this.paymentitem = {
                customer: this.item.customer,
                bill: this.item.id,
                amount: 0,
                paymentdate: new Date(),
                remark: '',
                statustxt: 'paid',
                platform: 'manual',
                platformid: '',
            }
        },
        closePaymentItem () {
            this.paymentitem = undefined
        },
        createPayment (p: any) {
            if (p && p.id) {
                // update payment
                paymentStore.updatePayment({ token: this.token, form: p, id: p.id })
                this.closePaymentItem()
                this.loadDatabase()
            } else {
                // create payment
                paymentStore.createPayment({ token: this.token, form: p })
                this.closePaymentItem()
                this.loadDatabase()
            }
        },
        viewReceipt (p: any) {
            if (p && p.subscriptions && p.subscriptions.length > 0 ) {
                let sid: string = p.subscriptions[0]
                getSubscription({ token: this.token, id: sid }).then((res: any) => {
                let r = res && res.data
                this.printPDF2(p, r)
                })
            } else {
                this.printPDF2(p, null)
            }
        },
        printPDF2 (p: any, sub: any) {
            const template: any = invTemplate;
            const c: any = this.user

            let amts = ''
            let itms = ''

            // items
            p.items.forEach((item: any, index: number) => {
                itms += `${item.itemname} \n`
                // get \n count in item.itemname
                let ncount = item.itemname.split(/\r?\n/)

                amts += `${this.formatMoney(item.amount)} \n`
                if (ncount.length > 1) {
                for (let i = 1; i < ncount.length; i++) {
                    amts += '\n'
                }
                }
            })

            var canvas = document.getElementById("qrcode_" + p.id) as HTMLCanvasElement;

            if(canvas){
                var pngDataUrl = canvas.toDataURL("image/png");
            } else {
                var pngDataUrl = ''
            }

            const balance = p.totalamount - p.amountpaid

            const inputs = [{
                "logo": imageBase64,
                "companyAddress": "A1-02-07, Level 2, Block A1, Sunway GEO Avenue\nJalan Lagoon Selatan, Sunway South Quay, Bandar Sunway\n47500 Subang Jaya",
                "itemTitle": "RECEIPT",
                "customerNameWtTitleGreeting": `Dear ${c.title && c.title.toUpperCase() || ''} ${c.name}`,
                "sidLabel": "Subscriber ID",
                "sid": `${sub.sid || ''}`,
                "companyName": "HIIFI (M) SDN. BHD. (1406319-P)",
                "customerNameWtTitle": `${c.title && c.title.toUpperCase()  || ''} ${c.name}`,
                "summaryTitle": "Here's a summary bill for month " + this.shortMonthFormat(p.billdate),
                "billLabel": "Bill No",
                // "userLabel": "User",
                "billdateLabel": "Bill Date",
                "duedateLabel": "Due Date\n\nDeposit",
                // "creditLabel": "",
                // "credit": "",
                "billNo": p.billno || p.id,
                "user": c.email,
                // "addonTitle": this.addOnTitle(sub),
                "billDate": this.formatDate(p.billdate),
                "dueDate": (p.duedate ? this.formatDate(p.duedate) : '-/-/-' ) + "\n\n" + (sub.deposit ? this.formatMoney(sub.deposit) : '0.00' ),
                // "Line": "   ",
                // "Line2": "   ",
                // "Line3": "   ",
                "customerAddress": `${sub.address.address || '---'}`,
                "page": "Page 1/1",
                // "Line4": "   ",
                // "Line5": "   ",
                "empty": " ",
                "empty2": " ",
                "itemsLabel": "Items",
                "amountLabel": "Amount (MYR)",
                "items": itms,
                "amounts": amts,
                "subtotalLabel": "Subtotal",
                "taxLabel": "Tax",
                "bfLabel": "B/F",
                "subtotal": this.formatMoney(p.amountcurrent),
                "tax": this.formatMoney(p.taxcurrent),
                "bf": this.formatMoney(p.amountbf),
                "totalLabel": "Total Amount Payable",
                "totalLabel2": "Total ",
                "total": `${this.formatMoney(p.totalamount)}`,
                "total2": `${this.formatMoney(p.totalamount)}`,
                "paid": 'Paid',
                "paidAmount": `${this.formatMoney(p.amountpaid)}`,
                "paidOn": 'Paid On:',
                "paidOnDate": `${this.formatDate(p.paymentdate)}`,
                "lineTotal": " ",
                "balance": "Balance",
                "balanceAmount": `${this.formatMoney(balance)}`,
                "empty3": " ",
                // "footerLabel1": "Notes:",
                // "footerLabel2": "1. All remittance shoud be made payable to :-\n    HIIFI(M) SDN. BHD.\n    CIMB **********\n\n2. Please include the invoice number at the recipient reference.\n3. Please share the bank in slip to HIIFI Support + 6019-719 9799\n",
                "footerLabel1": "",
                "footerLabel2": "",
                "footerfooter2": ``,
                "qrcode": ''
            }];
            if (template && inputs) {
                generate({ template, inputs, options: {font: this.font} }).then((pdf: any) => {
                const blob = new Blob([pdf.buffer], { type: 'application/pdf' });
                window.open(URL.createObjectURL(blob));
                });
            }
        },
    },
    data () {
        let paymentitem: any = undefined
        let deleteItem: any = undefined
        let table: any = {
            limit: 10,
            page: 1,
            keywords: '',
            // customer: this.item.customer,
            bill: this.item.id,
            token: this.token,
            status: true,            
        }
        let hideAddPayment2: boolean = false
        let font: any = null
        return {
            paymentitem,
            deleteItem,
            table,
            font,
            hideAddPayment2,
        }
    }
})
</script>
