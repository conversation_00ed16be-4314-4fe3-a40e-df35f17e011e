<template>
    <PopupModal defaultColor="blue" modalWidthPercent="90" :title="$t('navigations.prebills')"
        :btnNoText="$t('c.close')" :btnNoFunction="cancel" v-if="item">
        <template v-if="fixviewitem">
            <fixview :reloadfunc="reload" :item="fixviewitem" :subscriptions="subscriptions" :cancel="cancelFixView" />
        </template>

        <div class="border-b p-1 px-5 rounded bg-gray-100">
            <div class="inline-block text-lg">{{ item.email }}</div>
            <div class="inline-block mx-2 text-md rounded bg-blue-300 px-2" v-if="item.name">
                {{ item.name }}
            </div>
        </div>
        <div class="p-2">
            <div class="text-right">
                <button v-if="subscribefilter" @click="selectfilter('')"
                    class="text-xs rounded px-2 py-1 bg-blue-400 hover:bg-blue-600 text-white mr-2">
                    {{ $t("c.clearfilter") }}
                </button>
                <button @click="loadSubscriptionsList"
                    class="text-xs rounded px-2 py-1 bg-blue-400 hover:bg-blue-600 text-white mr-2">
                    {{ $t("c.getsubscriptions") }}
                </button>
                <button @click="loadDatabase" class="text-xs rounded px-2 py-1 bg-green-200 hover:bg-green-400 mr-2">
                    {{ $t("c.reload") }}
                </button>
                <button class="text-xs rounded px-2 py-1 bg-gray-200 hover:bg-gray-400">
                    Sync
                </button>
            </div>
            <div class="inline-block mr-5">
                <input type="checkbox" v-model="showId" />&nbsp;{{ $t("c.showid") }}
            </div>
            <div class="inline-block mr-5"> <input type="checkbox" v-model="showUnit" />&nbsp;{{$t('c.showunit')}}</div>
        </div>
        <div v-if="showsublist" class="m-2">
            <div class="mb-1">
                {{ $t("c.highlight") }}
                <input class="border shadow rounded p-1" v-model="subscriptionHighlight" />
            </div>
            <div @click="selectfilter(sc.id)" class="text-xs py-1 px-3 ml-1 mb-1 cursor-pointer rounded inline-block"
                :class="filterItem(sc.id) + ' ' + highlightCss(formatadd(sc) + ' ' + sc.sid)
                    " :key="sc.id" v-for="sc in subscriptions">
                {{ formatadd(sc) }} {{ sc.sid }}
            </div>
        </div>
        <div class="">
            <div v-if="databases == null">
                <dloading />
            </div>
            <template v-else>
                <div class="text-xs my-2 ml-10 flex justify-between">
                    <div class="mb-1">
                        {{ $t("c.billnoFilter") }}
                        <input class="ml-3 border shadow rounded p-2 text-md" v-model="billnoFilter" />
                        <button @click="reload"
                            class="inline-block h-8 px-5 bg-yellow-500 hover:bg-yellow-700 text-white ml-2 rounded">
                            {{ $t("c.filter") }}
                        </button>
                    </div>
                    <div class="w-full md:w-1/5 inline-block px-1">
                        <MonthPicker v-model="selectedMonth" :clearable="true" defaultColor="blue"></MonthPicker>
                    </div>
                    <div class="mb-1 flex items-center gap-2">
                        <p>Items per page</p>
                        <select @change="reload" class="mr-4 border shadow rounded p-2 text-md" v-model="limit">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
                <dtable :columns="columns" :data="databases" :checkboxFunc="checkboxFunc" :checkboxlist="checkboxlist"
                    columnColor="white">
                </dtable>
                <dpagination :total="(databases && databases.total) || 0" :page="table.page" :limit="table.limit"
                    :pageChange="pageChange" defaultColor="blue" />
            </template>
            <div :class="isDev ? '' : 'invisible'">
                <input type="checkbox" v-model="showSend" /> Show Send
            </div>
        </div>
    </PopupModal>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from "vue";
import PopupModal from "@/components/cvui/Modal.vue";
import dtable from "@/components/cvui/table/index.vue";
import dpagination from "@/components/cvui/table/Pagination.vue";
import svgicon from "@/components/cvui/svgcollection.vue";
import dloading from "@/components/cvui/loading.vue";
import { auth2Store } from "../../../store/auth2-store";
import moment from "moment";
import fixview from "./fixview.vue";
import {
    fixHFCDBilling,
    getSubscription,
    getSubscriptionGroups,
    getSubscriptions,
    sendBill,
} from "../../../api";
import DatePicker from "../../../components/cvui/form/DatePicker.vue";
import FormItemCompt from "../../../components/cvui/form/FormItem.vue";
import MonthPicker from "../../../components/MonthPicker.vue";
import { prebillsStore } from "@/store/prebill-store";
export default defineComponent({
    setup() {
        const authStore: any = inject("authStore");
        const authState = authStore.getState();
        const auth2State = auth2Store.getState();
        return {
            token: computed(() => authState.token),
            authStore: authStore,
            authState: authState,
            profile: computed(() => auth2State.profile),
            prebillsStore: prebillsStore,
            prebillsState: prebillsStore.getState(),
            isDev: computed(
                () =>
                    auth2State.profile.scopes && auth2State.profile.scopes.includes("dev")
            ),
        };
    },
    mounted() {
        document.addEventListener("keydown", this.handleEscKey);
        this.reload();
        this.isSubGroup();
    },
    beforeUnmount() {
        document.removeEventListener("keydown", this.handleEscKey);
    },
    components: {
        PopupModal,
        dtable,
        dpagination,
        dloading,
        svgicon,
        fixview,
        FormItemCompt,
        DatePicker,
        MonthPicker,
    },
    props: {
        item: Object,
        title: String,
        cancelFunc: Function,
        submitFunc: Function,
    },
    methods: {
        fixhfcd(id: string) {
            if (confirm("Are you sure to fix this bill?")) {
                fixHFCDBilling({ token: this.token, id: id }).then((r: any) => {
                    this.reload();
                });
            }
        },
        isSubGroup() {
            if (this.item) {
                getSubscriptionGroups({ user: this.item.id, token: this.token }).then(
                    (res: any) => {
                        if (res.total > 0) {
                            this.subGroup = true;
                        }
                    }
                );
            }
        },
        isCurrentMonth(date: string) {
            return moment(date).isSame(moment(), "month");
        },
        handleEscKey(event: KeyboardEvent) {
            if (event.key === "Escape") {
                this.cancel();
            }
        },
        cancelFixView() {
            this.fixviewitem = undefined;
        },
        highlightCss(p: string) {
            if (
                this.subscriptionHighlight.trim().length > 0 &&
                p.toLowerCase().indexOf(this.subscriptionHighlight.toLowerCase()) > -1
            ) {
                return "border-2 border-red-600";
            } else {
                return "";
            }
        },
        filterItem(id: string) {
            return this.subscribefilter && id && this.subscribefilter == id
                ? "bg-blue-600 text-white hover:bg-blue-400"
                : "bg-gray-600 text-white hover:bg-gray-400";
        },
        selectfilter(id: string) {
            this.subscribefilter = id;
            this.reload();
        },
        loadSubscriptionsList() {
            if (this.item) {
                this.showsublist = true;
                this.subscriptions = [];
                let params: any = ["customer"];
                let customer: any = this.item!.id;
                let filter: any = {
                    token: this.token,
                    limit: 10,
                    params: params,
                    customer: customer,
                };
                this.loadss(filter);
            }
        },
        loadss(filter: any) {
            getSubscriptions(filter).then((res: any) => {
                if (res && res.data) {
                    // console.log(res.data)
                    this.subscriptions = this.subscriptions.concat(
                        res.data.map((k: any) => {
                            k["value"] = this.formatadd(k);
                            return k;
                        })
                    );
                    if (res.total > this.subscriptions.length) {
                        filter.skip = this.subscriptions.length;
                        this.loadss(filter);
                    }
                }
            });
        },
        fixbillView(p: any) {
            this.fixviewitem = JSON.parse(JSON.stringify(p));
        },
        cancel() {
            if (this.cancelFunc) {
                this.cancelFunc();
            }
        },
        sendNow() {
            if (confirm("double confirm to send?")) {
                // console.log(this.showTxt)
                sendBill({ data: this.showTxt, token: this.token });
                this.showTxt = "";
            }
        },
        submitNow() {
            if (this.submitFunc) {
                this.submitFunc();
            }
        },
        sendbill(p: any) {
            let h: any = JSON.parse(JSON.stringify(this.item));
            let g: any = {
                item: JSON.parse(JSON.stringify(p)),
                customer: {
                    email: h.email,
                    name: h.name,
                    title: h.title,
                    contact: h.contact,
                },
            };

            if (p && p.subscriptions && p.subscriptions.length > 0) {
                let sid: string = p.subscriptions[0];
                getSubscription({ token: this.token, id: sid }).then((res: any) => {
                    let r = res && res.data;
                    g.item["deposit"] = r.deposit;
                    g["billingaddress"] = r.billingaddress && r.billingaddress.address;
                    g["sub"] = r;
                    if (r.address && r.address["block"]) {
                        g["customer"]["blockinfo"] = `${r.address.building || "The Grand Subang Jaya SS15"
                            }  ${r.address.block}-${r.address.level}-${r.address.unit}`;
                    }
                    this.sendbillC(g);
                });
            } else {
                g.item["deposit"] = 0;
                g["billingaddress"] = "";
                this.sendbillC(g);
            }
        },
        sendbillC(g: any) {
            this.showTxt = g;
        },
        formatMoney(p: any) {
            if (p != null) {
                return p.toFixed(2);
            } else {
                return "0.00";
            }
        },
        formatadd(p: any) {
            return p && p.address
                ? `${p.address.block}-${p.address.level}-${p.address.unit} ${p.address.building}`
                : "---";
        },
        reload() {
            this.table.page = 1;
            this.table.keywords = "";
            this.keywords = "";
            this.selectedMonth = null;
            this.table.limit = this.limit;
            this.loadDatabase();
        },
        shortMonthFormat(p: any) {
            let r = "";
            if (p) {
                r = moment(p).format("MM/YY");
            }
            return r;
        },
        formatDate(p: any) {
            let r = "";
            if (p) {
                r = moment(p).format("DD/MM/YYYY");
            }
            return r;
        },
        loadDatabase() {
            if (this.item) {
                if (this.subscribefilter) {
                    let p = {
                        ...this.table,
                        token: this.token,
                        customer: this.item.id,
                        subscriptions: [this.subscribefilter],
                    };

                    if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
                        p["billno"] = this.billnoFilter.trim();
                        if (!p["params"]) {
                            p["params"] = [];
                        }
                        p["params"].push("billno");
                    }

                    this.prebillsStore.getPrebills(p);
                } else {
                    let p = { ...this.table, token: this.token, customer: this.item.id };

                    if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
                        p["billno"] = this.billnoFilter.trim();
                        if (!p["params"]) {
                            p["params"] = [];
                        }
                        p["params"].push("billno");
                    }

                    if (this.selectedMonth) {
                        const selectedDate = new Date(this.selectedMonth);

                        const day = String(selectedDate.getDate()).padStart(2, "0");
                        const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
                        const year = selectedDate.getFullYear();

                        const formattedDate = `${day}-${month}-${year}`;
                        p.$billdate = formattedDate;
                    }

                    this.prebillsStore.getPrebills(p);
                }
            }
        },
        pageChange(p: any) {
            this.table.page = p;
            this.loadDatabase();
        },
        addOnTitle(s: any) {
            let c: any = this.item;
            return s
                ? `${s.address["building"] || "The Grand Subang Jaya SS15"} ${s.address["block"]
                }-${s.address["level"]}-${s.address["unit"]}\n${s.contact}`
                : `${c.contact}`;
        },
        checkboxFunc(type: any, sitem: any) {
            if (type === "checkall") {
                if (this.checkboxlist.length === this.databases.data.length) {
                    this.checkboxlist = [];
                } else {
                    this.checkboxlist = [...this.databases.data];
                }
            } else if (type === "checkone") {
                let index = this.checkboxlist.findIndex((k: any) => k.id === sitem.id);
                if (index >= 0) {
                    this.checkboxlist.splice(index, 1);
                } else {
                    this.checkboxlist.push(sitem);
                }
            }
        },
    },
    computed: {
        columns() {
            let subscriptions: any = this.subscriptions;
            let ar: any = [
                {
                    title: "billings.billno",
                    key: "billno",
                    type: "text",
                    class: "text-center",
                },
                {
                    title: "billings.billdate",
                    key: "billdate",
                    type: "date",
                    class: "text-center",
                },
                {
                    title: "billings.total",
                    key: "totalamount",
                    type: "price",
                    class: "text-center",
                },
                {
                    title: "billings.duedate",
                    key: "duedate",
                    type: "date",
                    class: "text-center",
                },
                {
                    title: "billings.paid",
                    key: "amountpaid",
                    type: "price",
                    class: "text-center",
                },
            ];
            if (this.showUnit) {
                ar.unshift({
                    title: "billings.subscriptions",
                    key: "subscriptions",
                    objectlist: subscriptions,
                    type: "objectarray",
                    class: "text-center",
                });
            }
            if (this.showId) {
                ar.unshift({
                    title: "billings.id",
                    key: "id",
                    type: "text",
                    class: "text-center",
                });
            }
            return ar;
        },
        databases() {
            return prebillsStore.getState().prebills;
        },
    },
    watch: {
        selectedMonth(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.loadDatabase();
            }
        },
    },
    data() {
        return {
            table: { page: 1, keywords: "" },
            subscription: undefined,
            keywords: "",
            fixviewitem: undefined,
            billnoFilter: "",
            showId: false,
            showTxt: "",
            showSend: false,
            showUnit: false,
            showFixBill: false,
            showsublist: false,
            subscriptions: [],
            subscribefilter: undefined,
            subscriptionHighlight: "",
            startDate: null,
            endDate: null,
            selectedMonth: null,
            checkboxlist: [],
            subGroup: false,
            limit: 10,
        };
    },
});
</script>
