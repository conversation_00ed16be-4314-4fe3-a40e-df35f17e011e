<template>
    <div>
        <template v-if="whow"><ShowFiles :files="attachmentlist" :disableDelete="readOnly" /> </template>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import { getFile } from '../../../api'
export default defineComponent({
    props: {
        attachments: { type: Array },
        token: { type: String },
    },
    components: {
        ShowFiles,
    },
    mounted () {
        let k: any = this.attachments
        for (var i=0; i < k.length; i++) {
            getFile({token: this.token, id: k[i]}).then((res: any) => {
                this.attachmentlist.push(res['data'])
                this.refresh()
            })
        }
        
    },
    methods: {
        refresh() {
            this.whow = false
            this.$nextTick(()=> {
                this.whow = true
            })
        }
    },
    data () {
        let attachmentlist: any = []
        let readOnly: boolean = true
        let whow: boolean = true
        return {
            attachmentlist,
            readOnly,
            whow,
        }
    }
})
</script>
