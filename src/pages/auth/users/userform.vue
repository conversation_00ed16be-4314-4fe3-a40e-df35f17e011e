<template>
    <form @submit.prevent="preventsubmit" autocomplete="">
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="title"
            :labelTitle="$t('users.title')">
            <span class="text-xs rounded px-2 py-1" :class="isFormDisabled ? 'bg-gray-300 cursor-not-allowed' : 'cursor-pointer hover:bg-red-200 bg-blue-300'" @click="!isFormDisabled && switchTitleString()"><svgcollection icon="signal" dclass="inline-block w-3 h-3" /> {{$t('users.others')}}</span>
            <template v-if="titleString">
              <TextInput
                name="title"
                v-model="item.title"
                :placeholder="$t('c.frontPlc') + $t('users.title')"
                defaultColor="blue" :readonly="isFormDisabled"></TextInput>
            </template>
            <div v-else class="py-3">
              <SelectOne
                :list="titleList"
                :selectedItem="item.title"
                showTitle="title"
                itemValue="id"
                :addOrRemoveFunction="selectTitle"
                defaultColor="blue" :readonly="isFormDisabled"></SelectOne>
            </div>
          <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="name"
            :labelTitle="$t('users.name')"
            :required="true">
          <TextInput
              name="name"
              v-model="item.name"
              :placeholder="$t('c.frontPlc') + $t('users.name')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="identityno"
            :labelTitle="$t('users.identityNo')" :required="true">
          <TextInput
              name="identityno"
              v-model="item.identityno"
              :placeholder="$t('c.frontPlc') + $t('users.identityNo')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.identityno"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="email"
            :labelTitle="$t('users.email')"
            :required="true">
          <TextInput
              name="email"
              v-model="item.email"
              :placeholder="$t('c.frontPlc') + $t('users.email')"
              defaultColor="blue" :required="true" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.email"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="dob"
            :labelTitle="$t('users.dob')">
          <DatePicker
            v-model="item.dob"
            :clearable="true"
            defaultColor="blue" :disabled="isFormDisabled"></DatePicker>
          <ErrTextCompt :errs="errs && errs.dob"></ErrTextCompt>
        </FormItemCompt>
        <div class="w-full md:w-2/3 inline-block px-1">
            <FormItemCompt
                class="w-full md:w-2/3 inline-block px-1"
                labelFor="nationality"
                :labelTitle="$t('users.nationality')">
                <div><select v-model="item.nationality" class="p-4 rounded" :disabled="isFormDisabled">
                    <option v-for="(p, g) in countries" :key="`nation_${g}`" :value="p.code">{{p.name}}</option>
                </select></div>
                <ErrTextCompt :errs="errs && errs.nationality"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt
                class="w-full md:w-1/3 inline-block px-1"
                labelFor="gender"
                :labelTitle="$t('users.gender')">
                <div>
                    <input :disabled="isFormDisabled" v-model="igender" class="mr-2" type="radio" value="m">
                    <label class="mr-10 text-xs" for="html">{{$t('c.male')}}</label>
                    <input :disabled="isFormDisabled" v-model="igender" class="mr-2" type="radio" value="f">
                    <label class="text-xs" for="css">{{$t('c.female')}}</label>
                </div>
                <ErrTextCompt :errs="errs && errs.gender"></ErrTextCompt>
            </FormItemCompt>
        </div>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="contact"
            :labelTitle="$t('users.contact')">
          <TextInput
              name="contact"
              v-model="item.contact"
              :placeholder="$t('c.frontPlc') + $t('users.contact')"
              defaultColor="blue" :required="true" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.contact"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="contact2"
            :labelTitle="$t('users.contact2')">
          <TextInput
              name="contact2"
              v-model="item.contact2"
              :placeholder="$t('c.frontPlc') + $t('users.contact2')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.contact2"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="mothersName"
            :labelTitle="$t('users.mothersName')">
          <TextInput
              name="mothersName"
              v-model="item.mothersname"
              :placeholder="$t('c.frontPlc') + $t('users.mothersName')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.mothersname"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="fathersName"
            :labelTitle="$t('users.fathersName')">
          <TextInput
              name="fathersName"
              v-model="item.fathersname"
              :placeholder="$t('c.frontPlc') + $t('users.fathersName')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.fathersname"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="eInvoice"
            :labelTitle="$t('users.eInvoice')">
          <TextInput
              name="eInvoice"
              v-model="item.taxentityid"
              :placeholder="$t('c.frontPlc') + $t('users.eInvoice')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
              <div class="shadow text-xs inline-block px-2 py-1 rounded mt-2" :class="isFormDisabled ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-100 cursor-pointer'">Sync Autocount</div>
          <ErrTextCompt :errs="errs && errs.taxentityid"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="msic"
            :labelTitle="$t('users.msic')">
          <TextInput
              name="msic"
              v-model="item.msic"
              :placeholder="$t('c.frontPlc') + $t('users.msic')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.msic"></ErrTextCompt>
          <div class="mt-2">&nbsp;</div>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="autoCount"
            :labelTitle="$t('users.autoCount')">
          <TextInput
              name="autoCount"
              v-model="item.acaccno"
              :placeholder="$t('c.frontPlc') + $t('users.autoCount')"
              defaultColor="blue" :readonly="isFormDisabled"></TextInput>
          <ErrTextCompt :errs="errs && errs.acaccno"></ErrTextCompt>
          <div @click="!isFormDisabled && syncAutocountDebtor()" class="shadow text-xs inline-block px-2 py-1 rounded mt-2" :class="isFormDisabled ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-100 cursor-pointer'">Sync Autocount</div>
        </FormItemCompt>
        <template v-if="showMod">
            <template v-if="item.id">
                <template v-if="hasScope('dev')">
                    <FormItemCompt 
                        class="w-full inline-block px-1 py-5"
                        labelFor="scopes"
                        :labelTitle="$t('users.scopes')">
                        <SelectList :list="configscopes" :selectedList="item.scopes" :addOrRemoveFunction="addRemoveScopes" defaultColor="blue" :readonly="isFormDisabled"></SelectList>
                        <ErrTextCompt :errs="errs && errs.scopes"></ErrTextCompt>
                    </FormItemCompt>
                </template>
                <FormItemCompt
                    class="w-full md:w-1/3 inline-block px-1 align-top"
                    labelFor="lastIP"
                    :labelTitle="$t('users.lastIP')">
                    <TextInput
                        name="lastIP"
                        v-model="item.lastip"
                        :placeholder="$t('c.frontPlc') + $t('users.lastIP')"
                        defaultColor="blue"
                        :readonly="true"></TextInput>
                </FormItemCompt>
                <FormItemCompt
                    class="w-full md:w-1/3 inline-block px-1 align-top"
                    labelFor="lastLogin"
                    :labelTitle="$t('users.lastLogin')">
                    <DateTimePicker
                        v-model="item.lastlogin"
                        defaultColor="blue"
                        :disabled="true"></DateTimePicker>
                </FormItemCompt>             
                <div class="w-full md:w-1/3 inline-block px-1 align-top">         
                    <div class="w-full md:w-1/3 inline-block px-1 align-center">                    
                        <FormItemCompt
                            class=""
                            labelFor="installer"
                            labelTitle="">
                            <Checkbox v-model="item.installer" :label="$t('users.installer')" defaultColor="blue" :disabled="isFormDisabled"></Checkbox>
                            <ErrTextCompt :errs="errs && errs.installer"></ErrTextCompt>
                        </FormItemCompt>
                        <FormItemCompt
                            class=""
                            labelFor="agent"
                            labelTitle="">
                            <Checkbox v-model="item.agent" :label="$t('users.agent')" defaultColor="blue" :disabled="isFormDisabled"></Checkbox>
                            <ErrTextCompt :errs="errs && errs.agent"></ErrTextCompt>
                        </FormItemCompt>
                    </div>
                    <div class="w-full md:w-1/3 inline-block px-1 align-center">
                        <FormItemCompt
                            class=""
                            labelFor="support"
                            labelTitle="">
                            <Checkbox v-model="item.support" :label="$t('users.support')" defaultColor="blue" :disabled="isFormDisabled"></Checkbox>
                            <ErrTextCompt :errs="errs && errs.support"></ErrTextCompt>
                        </FormItemCompt>
                        <FormItemCompt
                            class=""
                            labelFor="customer"
                            labelTitle="">
                            <Checkbox v-model="item.customer" :label="$t('users.customer')" defaultColor="blue" :disabled="isFormDisabled"></Checkbox>
                            <ErrTextCompt :errs="errs && errs.customer"></ErrTextCompt>
                        </FormItemCompt>                   
                    </div>
                    <div class="w-full md:w-1/3 inline-block px-1 align-center">
                        <FormItemCompt
                            class=""
                            labelFor="supportl1"
                            labelTitle="">
                            <Checkbox v-model="item.supportl1" :label="$t('users.supportl1')" defaultColor="blue" :disabled="isFormDisabled"></Checkbox>
                            <ErrTextCompt :errs="errs && errs.support"></ErrTextCompt>
                        </FormItemCompt>                 
                    </div>
                </div>
                <div class="text-right">
                    <FormItemCompt                    
                        labelFor="status"
                        labelTitle="">
                        <Checkbox v-model="item.status" :label="$t('users.status')" defaultColor="blue" :disabled="!hasScope('dev')"></Checkbox>
                        <ErrTextCompt :errs="errs && errs.status"></ErrTextCompt>
                    </FormItemCompt>
                </div>
            </template>
        </template>
        <div class="rounded border p-3" v-if="item && item.agent">
            <div class="text-xs font-bold text-gray-500">Agent Commission</div>
            <div>
                <FormItemCompt
                    class="w-full md:w-1/5 inline-block px-1 align-top"
                    labelFor="commissionrate"
                    :labelTitle="$t('users.commissionrate')">
                    <TextInput
                        name="commissionrate"
                        v-model="item.commissionrate"
                        type="number"
                        :placeholder="$t('users.commissionrate')"
                        defaultColor="blue"
                        :readonly="isFormDisabled"
                    ></TextInput>
                </FormItemCompt>

                <FormItemCompt
                    class="w-full md:w-1/5 inline-block px-1 align-top"
                    labelFor="commissiontype"
                    :labelTitle="$t('users.commissiontype')">
                    <select v-model="item.commissiontype" class="block border-0 p-4 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full" :disabled="isFormDisabled">
                        <option value="fixed">Fixed</option>
                        <option value="percentage">Percentage</option>
                    </select>
                </FormItemCompt>

                <FormItemCompt
                    class="w-full md:w-1/5 inline-block px-1 align-top"
                    labelFor="overwriterate"
                    :labelTitle="$t('users.overwriterate')">
                    <TextInput
                        name="overwriterate"
                        type="number"
                        v-model="item.commissionrate"
                        :placeholder="$t('users.overwriterate')"
                        defaultColor="blue"
                        :readonly="isFormDisabled"
                    ></TextInput>
                </FormItemCompt>

                <FormItemCompt
                    class="w-full md:w-1/5 inline-block px-1 align-top"
                    labelFor="commrepeat"
                    :labelTitle="$t('users.commrepeat')">
                    <TextInput
                        name="commrepeat"
                        type="number"
                        v-model="item.commrepeat"
                        :placeholder="$t('users.commrepeat')"
                        defaultColor="blue"
                        :readonly="isFormDisabled"
                    ></TextInput>
                </FormItemCompt>

                <FormItemCompt
                    class="w-full md:w-1/5 inline-block px-1 align-top"
                    labelFor="parentcommrepeat"
                    :labelTitle="$t('users.parentcommrepeat')">
                    <TextInput
                        name="parentcommrepeat"
                        type="number"
                        v-model="item.parentcommrepeat"
                        :placeholder="$t('users.parentcommrepeat')"
                        defaultColor="blue"
                        :readonly="isFormDisabled"
                    ></TextInput>
                </FormItemCompt>

                <FormItemCompt
                    class="w-full md:w-full inline-block px-1 align-top"
                    labelFor="agentparent"
                    :labelTitle="$t('users.agentparent')">
                    <UserAssignInput
                        :token="token"
                        :addUser="addAgentParent"
                        :removeUser="removeAgentParent"
                        :searchUser="searchAgentParent"
                        :users="agentparent"
                        :readonly="isFormDisabled"
                        :multiple="false"
                        :additionalParamsSearch="{agent: true, params: ['agent']}"
                        itemcls="w-full"
                        :plcHolder="$t('c.agentKeySearch')"
                        :noUserText="$t('c.noagent')"
                        :addText="$t('c.selectagent')"
                        defaultColor="blue"></UserAssignInput>
                </FormItemCompt>
            </div>
        </div>
      </form>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import SelectList from '@/components/cvui/form/SelectList.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import DateTimePicker from '@/components/cvui/form/DateTimePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import svgcollection from '@/components/cvui/svgcollection.vue'
import config from '../../../config'
import { auth2Store } from '../../../store/auth2-store'
import { getUserName, searchUser, initializeAutocountDebtor } from '../../../api'
import UserAssignInput from '../../../components/cvui/form/UserAssignInput.vue'
export default defineComponent({
    props: {
        item: { type: Object, required: true },
        errs: { type: Object, required: true },
        showMod: { type: Boolean, default: false },
        profile: { type: Object },
        token: { type: String, required: true },
        closeReload: { type: Function },
    },
    components: {
        FormItemCompt,
        TextInput,
        SelectList,
        Checkbox,
        DateTimePicker,
        ErrTextCompt,
        svgcollection,
        SelectOne,
        DatePicker,
        UserAssignInput
    },
    computed: {
        configscopes () {
            let array = []
            for (let i = 0; i < config.scopes.length; i++) {
                array.push({ id: config.scopes[i], value: config.scopes[i]})
            }
            return array
        },
        scopes () {
            let s: any = auth2Store.getState().profile

            return  s && s.scopes
        },
        isSupportL1 () {
            let p: any = this.scopes
            if (p.length === 1 && p.includes('supportl1')) {
                return true;
            }
            return false;
        },
        isAdmin () {
            let p: any = this.scopes
            if (p.includes('admin')) {
                return true;
            }
            return false;
        },
        isDev () {
            let p: any = this.scopes
            if (p.includes('dev')) {
                return true;
            }
            return false;
        },
        isFormDisabled () {
            if (this.isDev) {
                return false;
            }
            return this.item.acaccno != null || this.item.acaccno != ''
        },
    },
     mounted() {
        this.igender = this.item.gender == true ? 'm': 'f'
        this.agentparent = []
        if (this.item.agentparent && this.item.agentparent.trim().length > 0) {
            getUserName({token: this.token, id: this.item.agentparent}).then(res => {
                this.agentparent.push(res)
            })
        }
    },
    watch: {
        igender (p) {
            this.item.gender = p == 'm'
        }
    },
    methods: {
        syncAutocountDebtor () {
            if (confirm('Are you sure to sync autocount?')) {
                if (!this.item.id) {
                    alert('User ID is required to sync autocount debtor')
                    return
                }

                initializeAutocountDebtor({
                    userid: this.item.id,
                    token: this.token
                }).then((response: any) => {
                    // reload the user
                    alert('Autocount debtor sync completed successfully')
                    // reload user
                    console.log('Sync response:', response)
                    // close the form and reload user
                    if (this.closeReload) {
                        this.closeReload()
                    }
                }).catch((error: any) => {
                    console.error('Sync error:', error)
                    alert('Failed to sync autocount debtor: ' + (error.response?.data?.message || error.message))
                    // reload user
                    if (this.closeReload) {
                        this.closeReload()
                    }
                })
            }
        },
        hasScope (p: any) {
             return this.profile && this.profile.scopes && this.profile.scopes.indexOf(p) > -1
        },
        switchTitleString() {
            this.titleString = !this.titleString
        },
        selectTitle (item: any, index: any) {
            if (this.item.title === item.id) {
                this.item.title = ''
            } else {
                this.item.title = item.id
            }
        },
        preventsubmit (e: any) {
            e.preventDefault()
        },
        addRemoveScopes (p: any, i: Number) {
            if (!this.item.scopes) {
                this.item.scopes = []
            }
            let userindex = this.item.scopes.indexOf(p.ID || p.id)
            if (userindex == -1) {
                this.item.scopes.push(p.id)
            } else {
                this.item.scopes.splice(userindex, 1)
            }
        },
        addAgentParent (p: any) {
            if (p) {
                if (!this.item.agentparent) {
                    this.item.agentparent = ''
                }
                if (this.item.agentparent !== p.ID) {
                    this.item.agentparent = p.ID
                    this.agentparent.push(p)
                }
                this.showAgentParent = true
            }        
        },
        removeAgentParent (p: any) {
            if (p) {
                this.agentparent = []
                this.item.agentparent = ''
            }        
        },
    },
    data () {
        let titleString = false
        let igender: string = 'm'
        let titleList: any = config.titles
        let agentparent: any = []
        let showAgentParent: any = false
        return {
            titleString,
            igender,
            titleList,
            countries: config.countrieswithcode,
            agentparent,
            showAgentParent,
            searchAgentParent:searchUser,
        }
    }
})
</script>
