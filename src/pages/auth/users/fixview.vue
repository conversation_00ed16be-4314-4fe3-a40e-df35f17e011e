<template>
<PopupModal
    defaultColor="blue"
    modalWidthPercent="90"
    :title="$t('c.showfixbill')"
    :btnNoText="$t('c.close')"
    :btnNoFunction="cancel">
    
    <div class="m-2">
        <div class="text-sm border-b mb-2" :key="`item_${String(pi)}`" v-for="(p, pi) in item.subscriptions">
            {{ filterSubscribe(p, 'addressShort') }}
        </div>
        <div class="mb-2">
            <div class="inline-block">{{$t('billings.billdate')}} : </div>
            <div class="inline-block">&nbsp;{{ formatdate(item.billdate) }}</div>
        </div>
        <div>{{$t('billings.items')}}</div>
        <div class="rounded border p-2">
            <div v-for="(m, mi) in item.items" class=" text-sm border-b">
                <div class="w-2/3 inline-block">{{`${Number(mi) + 1}`}}. {{m.itemname}}</div>
                <div class="w-1/3 inline-block text-right">{{moneyformat(m.amount)}}</div>                
            </div>
        </div>
        <div>
            <FormItemCompt
                class="w-full md:w-1/4 inline-block px-1"
                labelFor="amountpaid"
                :labelTitle="$t('billings.amountpaid')"
                :required="true">
              <TextInput
                  name="amountpaid"
                  type="number"
                  v-model="item.amountpaid"
                  placeholder="0"
                  :readonly="true"
                  defaultColor="blue"></TextInput>
              <ErrTextCompt :errs="errs && errs.amountpaid"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt
                class="w-full md:w-1/4 inline-block px-1"
                labelFor="amountbf"
                :labelTitle="$t('billings.amountbf')"
                :required="true">
            <TextInput
                name="amountbf"
                type="number"
                v-model="item.amountbf"
                placeholder="0"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.amountbf"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt
                class="w-full md:w-1/4 inline-block px-1"
                labelFor="amountcf"
                :labelTitle="$t('billings.amountcf')"
                :required="true">
              <TextInput
                  name="amountcf"
                  type="number"
                  v-model="item.amountcf"
                  placeholder="0"
                  defaultColor="blue"></TextInput>
              <ErrTextCompt :errs="errs && errs.amountcf"></ErrTextCompt>
            </FormItemCompt>            
          </div>
          <div class="m-2">
            <div @click="fixnow" class="text-center bg-purple-500 text-white hover:bg-gray-500 rounded p-1 cursor-pointer">{{$t('c.showfixbill')}}</div>
          </div>
    </div>
</PopupModal>
    <!-- <div>{{subscriptions}}</div> -->
</template>
<script setup lang="ts">
import { ref, inject, defineProps, computed} from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import moment from 'moment';
import { updateBilling } from '../../../api';
const authStore: any = inject("authStore")
const authState = authStore.getState()
const token = computed(() => authState.token)
const props = defineProps({
  item: { type: Object, required: true },
  subscriptions: Array,
  cancel: { type: Function, required: true },
  reloadfunc: { type: Function, required: true },
})

const formatadd = (p: any) => {
      return p && p.address ? `${p.address.block}-${p.address.level}-${p.address.unit} ${p.address.building}` : '---'
}

const formatdate = (p: any) => {
    return moment(p).format('DD-MM-YYYY')
}

const moneyformat = (p: any) => {
    return p.toFixed(2)
}

const fixnow = () => {
    if (confirm('Are you sure to fix this bill?')) {
        let amountbf = 0
        let amountcf = 0
        let totalamount = props.item.amountcurrent
        if (props.item.amountbf) {
            amountbf = parseFloat(props.item.amountbf)
        }
        if (props.item.amountcf) {
            amountcf = parseFloat(props.item.amountcf)
        }
        totalamount += amountbf
        updateBilling({token: token.value, form: {totalamount: totalamount, amountbf: amountbf, amountcf: amountcf }, id: props.item.id}).then((r: any) => {
            props.reloadfunc()
            props.cancel()
        })
    }
}

const filterSubscribe = (p: string, show: string) => {
    if (props.subscriptions) {
        let m: any = props.subscriptions!.filter((k: any) => k.id == p)
        if (m && m.length > 0) {
            if (m[0].value) {
                return m[0].value
            } else {
                if (show == 'addressShort') {
                    return formatadd(m[0]['address'])
                } else {
                    return m[0][show]
                }
            }
            
        }
        
    }
    return ''    
}

const errs: any = ref({})
</script>