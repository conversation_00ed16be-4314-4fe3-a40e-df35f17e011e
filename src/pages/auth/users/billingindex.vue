<template>
  
     <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('users.billings')"
      :btnNoText="$t('c.close')"
      :btnNoFunction="cancel"
      v-if="item">

      <template v-if="fixviewitem">
        <fixview :reloadfunc="reload" :item="fixviewitem" :subscriptions="subscriptions" :cancel="cancelFixView" />
      </template>

      <div class="border-b p-1 px-5 rounded bg-gray-100">
        <div class="inline-block text-lg "> {{item.email}}</div>
        <div class="inline-block mx-2 text-md rounded bg-blue-300 px-2" v-if="item.name">{{item.name}}</div>
      </div>
      <div class="p-2">
        <div class="text-right ">
          <button v-if="subscribefilter" @click="selectfilter('')" class="text-xs rounded px-2 py-1 bg-blue-400 hover:bg-blue-600 text-white mr-2">{{$t('c.clearfilter')}}</button>
          <button @click="checkPayments" :disabled="checkingPayments" class="text-xs rounded px-2 py-1 mr-2" :class="checkingPayments ? 'bg-gray-300 cursor-not-allowed' : 'bg-purple-300 hover:bg-purple-400'">Check payment</button>
          <button v-if="subGroup" @click="doBulkPayment()" class="text-xs rounded px-2 py-1 bg-yellow-300 hover:bg-yellow-400 mr-2">{{showBulkPayment ? 'Cancel Bulk Payment' : 'Bulk Payment'}}</button>
          <button @click="loadSubscriptionsList" class="text-xs rounded px-2 py-1 bg-blue-400 hover:bg-blue-600 text-white mr-2">{{$t('c.getsubscriptions')}}</button>
          <button @click="loadDatabase" class="text-xs rounded px-2 py-1 bg-green-200 hover:bg-green-400">{{$t('c.reload')}}</button>
          <button class="text-xs rounded px-2 py-1 bg-gray-200 hover:bg-gray-400">Sync</button>
        </div>
        <div class="inline-block mr-5"> <input type="checkbox" v-model="showId" />&nbsp;{{$t('c.showid')}}</div>
        <div class="inline-block mr-5"> <input type="checkbox" v-model="showBillNo" />&nbsp;{{$t('c.showbillno')}}</div>
        <div class="inline-block mr-5"> <input type="checkbox" v-model="showUnit" />&nbsp;{{$t('c.showunit')}}</div>
        <div class="inline-block mr-5"> <input type="checkbox" v-model="showFixBill" />&nbsp;{{$t('c.showfixbill')}}</div>
      </div>
      <div v-if="showsublist" class="m-2">
        <div class="mb-1">{{$t('c.highlight')}} <input class="border shadow rounded p-1" v-model="subscriptionHighlight" /></div>
        <div @click="selectfilter(sc.id)" class="text-xs py-1 px-3 ml-1 mb-1 cursor-pointer rounded inline-block" :class="filterItem(sc.id) + ' ' + highlightCss(formatadd(sc) + ' ' + sc.sid)" :key="sc.id" v-for="sc in subscriptions">{{formatadd(sc)}} {{ sc.sid }}</div>
      </div>
      <div class="border-b py-1" v-if="showBulkPayment">
        <div class="flex justify-between items-center gap-4">
          <div class="w-full md:w-1/5 inline-block px-1">
            <MonthPicker
              v-model="selectedMonth"
              :clearable="true"
              defaultColor="blue"></MonthPicker>
          </div>
          <button v-if="checkboxlist.length > 1" @click="bulkPayment()" class="flex items-center h-8 px-3 bg-pink-500 hover:bg-pink-700 text-white rounded">
            <svgicon icon="creditcard" dclass="inline-block w-4 h-4 mr-1" /> 
            Do Bulk Payment
          </button>
        </div>
      </div>
      <div class="">
        <div v-if="databases == null">
          <dloading />
        </div>
        <template v-else>
          <div class="text-xs my-2 ml-10 flex justify-between" v-if="showBillNo">
            <div class="mb-1">
              {{$t('c.billnoFilter')}} <input class="ml-3 border shadow rounded p-2 text-md" v-model="billnoFilter" />
              <button @click="reload" class="inline-block h-8 px-5 bg-yellow-500 hover:bg-yellow-700 text-white ml-2 rounded">{{$t('c.filter')}}</button>
            </div>
            <div class="w-full md:w-1/5 inline-block px-1">
              <MonthPicker
                v-model="selectedMonth"
                :clearable="true"
                defaultColor="blue"></MonthPicker>
            </div>
            <div class="mb-1 flex items-center gap-2">
              <p>Items per page</p>
              <select @change="reload" class="mr-4 border shadow rounded p-2 text-md" v-model="limit">
                <option value=10>10</option>
                <option value=25>25</option>
                <option value=50>50</option>
              </select>
            </div>
          </div>
          <div class="px-6 pt-4 overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th v-if="showBulkPayment && selectedMonth" class="px-5 py-3 text-sm text-center">
                    <input type="checkbox" @change="checkboxFunc('checkall','')" :checked="databases && databases.data && checkboxlist.length === databases.data.length">
                  </th>
                  <th v-if="showId" class="px-5 py-3 text-sm text-center">{{$t('billings.id')}}</th>
                  <th v-if="showUnit" class="px-5 py-3 text-sm text-center">{{$t('billings.subscriptions')}}</th>
                  <th v-if="showBillNo" class="px-5 py-3 text-sm text-center">{{$t('billings.billno')}}</th>
                  <th class="px-5 py-3 text-sm text-center">{{$t('billings.billdate')}}</th>
                  <th class="px-5 py-3 text-sm text-center">{{$t('billings.total')}}</th>
                  <th class="px-5 py-3 text-sm text-center">{{$t('billings.duedate')}}</th>
                  <th class="px-5 py-3 text-sm text-center">{{$t('billings.paid')}}</th>
                  <th v-if="showCheckPayments" class="px-5 py-3 text-sm text-center">Sum Payments</th>
                  <th class="px-5 py-3 text-sm text-center">{{$t('c.action')}}</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(row, index) in (databases && databases.data || [])" :key="row.id">
                  <td v-if="showBulkPayment && selectedMonth" class="px-5 py-3 text-sm text-center">
                    <input type="checkbox" @change="checkboxFunc('checkone', row)" :checked="Array.isArray(checkboxlist) && checkboxlist.indexOf(row) > -1">
                  </td>
                  <td v-if="showId" class="px-5 py-3 text-sm text-center">{{ row.id }}</td>
                  <td v-if="showUnit" class="px-5 py-3 text-sm text-center">{{ subscriptionsLabel(row.subscriptions) }}</td>
                  <td v-if="showBillNo" class="px-5 py-3 text-sm text-center">{{ row.billno }}</td>
                  <td class="px-5 py-3 text-sm text-center">{{ formatDate(row.billdate) || '-/-/-' }}</td>
                  <td class="px-5 py-3 text-sm text-center">{{ Number(row.totalamount).toFixed(2) }}</td>
                  <td class="px-5 py-3 text-sm text-center">{{ formatDate(row.duedate) || '-/-/-' }}</td>
                  <td class="px-5 py-3 text-sm text-center">{{ Number(row.amountpaid).toFixed(2) }}</td>
                  <td v-if="showCheckPayments" class="px-5 py-3 text-sm text-center">
                    <div v-if="hasPaid(row) && paymentCheckResults[row.id]" class="mt-2 text-xs text-left">
                      <div class="inline-block px-2 py-1 rounded" :class="Math.abs(Number(row.amountpaid || row.paid || 0) - Number(paymentCheckResults[row.id].sum)) < 0.01 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
                        {{ formatMoney(paymentCheckResults[row.id].sum || 0) }}
                        <!-- Sum payments: {{ formatMoney(paymentCheckResults[row.id].sum || 0) }}
                        <span class="ml-1">(n={{ paymentCheckResults[row.id].count || 0 }})</span>
                        <span class="ml-2" v-if="Math.abs(Number(row.amountpaid || row.paid || 0) - Number(paymentCheckResults[row.id].sum)) < 0.01">Match</span>
                        <span class="ml-2" v-else>Mismatch vs billed paid: {{ formatMoney(Number(row.amountpaid || row.paid || 0)) }}</span> -->
                      </div>
                    </div>
                  </td>
                  <td class="px-5 py-3 text-sm text-center">
                    <button @click="printPDF(row, index)" class="inline-block h-8 pr-5 bg-blue-500 hover:bg-blue-700 text-white mr-2 rounded-full">
                      <svgicon icon="print" dclass="inline-block w-4 h-4 mx-2" /> PDF
                    </button>
                    <button v-if="isCurrentMonth(row.billdate) || true" @click="payment(row, index != (databases && databases.total-1))" class="inline-block h-8 pr-5 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
                      <svgicon icon="cash" dclass="inline-block w-4 h-4 mx-2" /> Payment
                    </button>
                    <button v-if="showSend" @click="sendbill(row)" class="inline-block h-8 pr-5 bg-yellow-500 hover:bg-yellow-700 text-white mr-2 rounded-full">
                      <svgicon icon="invoice" dclass="inline-block w-4 h-4 mx-2" /> Send Bill x
                    </button>
                    <button v-if="showFixBill" @click="fixbillView(row)" class="inline-block h-8 pr-5 bg-indigo-500 hover:bg-indigo-700 text-white mr-2 rounded-full">
                      <svgicon icon="edit" dclass="inline-block w-4 h-4 mx-2" /> {{ $t('c.showfixbill') }}
                    </button>
                    <qrcode-vue :id="`qrcode_${row.id}`" class="hidden mb-8" :value="'https://paymentapi.highfi.com.my/api/payex/' + row.id" level="M" render-as="canvas" />
                    <br/>
                    <a target="_blank" :href="payexpaylink(row.id)" v-if="index == 0"  >
                      <div class="mt-5 bg-purple-500 text-white hover:bg-gray-500 rounded p-1 cursor-pointer">{{$t('c.upaylink')}}</div>
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <dpagination
              :total="databases && databases.total || 0"
              :page="table.page"
              :limit="table.limit"
              :pageChange="pageChange"
              defaultColor="blue"
          />
        </template>
        <div class="text-center pt-10" v-if="showTxt">
          {{showTxt.item && showTxt.item.billno}}
          <div @click="sendNow" class="bg-gray-300 hover:bg-blue-500 hover:text-white rounded my-2 cursor-pointer text-center p-1">
            Send Now
          </div>
        </div>
        <div :class="isDev ? '' : 'invisible'"><input type="checkbox" v-model="showSend" /> Show Send</div>
      </div>
    </PopupModal>
    <template v-if="paymentitem && item">
      <paymentindex :item="paymentitem" :cancel="cancelpayment" :hideAddPayment="hideAddPayment" :user="item" />
    </template>
    <template v-if="bulkpaymentitem && item">
      <bulkpaymentform :savesilentfunc="savesilentfunc" :token="token" :item="bulkpaymentitem" :cancel="closePaymentItem" :submitfunc="createPayment" />
    </template>
</template>
<script lang="ts">
import { defineComponent, inject,computed } from 'vue'
import type { Font } from '@pdfme/common';
import QrcodeVue from 'qrcode.vue'
import PopupModal from '@/components/cvui/Modal.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import { auth2Store } from '../../../store/auth2-store'
import { billingStore } from '../../../store/billing-store'
import { generate } from '@pdfme/generator'
import { PDFDocument } from 'pdf-lib';
import invTemplate from '../billings/template/invoice_v2.json'
// import invTemplate2 from '../billings/template/invoice_v3.json'
import multiInvoice from '../billings/template/multi_invoice.json'
import multiInvoice2 from '../billings/template/multi_invoice2.json'
import multiInvoice3 from '../billings/template/multi_invoice3.json'
import { imageBase64 } from '@/assets/logo_base64.js';
import paymentindex from './paymentindex.vue'
import bulkpaymentform from './bulkpaymentform.vue';
import moment from 'moment'
import fixview from './fixview.vue'
import { createPayment, fixHFCDBilling, getBillings, getSubscription, getSubscriptionGroups, getSubscriptions, sendBill, getPayments } from '../../../api'
import DatePicker from '../../../components/cvui/form/DatePicker.vue';
import FormItemCompt from '../../../components/cvui/form/FormItem.vue';
import MonthPicker from '../../../components/MonthPicker.vue';
import { paymentStore } from '../../../store/payment-store';
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    // const billingState = billingStore.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        billingStore: billingStore,
        billingState: billingStore.getState(),
        isDev: computed(() => auth2State.profile.scopes && auth2State.profile.scopes.includes('dev')),
    }
  },
  mounted () {   
    document.addEventListener('keydown', this.handleEscKey);
    (async function (t: any) {
      const kk: Font = {
        Arial: {
          data: await fetch('/fonts/Arial/Arial2.ttf').then((res) => res.arrayBuffer()),
          fallback: true,
        },
        Effra: {
          data: await fetch('/fonts/Effra/Effra.ttf').then((res) => res.arrayBuffer()),
        },
        EffraBold: {
          data: await fetch('/fonts/Effra/EffraBold.ttf').then((res) => res.arrayBuffer()),
        },
        Gotham: {
          data: await fetch('/fonts/Gotham/GothamMedium.ttf').then((res) => res.arrayBuffer()),
        },
        GothamBold: {
            data: await fetch('/fonts/Gotham/GothamBold.ttf').then((res) => res.arrayBuffer()),
        },
        Helvetica: {
          data: await fetch('/fonts/Helvetica/Helvetica.ttf').then((res) => res.arrayBuffer()),
        },
        HelveticaBold: {
          data: await fetch('/fonts/Helvetica/HelveticaBold.ttf').then((res) => res.arrayBuffer()),
        },
        // PTSerifBold: {
        //     data: await fetch('/fonts/PTSerif/PTSerif-Bold.ttf').then((res) => res.arrayBuffer()),
        // },
            
      };
      t.font = kk;
    })(this);

    this.reload()
    this.isSubGroup()
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  components: {
    PopupModal,
    dpagination,
    dloading,
    svgicon,
    paymentindex,
    bulkpaymentform,
    fixview,
    QrcodeVue,
    imageBase64,
    FormItemCompt,
    DatePicker,
    MonthPicker
  },
  props: {
    item: Object,
    title: String,
    cancelFunc: Function,
    submitFunc: Function
  },
  methods: {
    fixhfcd (id: string) {
      if (confirm('Are you sure to fix this bill?')) {
        fixHFCDBilling({token: this.token, id: id}).then((r: any) => {
            this.reload()
        })
      }
    },
    isSubGroup () {
      if (this.item){
        getSubscriptionGroups({ user: this.item.id, token: this.token }).then((res: any) => {
          if (res.total > 0) {
            this.subGroup = true
          }
        })
      }
    },
    isCurrentMonth (date: string) {
      return moment(date).isSame(moment(), 'month')
    },
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        this.cancel()
      }
    },
    payexpaylink (id: string) {
      return `https://paymentapi.highfi.com.my/api/payex/${id}`
    },
    cancelFixView () {
      this.fixviewitem = undefined
    },
    highlightCss (p: string) {
      if (this.subscriptionHighlight.trim().length > 0 && p.toLowerCase().indexOf(this.subscriptionHighlight.toLowerCase()) > -1) {
        return 'border-2 border-red-600'
      } else {
        return ''
      }
    },
    filterItem (id: string) {
      return (this.subscribefilter && id && this.subscribefilter == id) ? 'bg-blue-600 text-white hover:bg-blue-400' : 'bg-gray-600 text-white hover:bg-gray-400'
    },
    selectfilter (id: string) {
      this.subscribefilter = id
      this.reload()
    },
    loadSubscriptionsList () {
      if (this.item) {
        this.showsublist = true
        this.subscriptions = []
        let params: any = ['customer']
        let customer: any = this.item!.id
        let filter: any = {token: this.token, limit: 10, params: params, customer: customer}
        this.loadss(filter)
      }      
    },
    loadss (filter: any) {
      getSubscriptions(filter).then((res: any) => {
          if (res && res.data) {
            // console.log(res.data)
            this.subscriptions = this.subscriptions.concat(res.data.map((k:any) => {
              k["value"] = this.formatadd(k)
              return k
            }))
            if (res.total > this.subscriptions.length) {
              filter.skip = this.subscriptions.length
              this.loadss(filter)
            }
          }
      })
    },
    fixbillView (p: any) {
      this.fixviewitem = JSON.parse(JSON.stringify(p))
    },
    cancel () {
      if (this.cancelFunc) {
        this.cancelFunc()
      }
    },
    sendNow () {
      if (confirm('double confirm to send?')) {
        // console.log(this.showTxt)
        sendBill({data: this.showTxt, token: this.token})
        this.showTxt = ''
      }
    },
    submitNow () {
      if (this.submitFunc) {
        this.submitFunc()
      }
    },
    payment (p: any, hi: any) {
      this.paymentitem = p
      this.hideAddPayment = hi
    },
    cancelpayment () {
      this.paymentitem = null
    },
    sendbill (p: any) {
      let h: any = JSON.parse(JSON.stringify(this.item))
      let g: any = {
        "item": JSON.parse(JSON.stringify(p)),
        "customer": {
          "email": h.email,
          "name": h.name,
          "title": h.title,
          "contact": h.contact,
        },
      }

      if (p && p.subscriptions && p.subscriptions.length > 0 ) {
        let sid: string = p.subscriptions[0]
        getSubscription({ token: this.token, id: sid }).then((res: any) => {
          let r = res && res.data
          g.item["deposit"] = r.deposit
          g["billingaddress"] = r.billingaddress && r.billingaddress.address
          g["sub"] = r
          if (r.address && r.address["block"]) {
            g["customer"]["blockinfo"] = `${r.address.building||'The Grand Subang Jaya SS15'}  ${r.address.block}-${r.address.level}-${r.address.unit}`
          }            
          this.sendbillC(g)
        })
      } else {
        g.item["deposit"] = 0
        g["billingaddress"] = ""        
        this.sendbillC(g)
      }
      
    },
    // upaylink (id: string) {
    //   return this.$router.resolve({
    //     name: 'upaylink',
    //     params: {id}
    //   }).href
    // },
    sendbillC(g: any) {
      this.showTxt = g
    },
    printPDF (p: any, i: number) {
      if (p && p.subscriptions && p.subscriptions.length > 0 ) {
        let sid: string = p.subscriptions[0]
        getSubscription({ token: this.token, id: sid }).then((res: any) => {
          let r = res && res.data
          this.printPDF2(p, i, r)
        })
      } else {
        this.printPDF2(p, i, null)
      }
    },
    printPDF2 (p: any, i: number, sub: any) {
      const template: any = invTemplate;
      const c: any = this.item

      let amts = ''
      let itms = ''

      // items
      p.items.forEach((item: any, index: number) => {
        itms += `${item.itemname} \n`
        // get \n count in item.itemname
        let ncount = item.itemname.split(/\r?\n/)

        amts += `${this.formatMoney(item.amount)} \n`
        if (ncount.length > 1) {
          for (let i = 1; i < ncount.length; i++) {
            amts += '\n'
          }
        }
      })

      var canvas = document.getElementById("qrcode_" + p.id) as HTMLCanvasElement;

      if(canvas){
        var pngDataUrl = canvas.toDataURL("image/png");
      } else {
        var pngDataUrl = ''
      }
      
      const inputs = [{
        "logo": imageBase64,
        // "jompaylogo": jompayBase64,
        "jompaylogo": '',
        // "billercode": "630566",
        "billercode": "",
        "companyAddress": "A1-02-07, Level 2, Block A1, Sunway GEO Avenue\nJalan Lagoon Selatan, Sunway South Quay, Bandar Sunway\n47500 Subang Jaya",
        "itemTitle": "INVOICE",
        "customerNameWtTitleGreeting": `Dear ${c.title && c.title.toUpperCase() || ''} ${c.name}`,
        "sidLabel": "Subscriber ID",
        "sid": `${sub.sid || ''}`,
        // "sid2": `${sub.sid || ''}`,
        "sid2": ``,
        "companyName": "HIIFI (M) SDN. BHD. (1406319-P)",
        "customerNameWtTitle": `${c.title && c.title.toUpperCase()  || ''} ${c.name}`,
        "summaryTitle": "Here's a summary bill for month " + this.shortMonthFormat(p.billdate),
        "billLabel": "Bill No",
        // "userLabel": "User",
        "billdateLabel": "Bill Date",
        "duedateLabel": "Due Date",
        // "creditLabel": "",
        // "credit": "",
        "billNo": p.billno || p.id,
        "user": c.email,
        // "addonTitle": this.addOnTitle(sub),
        "billDate": this.formatDate(p.billdate),
        "dueDate": (p.duedate ? this.formatDate(p.duedate) : '-/-/-' ),
        // "Line": "   ",
        // "Line2": "   ",
        // "Line3": "   ",
        "customerAddress": `${sub.address.address || '---'}`,
        "page": "Page 1/1",
        // "Line4": "   ",
        // "Line5": "   ",
        "empty": " ",
        "empty2": " ",
        "itemsLabel": "Items",
        "amountLabel": "Amount (MYR)",
        "items": itms,
        "amounts": amts,
        "subtotalLabel": "Subtotal",
        "taxLabel": "Tax",
        "bfLabel": "B/F",
        "subtotal": this.formatMoney(p.amountcurrent),
        "tax": this.formatMoney(p.taxcurrent),
        "bf": this.formatMoney(p.amountbf),
        "totalLabel": "Total Amount Payable",
        "totalLabel2": "Total ",
        "total": `${this.formatMoney(p.totalamount)}`,
        "total2": `${this.formatMoney(p.totalamount)}`,
        "empty3": " ",
        // "footerLabel1": "Notes:",
        // "footerLabel2": "1. All remittance shoud be made payable to :-\n    HIIFI(M) SDN. BHD.\n    CIMB **********\n\n2. Please include the invoice number at the recipient reference.\n3. Please share the bank in slip to HIIFI Support + 6019-719 9799\n",
        "footerLabel1": "",
        "footerLabel2": "",
        "footerfooter1": "Make your payment via FPX",
        "footerfooter2": `https://paymentapi.highfi.com.my/api/payex/${p.id}`,
        "qrcode": pngDataUrl
      }];
      if (template && inputs) {
        generate({ template, inputs, options: {font: this.font} }).then((pdf: any) => {
          // Browser
          const blob = new Blob([pdf.buffer], { type: 'application/pdf' });
          window.open(URL.createObjectURL(blob));

          // Node.js
          // fs.writeFileSync(path.join(__dirname, `test.pdf`), pdf);
        });
      }
    },
    async printConInv (startDate: any, endDate: any) {
      const customerId = this.item?.id;
      let list: any[] = [];
      let skip = 0
      const pdfBuffers: any[] = [];

      if (customerId) {        
        const fetchBills = async () => {
          await getBillings({ token: this.token, customer: customerId, skip }).then(async (res: any) => {            
            if (res.data && res.data.length > 0) {
              list = list.concat(res.data);
              skip += res.limit;
              if (res.total > skip) {
                await fetchBills();
              } else {
                const perPage = 7
                const totalPaid = list.reduce((sum: number, invoice: any) => { return sum + invoice.amountpaid; }, 0).toFixed(2);
                const totalSum = list.reduce((sum: number, invoice: any) => { return sum + invoice.totalamount; }, 0).toFixed(2);
                const totalCurrent = list.reduce((sum: number, invoice: any) => { return sum + invoice.amountcurrent; }, 0).toFixed(2);
                const totalTax = list.reduce((sum: number, invoice: any) => { return sum + invoice.taxcurrent; }, 0).toFixed(2);
                const totalBf = list.reduce((sum: number, invoice: any) => { return sum + invoice.amountbf; }, 0).toFixed(2);

                for (let i = 0; i < list.length; i += perPage) {
                  let pdfBuffer;
                  
                  if (i === 0) {
                    const listPerPage = list.slice(i, i + perPage);
                    pdfBuffer = await this.printConInv2(listPerPage, totalSum);
                  } else if (i + perPage >= list.length) {
                    const listPerPage = list.slice(i, i + perPage);
                    pdfBuffer = await this.printConInvLastPage(listPerPage, totalPaid, totalSum, totalCurrent, totalTax, totalBf);
                  }
                  else {
                    const listPerPage = list.slice(i, i + perPage + 3);
                    pdfBuffer = await this.printConInv3(listPerPage);
                  }
                  pdfBuffers.push(pdfBuffer);
                }
                await this.mergeAndOpenPDF(pdfBuffers)
              }
            }
          })
        }
        await fetchBills();
      }
    },
    async printConInv2 (p: any, totalSum: any) {
      const template: any = multiInvoice;
      const c: any = this.item

      let amts = ''
      let itms = ''
      
      // items
      p.forEach((invoice: any) => {               
        itms += `${invoice.items[0].itemname} \n \n`
        let ncount = invoice.items[0].itemname.split(/\r?\n/)

        amts += `${this.formatMoney(invoice.totalamount)} \n \n`
        if (ncount.length > 1) {
          for (let i = 1; i < ncount.length; i++) {
            amts += '\n'
          }
        }
      })
      
      const inputs = [{
        "logo": imageBase64,
        "empty0": " ",
        "companyAddress": "A1-02-07, Level 2, Block A1, Sunway GEO Avenue\nJalan Lagoon Selatan, Sunway South Quay, Bandar Sunway\n47500 Subang Jaya",
        "itemTitle": "INVOICE",
        "customerNameWtTitleGreeting": `Dear ${c.title && c.title.toUpperCase() || ''} ${c.name}`,
        "companyName": "HIIFI (M) SDN. BHD. (1406319-P)",
        "customerNameWtTitle": `${c.title && c.title.toUpperCase()  || ''} ${c.name}`,
        "summaryTitle": "Here's a summary bill for month " + this.shortMonthFormat(p.billdate),
        "user": c.email,
        "billDate": this.formatDate(p.billdate),
        "empty": " ",
        "empty2": " ",
        "itemsLabel": "Items",
        "amountLabel": "Amount (MYR)",
        "items": itms,
        "amounts": amts,
        "totalLabel": "Total Amount",
        "total": `RM${this.formatMoney(totalSum)}`,
      }];

      const pdfBuffer = await this.generatePDFBuffer(template, inputs);
      return pdfBuffer;
    },
    async printConInv3 (p: any) {
      const template: any = multiInvoice2;
      const c: any = this.item

      let amts = ''
      let itms = ''
      
      // items
      p.forEach((invoice: any) => {               
        itms += `${invoice.items[0].itemname} \n \n`
        let ncount = invoice.items[0].itemname.split(/\r?\n/)

        amts += `${this.formatMoney(invoice.totalamount)} \n \n`
        if (ncount.length > 1) {
          for (let i = 1; i < ncount.length; i++) {
            amts += '\n'
          }
        }
      })
      
      const inputs = [{
        "logo": imageBase64,
        "empty0": " ",
        "empty2": " ",
        "itemsLabel": "Items",
        "amountLabel": "Amount (MYR)",
        "items": itms,
        "amounts": amts,
      }];
      const pdfBuffer = await this.generatePDFBuffer(template, inputs);
      return pdfBuffer;
    },
    async printConInvLastPage (p: any, totalPaid: any, totalSum: any, totalCurrent: any, totalTax: any, totalBf: any) {
      const template: any = multiInvoice3;
      const c: any = this.item

      let amts = ''
      let itms = ''
      
      // items
      p.forEach((invoice: any) => {               
        itms += `${invoice.items[0].itemname} \n \n`
        let ncount = invoice.items[0].itemname.split(/\r?\n/)

        amts += `${this.formatMoney(invoice.totalamount)} \n \n`
        if (ncount.length > 1) {
          for (let i = 1; i < ncount.length; i++) {
            amts += '\n'
          }
        }
      })
      
      const inputs = [{
        "logo": imageBase64,
        "empty0": " ",
        "empty2": " ",
        "emptyLine": " ",
        "itemsLabel": "Items",
        "amountLabel": "Amount (MYR)",
        "items": itms,
        "amounts": amts,
        "taxLabel": "Tax",
        "bfLabel": "B/F",
        "tax": this.formatMoney(totalTax),
        "bf": this.formatMoney(totalBf),
        "totalLabel2": "Total ",
        "total2": `${this.formatMoney(totalSum)}`,
      }];
      const pdfBuffer = await this.generatePDFBuffer(template, inputs);
      return pdfBuffer;
    },
    async generatePDFBuffer(template: any, inputs: any) {
      return generate({ template, inputs, options: { font: this.font } })
        .then((pdf: any) => {
          return pdf.buffer;
        });
    },
    async mergeAndOpenPDF(pdfBuffers: any[]) {
      const mergedPdf = await PDFDocument.create();

      for (let pdfBuffer of pdfBuffers) {
        const pdf = await PDFDocument.load(pdfBuffer);
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach((page) => mergedPdf.addPage(page));
      }

      const mergedPdfBuffer = await mergedPdf.save();
      const blob = new Blob([mergedPdfBuffer], { type: 'application/pdf' });
      window.open(URL.createObjectURL(blob));
    },
    formatMoney (p: any) {
      if (p != null) {
        return p.toFixed(2)
      } else {
        return '0.00'
      }
    },
    removeDuplicate (arraylist: any) {
      return arraylist = [...new Set(arraylist)]
    },
    getSubscribtionList () {
      let subscriptionslist2 = this.subscriptions.map((p: any) => p.id)
      let subscriptions:any = []
      if (this.databases) {
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i].subscriptions
          subscriptions = subscriptions.concat(data)
        }
        subscriptions = this.removeDuplicate(subscriptions)
        subscriptions = subscriptions.filter((p: any) => subscriptionslist2.indexOf(p) === -1)
        subscriptions.filter((k:any) => {
          getSubscription({ token: this.token, id: k }).then((rs) => {
            this.subscriptions.push({
              id: k,
              value: this.formatadd(rs.data),
            })
          })
        })
      }
    },
    formatadd (p: any) {
      return p && p.address ? `${p.address.block}-${p.address.level}-${p.address.unit} ${p.address.building}` : '---'
    },
    subscriptionsLabel (subs: any[]) {
      if (!subs || subs.length === 0) return '---'
      // Map ids to display string using this.subscriptions (already fetched)
      const map: Record<string,string> = {}
      this.subscriptions.forEach((s: any) => { map[s.id] = s.value || s.id })
      return subs.map((id: string) => map[id] || id).join(', ')
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.selectedMonth = null
      this.table.limit = this.limit
      !this.showBulkPayment
      this.loadDatabase()
    },
    shortMonthFormat (p: any) {
      let r = ''
      if (p) {
        r = moment(p).format('MM/YY')
      }
      return r
    },
    formatDate (p: any) {
      let r = ''
      if (p) {
        r = moment(p).format('DD/MM/YYYY')
      }
      return r
    },
    loadDatabase () {
      this.showCheckPayments = false
      this.paymentCheckResults = {}
      if (this.item) {
        if (this.subscribefilter) {
          let p = {...this.table, token: this.token, customer: this.item.id, subscriptions: [this.subscribefilter] }

          if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
            p["billno"] = this.billnoFilter.trim()
            if (!p["params"]) {
              p["params"] = []
            }
            p["params"].push("billno")
          }

          this.billingStore.getBillings(p)
        } else {
          let p = {...this.table, token: this.token, customer: this.item.id }

          if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
            p["billno"] = this.billnoFilter.trim()
            if (!p["params"]) {
              p["params"] = []
            }
            p["params"].push("billno")
          }

          if (this.selectedMonth) {
            const selectedDate = new Date(this.selectedMonth)

            const day = String(selectedDate.getDate()).padStart(2, '0');
            const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
            const year = selectedDate.getFullYear();

            const formattedDate = `${day}-${month}-${year}`;
            p.$billdate = formattedDate
          }

          this.billingStore.getBillings(p)
        }
        
      }         
    },
    pageChange (p:any) {
      this.table.page = p
      this.loadDatabase()
    },
    addOnTitle (s: any) {
      let c: any = this.item
      return s ? `${s.address['building'] || 'The Grand Subang Jaya SS15'} ${s.address['block']}-${s.address['level']}-${s.address['unit']}\n${s.contact}` : `${c.contact}`
    },
    checkboxFunc (type: any, sitem: any) {
      if (type === 'checkall') {
        if (this.checkboxlist.length === this.databases.data.length) {
          this.checkboxlist = []
        } else {
          this.checkboxlist = [...this.databases.data]
        }
      } else if (type === 'checkone') {
        let index = this.checkboxlist.findIndex((k: any) => k.id === sitem.id)
        if (index >= 0) {
          this.checkboxlist.splice(index, 1)
        } else {
          this.checkboxlist.push(sitem)
        }
      }
    },
    doBulkPayment () {
      this.showBulkPayment = !this.showBulkPayment

      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      this.selectedMonth = firstDayOfMonth
      this.se
    },
    hasPaid (bill: any) {
      const paid = Number(bill.amountpaid || bill.paid || 0)
      return paid > 0
    },
    async checkPayments () {
      if (!this.databases) return
      this.checkingPayments = true
      const bills = this.databases.data.filter((b: any) => this.hasPaid(b))
      try {
        const results = await Promise.all(bills.map(async (b: any) => {
          try {
            const res: any = await getPayments({ token: this.token, bill: b.id, status: true })
            const sum = (res.data || []).reduce((acc: number, p: any) => acc + Number(p.amount || 0), 0)
            const count = (res.data || []).length
            return { id: b.id, sum, count }
          } catch (e) {
            return { id: b.id, sum: 0, count: 0, error: true }
          }
        }))
        const map: any = {}
        results.forEach((r: any) => { map[r.id] = { sum: r.sum, count: r.count, error: r.error } })
        this.paymentCheckResults = map
      } finally {
        this.checkingPayments = false
        this.showCheckPayments = true
      }
    },
    bulkPayment () {
      const billIds = this.checkboxlist.map((bill: any) => bill.id);
      const totalAmount = this.checkboxlist.reduce((sum: any, bill: any) => sum + bill.totalamount, 0);
      const singleAmount = this.checkboxlist.map((bill: any) => bill.totalamount);
      
      this.bulkpaymentitem = {
        "customer": this.checkboxlist[0].customer, 
        "bills": this.checkboxlist,
        "bill": billIds, 
        "amount": totalAmount,
        "single_amount": singleAmount,
        "paymentdate": new Date(), 
        "remark": "",
        "statustxt": "paid", 
        "platform": "manual", 
        "platformid": ""
      }
    },
    savesilentfunc (p: any) {
      p.bill.forEach((bill: any, index: any) => {
        p.bill = bill
        p.amount = p.single_amount[index]
        createPayment({ token: this.token, form: p })
      });
    },
    createPayment (p: any) {
      // create payment
      p.bill.forEach((bill: any, index: any) => {
        p.bill = bill
        p.amount = p.single_amount[index]
        paymentStore.createPayment({ token: this.token, form: p })   
      });
      this.closePaymentItem()
      this.loadDatabase()
    },
    closePaymentItem () {
      this.bulkpaymentitem = undefined
    },
  },
  computed: {
    columns () {
      let subscriptions: any = this.subscriptions
      let ar: any = [
        { title: 'billings.billdate', key: 'billdate', type: 'date', class: 'text-center' },
        { title: 'billings.total', key: 'totalamount', type: 'price', class: 'text-center' },        
        { title: 'billings.duedate', key: 'duedate', type: 'date', class: 'text-center' },        
        { title: 'billings.paid', key: 'amountpaid', type: 'price', class: 'text-center' },
        // { title: 'billings.subscriptions', key: 'subscriptions', objectlist: subscriptions, type: 'objectarray', class: 'text-center' },
        { title: 'c.extra', key: 'extra', type: 'extra', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]      
      if (this.showBillNo) {
        ar.unshift({ title: 'billings.billno', key: 'billno', type: 'text', class: 'text-center' })
      }
      if (this.showUnit) {
        ar.unshift({ title: 'billings.subscriptions', key: 'subscriptions', objectlist: subscriptions, type: 'objectarray', class: 'text-center' })
      }
      if (this.showId) {
        ar.unshift({ title: 'billings.id', key: 'id', type: 'text', class: 'text-center' })
      }
      if (this.showBulkPayment && this.selectedMonth) {
        ar.unshift({ title: '', key: 'id', type: 'checkbox', checkall: true, class: 'text-center' })
      }
      return ar 
    },
    databases () {
      return billingStore.getState().billings
    }
  },
  watch: {
    databases (p: any) {
      if (p) {
        this.getSubscribtionList()
      }
    },
    selectedMonth(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loadDatabase();
      }
    },
  },  
  data() {
    return {
      table: { page: 1, keywords: '' },
      paymentitem: undefined,
      fixviewitem: undefined,
      keywords: '',
      showBillNo: true,
      showId: false,
      showTxt: '',
      showSend: false,
      subscription: undefined,
      hideAddPayment: true,
      showUnit: false,
      showFixBill: false,
      subscriptions: [],
      showsublist: false,
      subscribefilter: undefined,
      subscriptionHighlight: '',
      billnoFilter: '',
      font: null,
      startDate: null,
      endDate: null,
      showBulkPayment: false,
      selectedMonth: null,
      selectedBulkPayment: [],
      checkboxlist: [],
      bulkpaymentitem: null,
      subGroup: false,
      limit: 10,
      paymentCheckResults: {},
      checkingPayments: false,
      showCheckPayments: false
    }
  }
})
</script>