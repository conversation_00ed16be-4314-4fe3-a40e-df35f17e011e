<template>
    <div class="text-sm text-gray-700 border-b pl-5">{{ $t('users.subscriptions') }}</div>
    <div class="m-2">
        <div v-if="databases == null">
            <dloading />
        </div>
        <template v-else>
            <dtable :columns="columns" :data="databases" columnColor="white" />
            <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
                :pageChange="pageChange" defaultColor="blue" />
        </template>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed } from 'vue'
import { subscriptionStore } from '../../../store/subscription-store'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import { getPlan } from '../../../api'
export default defineComponent({
    props: {
        userid: { type: String, required: true },
        token: {
            type: String,
            required: true
        }
    },
    components: {
        dtablesearch,
        dtable,
        dpagination,
        dloading,
        svgicon
    },
    methods: {
        getListChecker() {
            this.getPlanList()
        },
        searchNow() {
            this.table.page = 1
            this.table.keywords = this.keywords
            this.loadDatabase()
        },
        searchFunc(p: string) {
            this.keywords = p
            this.searchNow()
        },
        reload() {
            this.table.page = 1
            this.table.keywords = ''
            this.keywords = ''
            this.table.name = ''
            this.customer = this.userid
            this.table.customer = this.userid
            this.table.statustxt = ''
            this.loadDatabase()
        },
        loadDatabase() {
            let p = { ...this.table, token: this.token }
            subscriptionStore.getSubscriptions(p)
        },
        pageChange(p: any) {
            this.table.page = p
            this.loadDatabase()
        },
        getPlanList() {
            let planidlist2 = this.planlist.map((p: any) => p.id)
            let planidlist = []
            if (this.databases) {
                for (let i = 0; i < this.databases.data.length; i++) {
                    let data = this.databases.data[i]
                    planidlist.push(data.plan)
                }
                planidlist = this.removeDuplicate(planidlist)
                planidlist = planidlist.filter((p: any) => planidlist2.indexOf(p) === -1)
                planidlist.filter(k => {
                    getPlan({ token: this.token, id: k }).then((rs) => {
                        this.planlist.push({
                            id: k,
                            value: rs.data.title || k + ' (' + this.$t('c.noTitle') + ')'
                        })
                    })
                })
            }
        },
        removeDuplicate(arraylist: any) {
            return arraylist = [...new Set(arraylist)]
        },
    },
    computed: {
        databases() {
            return subscriptionStore.getState().subscriptions
        },
        subscriptionCreaten() {
            return subscriptionStore.getState().subscriptionCreate
        },
        subscriptionCreateSuccess() {
            return subscriptionStore.getState().subscriptionCreateSuccess
        },
        subscriptionCreateError() {
            return subscriptionStore.getState().subscriptionCreateError
        },
        subscriptionUpdate() {
            return subscriptionStore.getState().subscriptionUpdate
        },
        subscriptionUpdateSuccess() {
            return subscriptionStore.getState().subscriptionUpdateSuccess
        },
        subscriptionUpdateError() {
            return subscriptionStore.getState().subscriptionUpdateError
        },
        subscriptionDeleteSuccess() {
            return subscriptionStore.getState().subscriptionDeleteSuccess
        },
    },
    watch: {
        databases(p, o) {
            if (p && p != o) {
                this.getListChecker()
            }
        }
    },
    mounted() {
        this.reload()
    },
    data() {
        let table: any = {
            limit: 10,
            page: 1,
            keywords: '',
            customer: '',
        }
        let keywords: string = ''
        let customer: string = this.userid
        let subscriptions: any = []
        let planlist: any = []
        let statuslist: any = [
            {
                id: 'pendinginstall',
                value: this.$t('subscriptions.pendinginstall')
            },
            {
                id: 'new',
                value: this.$t('subscriptions.new')
            },
            {
                id: 'terminated',
                value: this.$t('subscriptions.terminated')
            },
            {
                id: 'active',
                value: this.$t('subscriptions.active')
            }
        ]
        return {
            table,
            keywords,
            planlist,
            columns: computed(() => {
                let userlist = this.userlist
                let planlist = this.planlist
                return [
                    { title: 'subscriptions.sid', key: 'sid', type: 'string', class: 'text-center' },
                    { title: 'subscriptions.plan', key: 'plan', type: 'string', objectlist: planlist, class: 'text-center' },
                    { title: 'subscriptions.activationDate', key: 'activationdate', type: 'date', class: 'text-center' },
                    { title: 'subscriptions.status', key: 'statustxt', type: 'string', objectlist: statuslist, class: 'text-center' },
                ]
            }),
            customer,
            subscriptions,
            statuslist
        }
    }
})
</script>