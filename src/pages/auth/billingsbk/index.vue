<template>
  <div>
    <adminheader :title="$t('navigations.billings')"></adminheader>
    <tableheader
        :title="$t('billings.listTitle')"
        :addFunction="addBilling"></tableheader>
    <dtablesearch></dtablesearch>
    <dtable
        :columns="columns"
        :data="billings"></dtable>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import tableheader from '@/components/table/TableHeader.vue'
import dtablesearch from '@/components/table/TableSearch.vue'
import dtable from '@/components/table/Table.vue'
export default defineComponent({
  components: {
    adminheader,
    tableheader,
    dtablesearch,
    dtable
  },
  computed: {
    columns () {
      return [
        { title: 'billings.invoiceID', key: 'invoiceID', type: 'string', class: 'text-center' },
        { title: 'billings.date', key: 'date', type: 'date', class: 'text-center' },
        { title: 'billings.code', key: 'code', type: 'string', class: 'text-center' },
        { title: 'billings.status', key: 'status', type: 'string', class: 'text-center' },
        { title: 'billings.price_rm', key: 'price', type: 'string', class: 'text-center' },
      ]
    },
    billings () {
      let list = [
        { invoiceID: 'M063592DR2', date: '08.04.2021', code: '5928MD01', status: 'Unpaid', price: '2500.00' },
        { invoiceID: 'M063592DR2', date: '08.04.2021', code: '5928MD01', status: 'Completed', price: '2500.00' },
        { invoiceID: 'M063592DR2', date: '08.04.2021', code: '5928MD01', status: 'Completed', price: '2500.00' },
        { invoiceID: 'M063592DR2', date: '08.04.2021', code: '5928MD01', status: 'Completed', price: '2500.00' },
        { invoiceID: 'M063592DR2', date: '08.04.2021', code: '5928MD01', status: 'Completed', price: '2500.00' }
      ]
      return {
        data: list,
        total: list.length
      }
    }
  },
  methods: {
    addBilling () {}
  }
})
</script>
