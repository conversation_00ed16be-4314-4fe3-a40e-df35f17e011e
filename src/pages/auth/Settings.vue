<template>
    <form @submit.prevent="preventsubmit" autocomplete="" class="p-6">
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="title"
            :labelTitle="$t('users.title')">
            <!-- <span class="cursor-pointer text-xs hover:bg-red-200 bg-blue-300 rounded px-2 py-1" @click="switchTitleString"><svgcollection icon="signal" dclass="inline-block w-3 h-3" /> {{$t('users.others')}}</span> -->
            <template v-if="titleString">
              <TextInput
                name="title"
                v-model="profile.title"
                :placeholder="$t('c.frontPlc') + $t('users.title')"
                defaultColor="blue" readonly></TextInput>
            </template>
            <div v-else class="py-3">
              <SelectOne
                :list="titleList"
                :selectedItem="profile.title"
                showTitle="title"
                itemValue="id"
                :addOrRemoveFunction="selectTitle"
                defaultColor="blue" disabled></SelectOne>
            </div>
          <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full inline-block px-1"
            labelFor="name"
            :labelTitle="$t('users.name')"
            :required="true">
          <TextInput
              name="name"
              v-model="profile.name"
              :placeholder="$t('c.frontPlc') + $t('users.name')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="identityno"
            :labelTitle="$t('users.identityNo')" :required="true">
          <TextInput
              name="identityno"
              v-model="profile.identityno"
              :placeholder="$t('c.frontPlc') + $t('users.identityNo')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.identityno"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="email"
            :labelTitle="$t('users.email')"
            :required="true">
          <TextInput
              name="email"
              class="bg-gray-100"
              v-model="profile.email"
              :placeholder="$t('c.frontPlc') + $t('users.email')"
              defaultColor="blue" readonly :required="true"></TextInput>
          <ErrTextCompt :errs="errs && errs.email"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/3 inline-block px-1"
            labelFor="dob"
            :labelTitle="$t('users.dob')">
          <DatePicker
            v-model="profile.dob"
            :clearable="true"
            defaultColor="blue" disabled></DatePicker>
          <ErrTextCompt :errs="errs && errs.dob"></ErrTextCompt>
        </FormItemCompt>
        <div class="w-full md:w-2/3 inline-block px-1">
            <FormItemCompt
                class="w-full md:w-2/3 inline-block px-1"
                labelFor="nationality"
                :labelTitle="$t('users.nationality')">
                <div><select v-model="profile.nationality" class="p-4 rounded" disabled>
                    <option v-for="(p, g) in countries" :key="`nation_${g}`" :value="p.code">{{p.name}}</option>
                </select></div>
                <ErrTextCompt :errs="errs && errs.nationality"></ErrTextCompt>
            </FormItemCompt>
            <FormItemCompt
                class="w-full md:w-1/3 inline-block px-1"
                labelFor="gender"
                :labelTitle="$t('users.gender')">
                <div>
                    <input v-model="igender" class="mr-2" type="radio" value="m" disabled>
                    <label class="mr-10 text-xs" for="html">{{$t('c.male')}}</label>
                    <input v-model="igender" class="mr-2" type="radio" value="f" disabled>
                    <label class="text-xs" for="css">{{$t('c.female')}}</label>
                </div>
                <ErrTextCompt :errs="errs && errs.gender"></ErrTextCompt>
            </FormItemCompt>
        </div>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="contact"
            :labelTitle="$t('users.contact')">
          <TextInput
              name="contact"
              v-model="profile.contact"
              :placeholder="$t('c.frontPlc') + $t('users.contact')"
              defaultColor="blue" readonly :required="true"></TextInput>
          <ErrTextCompt :errs="errs && errs.contact"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="contact2"
            :labelTitle="$t('users.contact2')">
          <TextInput
              name="contact2"
              v-model="profile.contact2"
              :placeholder="$t('c.frontPlc') + $t('users.contact2')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.contact2"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="mothersName"
            :labelTitle="$t('users.mothersName')">
          <TextInput
              name="mothersName"
              v-model="profile.mothersname"
              :placeholder="$t('c.frontPlc') + $t('users.mothersName')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.mothersname"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full md:w-1/2 inline-block px-1"
            labelFor="fathersName"
            :labelTitle="$t('users.fathersName')">
          <TextInput
              name="fathersName"
              v-model="profile.fathersname"
              :placeholder="$t('c.frontPlc') + $t('users.fathersName')"
              defaultColor="blue" readonly></TextInput>
          <ErrTextCompt :errs="errs && errs.fathersname"></ErrTextCompt>
        </FormItemCompt>
        <div id="footer" class="ml-auto mt-3">
                <!-- <button
                    @click="submitNow()"
                    class="text-white font-bold py-2 px-4 rounded mr-3"
                    :class="`bg-blue-500 hover:bg-blue-700`">
                  Submit
                </button> -->
            </div>
      </form>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import { auth2Store } from '../../store/auth2-store'
import { userStore } from '../../store/user-store'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import SelectList from '@/components/cvui/form/SelectList.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import DateTimePicker from '@/components/cvui/form/DateTimePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import svgcollection from '@/components/cvui/svgcollection.vue'
import config from '../../config'
export default defineComponent({
    setup () {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        const auth2State = auth2Store.getState()
        return {
            token: computed(() => authState.token),
            profile: computed(() => auth2State.profile ),
            userStore: userStore,
            userState: userStore.getState()
        }
    },
    props: {
        errs: { type: Object, required: true },
        showMod: { type: Boolean, default: false },
    },
    components: {
        FormItemCompt,
        TextInput,
        SelectList,
        Checkbox,
        DateTimePicker,
        ErrTextCompt,
        svgcollection,
        SelectOne,
        DatePicker
    },
    computed: {
        configscopes () {
            let array = []
            for (let i = 0; i < config.scopes.length; i++) {
                array.push({ id: config.scopes[i], value: config.scopes[i]})
            }
            return array
        }
    },
     mounted() {
        this.igender = this.profile.gender == true ? 'm': 'f'
    },
    watch: {
        igender (p) {
            this.profile.gender = p == 'm'
        }
    },
    methods: {
        hasScope (p: any) {
             return this.profile && this.profile.scopes && this.profile.scopes.indexOf(p) > -1
        },
        switchTitleString() {
            this.titleString = !this.titleString
        },
        selectTitle (profile: any, index: any) {
            if (this.profile.title === profile.id) {
                this.profile.title = ''
            } else {
                this.profile.title = profile.id
            }
        },
        preventsubmit (e: any) {
            e.preventDefault()
        },
        validateForm () {
            let p = true
            if (!this.profile.email || this.profile.email.trim().length == 0) {
                this.errs['email'] = 'c.fieldRequired'
                p = false
            }
            if (!this.profile.name || this.profile.name.trim().length == 0) {
                this.errs['name'] = 'c.fieldRequired'
                p = false
            }
            if (!this.profile.identityno || this.profile.identityno.trim().length == 0) {
                this.errs['identityno'] = 'c.fieldRequired'
                p = false
            }
            return p
        },
        submitNow (e: any) {
            if (this.validateForm()) {
                if (this.save != null) {
                    this.save(this.profile)
                }
            }
            return false
        },
        save (p: any) {
            if (p.id) {
                this.userStore.updateUser({ form: p, id: p.id, token: this.token })
            } else {
                this.userStore.createUserByAdmin({ form: p, token: this.token })
            }
        },
    },
    data () {
        let titleString = false
        let igender: string = 'm'
        let titleList: any = config.titles
        
        return {
            titleString,
            igender,
            titleList,
            countries: config.countrieswithcode,      
        }
    }
})
</script>
