<template>
    <div>
        <div>

        </div>
        <p v-html="template2"></p>
    </div>
</template>
<script setup lang="ts">
import { ref,computed } from 'vue';
const email: any = ref('')
const plan: any = ref('')
const subscriber: any = ref('')
const params: any = ref(['%subscriber%', '%plan%'])
const title:any = ref('🎉 Welcome to Blazing-Fast Internet with HighFi! 🚀')
const template: any = ref('Hi %subscriber%,\
We\'re absolutely thrilled to welcome you to HighFi! 🌟 Get ready to surf, stream, and play with  %plan% lightning speed thanks to your new fiber internet connection.\
\n\nHere\'s what you can look forward to:\n\n\
📡 Ultra-Fast Speeds: Whether you’re streaming your favourite shows in HD or battling it out in online games, your new HighFi connection is built to handle it all with ease.\
🔄 Unlimited Data: Dive deep into the digital ocean with absolutely no data caps — the internet is yours to explore!\
🛡️ Reliable Connection: Your online experience should be seamless and smooth. That\'s why we at HighFi strive to ensure maximum uptime and minimal interruptions.\
🌐 Top-Notch Support: Got a question? Need some help? Our friendly HighFi team is here to support you every step of the way. Reach out anytime! +60 19-719 9799 | https://www.highfi.com.my/contact-us/\
\n\n\
Getting Started:\n\
Your setup is all done, and you\'re just a click away from stepping into the fast lane. To get connected, simply follow these steps:\n\n\
1. Turn on your HighFi modem and router.\
2. Connect your device via Wi-Fi or an Ethernet cable.\
3. Open your browser and start flying across the web!\
Remember, our customer service team is always just a phone call or WhatsApp away if you have any questions.\
\n\n\
We can’t wait to see what you do with your new supercharged connection. Welcome aboard, and happy browsing!\n\
\n\
Warmest regards,\n\
HighFi, Always Satisfied\n\
+60 19-719 9799\n\
Find us @ \n\
Website: highfi.com.my\n\
Email: <EMAIL>\n\
FB: fb.com/highfi.com.my\n\
IG: @HighFi_my\n\
\n\
P.S. Love staying ahead of tech trends? Follow us on our social media for the latest updates, tips, and special offers!')

const template2 = computed(() => {
    let result = template.value
    // replace %subscriber%
    result = result.replace('%subscriber%', subscriber.value)
    // replace %plan%
    result = result.replace('%plan%', plan.value)

    return result
})

</script>