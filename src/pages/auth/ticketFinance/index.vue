<template>
  <div>
    <adminheader
        :title="$t('navigations.financeTickets')"
        :addFunction="addTicket"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div class="mx-6 flex justify-end">
      <select v-model="statustxt" @change="filterFunc"
        class="text-sm placeholder-gray-500 border rounded bg-white p-2 focus:ring-blue-500">
        <option value="">Show All</option>
        <option value="new">New</option>
        <option value="pending">Pending</option>
        <option value="processing">Processing</option>
        <option value="rejected">Rejected</option>
        <option value="completed">Completed</option>
      </select>
    </div>
    <div v-if="databases === null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
            <button @click="editRow(slotProps.item)" :title="$t('c.edit')" class="inline-block bg-blue-500 hover:bg-blue-700 text-white p-2 mr-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
            </button>
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :save="saveFunc"
            :profile="profile"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { ticketStore } from '../../../store/ticket-store'
import { getUserName, getFinanceTicket, getSubscription } from '../../../api'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        ticketStore: ticketStore,
        crossStore: crossStore
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    svgCollection
  },
  mounted () {
    if (this.$route.params.id) {
      let ticketId = this.$route.params.id
      getFinanceTicket({token: this.token, id: ticketId}).then((res: any) => {
          this.editRow(res.data)
      }).catch((err: any) => {
          console.error(err);
      })
    }
    this.reload()
  },
  data () {
    let prof : any = auth2Store.getState().profile
    let item: any = null
    let deleteItem : any = null
    let itemStyle: any = {
      type: 'refund',
      attachments: [],
      subscription: '',
      status: 'new',
      comments: [],
      remark: '',
      amount: 0,
      date_requested: new Date(),
      date_completed: null
    }
    let table: any = {
        limit: 10,
        page: 1,
        keywords: ''
    }
    let keywords: string = ''
    let userlist: any = []
    let statustxt: string = ''
    let subscriptions: any[] = []
    return {
      prof,
      item,
      deleteItem,
      itemStyle,
      table,
      keywords,
      userlist,
      subscriptions,
      statustxt
    }
  },
  methods: {
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    searchFunc(p: string) {
      this.keywords = p
      this.searchNow()
    },
    filterFunc() {
      this.table.page = 1;
      this.table.status = this.statustxt;
      this.loadDatabase();
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.table.status = this.statustxt;
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      this.ticketStore.getFinanceTickets(p)
    },
    pageChange (p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = this.itemStyle
    },
    addTicket () {
      this.item = JSON.parse(JSON.stringify(this.itemStyle))
      this.item.date_requested = new Date()
    },
    editRow (item: any) {
      this.item = JSON.parse(JSON.stringify(item))
    },
    cancelNow () {
      this.item = null
    },
    saveFunc (item: any) {
      if (item.id) {
        this.ticketStore.updateFinanceTicket({ form: item, id: item.id, token: this.token })
      } else {
        this.ticketStore.createFinanceTicket({ form: item, token: this.token })
      }
      this.reload()
    },
    getSubscribtionList () {
      const subsFromTickets = (this.databases && this.databases.data) ? this.databases.data.map((row: any) => row.subscription).filter((x: any) => !!x) : []
      const unique = this.removeDuplicate(subsFromTickets)
      const existingIds = this.subscriptions.map((p: any) => p.id)
      const toFetch = unique.filter((id: any) => existingIds.indexOf(id) === -1)
      toFetch.forEach((id: any) => {
        getSubscription({ token: this.token, id }).then((rs: any) => {
          const sid = rs && rs.data ? (rs.data.sid || rs.data.id || id) : id
          this.subscriptions.push({ id, value: sid })
        }).catch(() => {
          // fallback to id if fetch fails
          this.subscriptions.push({ id, value: id })
        })
      })
    },
    removeDuplicate (arr: any[]) {
      return Array.from(new Set(arr))
    },
  },
  computed: {
    columns () {
      return [
        { title: 'tickets.ticketNo', key: 'finance_ticket_no', type: 'string', class: 'text-center' },
        { title: 'tickets.type', key: 'type', type: 'string', class: 'text-center' },
        { title: 'tickets.subscription', key: 'subscription', type: 'string', objectlist: this.subscriptions, class: 'text-center' },
        { title: 'tickets.status', key: 'status', type: 'string', class: 'text-center' },
        { title: 'tickets.dateRequested', key: 'date_requested', type: 'date', class: 'text-center' },
        { title: 'tickets.dateCompleted', key: 'date_completed', type: 'date', class: 'text-center' },
        { title: 'tickets.amount', key: 'amount', type: 'number', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return ticketStore.getState().financeTickets
    },
    financeTicketCreate () {
      return ticketStore.getState().financeTicketCreate
    },
    financeTicketCreateSuccess () {
      return ticketStore.getState().financeTicketCreateSuccess
    },
    financeTicketCreateError () {
      return ticketStore.getState().financeTicketCreateError
    },
    financeTicketUpdate () {
      return ticketStore.getState().financeTicketUpdate
    },
    financeTicketUpdateSuccess () {
      return ticketStore.getState().financeTicketUpdateSuccess
    },
    financeTicketUpdateError () {
      return ticketStore.getState().financeTicketUpdateError
    },
    financeTicketDeleteSuccess () {
      return ticketStore.getState().financeTicketDeleteSuccess
    },
  },
  watch: {
    databases (p) {
      if (p && p.data) { this.subscriptions = []; this.getSubscribtionList() }
    },
    financeTicketCreateSuccess (p) {
      if (p) {
        this.item = null
        this.crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.financeTicket'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    financeTicketCreateError (p) {
      if (p) {
        this.crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.financeTicket'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    financeTicketUpdateSuccess (p) {
      if (p) {
        this.item = null
        this.crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.financeTicket'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    financeTicketUpdateError (p) {
      if (p) {
        this.crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.financeTicket'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    financeTicketDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  }
})
</script>