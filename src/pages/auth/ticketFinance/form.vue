<template>
<PopupModal
    defaultColor="blue"
    modalWidthPercent="90"
    :title="$t('tickets.financeFormTitle')"
    :btnYesText="$t(item.id ? 'tickets.update' : 'tickets.submitTicket')"
    :btnYesFunction="submitNow"
    :btnNoText="$t('c.cancel')"
    :btnNoFunction="cancel"
    v-if="item">
    <form @submit.prevent="preventsubmit" autocomplete="">
        <div v-if="item.id" class="mb-2">
            <div
                class="mr-2 inline-block rounded bg-gray-100 px-5 py-1 text-sm font-bold text-blue-600">
              {{item.finance_ticket_no}}
            </div>
            <div
                class="mr-2 inline-block rounded bg-gray-100 px-5 py-1 text-sm font-bold text-blue-600">
              {{item.id}}
            </div>
        </div>
        <div class="text-xs flex items-center">
          <p>Date Requested</p>
          <p class="ml-10 p-1 bg-gray-200 rounded-md">{{ item.date_requested ? formattedDate(item.date_requested): formattedDate(moment())  }}</p>
        </div>
        <div v-if="item.date_completed" class="text-xs flex items-center mt-2">
          <p>Date Completed</p>
          <p class="ml-10 p-1 bg-green-200 rounded-md">{{ formattedDate(item.date_completed) }}</p>
        </div>
            <div>
                <div>
                  <FormItemCompt
                      class="w-full md:w-1/3 inline-block px-1"
                      labelFor="type"
                      :labelTitle="$t('tickets.type')"
                      :required="true">
                    <div>
                      <select v-model="item.type" class="form-select p-2 rounded block w-full border">
                        <option v-for="type in typeList" :key="type.id" :value="type.id">{{ type.value }}</option>
                      </select>
                      <ErrTextCompt :errs="errs && errs.type"></ErrTextCompt>
                    </div>
                  </FormItemCompt>
                  <FormItemCompt
                      class="w-full md:w-1/3 inline-block px-1"
                      labelFor="status"
                      :labelTitle="$t('tickets.amount')"
                      :required="true">
                    <TextInput
                        name="amount"
                        type="number"
                        v-model="item.amount"
                        :placeholder="$t('c.frontPlc') + $t('tickets.amount')"
                        defaultColor="blue"></TextInput>
                      <ErrTextCompt :errs="errs && errs.amount"></ErrTextCompt>
                  </FormItemCompt>
                  <FormItemCompt
                      class="w-full md:w-1/3 inline-block px-1"
                      labelFor="status"
                      :labelTitle="$t('tickets.status')"
                      :required="true">
                    <div>
                      <select v-model="item.status" class="form-select p-2 rounded block w-full border">
                        <option v-for="status in statusList" :key="status.id" :value="status.id">{{ status.value }}</option>
                      </select>
                      <ErrTextCompt :errs="errs && errs.status"></ErrTextCompt>
                    </div>
                  </FormItemCompt>
                </div>
                <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="subscription"
                    :labelTitle="$t('tickets.subscription')"
                    :required="true">
                    <div>
                      <SelectDb
                          v-model="item.subscription"
                          :getItem="getSubscriptionById"
                          :itemValue="formatSubscriptionDisplay"
                          :searchItems="searchSubscriptions"
                          placeHolder="c.searchSubscription"
                          inputClass="w-full px-2 py-2 border rounded" />
                      <ErrTextCompt :errs="errs && errs.subscription"></ErrTextCompt>
                    </div>
                </FormItemCompt>
                <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="remark"
                    :labelTitle="$t('tickets.remark')"
                    :required="true">
                    <TextareaInput
                        name="remark"
                        v-model="item.remark"
                        :placeholder="$t('c.frontPlc') + $t('tickets.remark')"
                        defaultColor="blue"
                        :rows="5"></TextareaInput>
                    <ErrTextCompt :errs="errs && errs.remark"></ErrTextCompt>
                </FormItemCompt>
                <FormItemCompt
                    labelFor="attachments"
                    :labelTitle="$t('subscriptions.attachments')">
                  <UploadInput
                      :attachments="attachments"
                      :removeFile="removeFile"
                      :addFile="addFile"
                      :basePath="basePath"
                      :token="token"></UploadInput>
                </FormItemCompt>

                <!-- Comments Section -->
                <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="comments"
                    :labelTitle="$t('tickets.comments')">
                    <div class="border rounded p-3 bg-gray-50 max-h-60 overflow-y-auto">
                        <div v-if="item.comments && item.comments.length > 0">
                            <div v-for="(comment, index) in item.comments" :key="index" class="mb-3 p-2 bg-white rounded border">
                                <div class="text-xs text-gray-500 mb-1">
                                    <strong>{{ comment.author }}</strong> - {{ formattedDate(comment.timestamp) }}
                                </div>
                                <div class="text-sm">{{ comment.message }}</div>
                            </div>
                        </div>
                        <div v-else class="text-gray-500 text-sm">No comments yet</div>
                    </div>

                    <!-- Add new comment -->
                    <div class="mt-3">
                        <TextareaInput
                            name="newComment"
                            v-model="newComment"
                            :placeholder="$t('tickets.addComment')"
                            defaultColor="blue"
                            :rows="2"></TextareaInput>
                        <button
                            @click="addComment"
                            :disabled="!newComment.trim()"
                            class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300">
                            {{ $t('tickets.addComment') }}
                        </button>
                    </div>
                </FormItemCompt>
            </div>
    </form>
</PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { getFile, deleteFile, basePath, getSubscriptions, getSubscription } from '../../../api'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import UserAssignInput from '@/components/cvui/form/UserAssignInput.vue'
import UploadInput from '@/components/cvui/form/UploadInput.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import SelectDb from '@/components/selectdb.vue'
import moment from 'moment'

export default defineComponent({
    name: 'FinanceTicketForm',
    components: {
        PopupModal,
        FormItemCompt,
        ErrTextCompt,
        TextInput,
        TextareaInput,
        UserAssignInput,
        UploadInput,
        DatePicker,
        SelectDb
    },
    props: {
        item: {type: Object, required: true },
        cancel: {type: Function, required: true },
        token: {type: String, required: true },
        save: {type: Function, required: true },
        profile: {type: Object, required: true }
    },
    computed: {
        profileIsCustomer () {
            return this.profile && this.profile.customer
        },
        isCompleted () {
            return this.item && this.item.status === 'completed'
        }
    },
    data () {
        let attachments: any = []
        let errs: any = {}
        let typeList: any = [
            {id: 'refund', value: 'Refund'},
            {id: 'others', value: 'Others'}
        ]
        let statusList: any = [
            {id: 'new', value: 'New'},
            {id: 'pending', value: 'Pending'},
            {id: 'processing', value: 'Processing'},
            {id: 'rejected', value: 'Rejected'},
            {id: 'completed', value: 'Completed'}
        ]
        let newComment: string = ''
        return {
            errs,
            basePath,
            attachments,
            typeList,
            statusList,
            newComment,
            moment
        }
    },
    mounted () {
      if (this.item) {
        this.attachments = []
        this.getAttachment()
      }
    },
    methods: {
        getAttachment () {
            if (this.item.attachments && this.item.attachments.length > 0) {
                for (let i = 0; i < this.item.attachments.length; i++) {
                    let attachmentId = this.item.attachments[i]
                    getFile({id: attachmentId, token: this.token}).then((res: any) => {
                        this.attachments.push(res.data)
                    })
                }
            }
        },
        // start attachments
        addFile (p: any) {
          if (!this.item.attachments) {
          this.item.attachments = []
          }
          this.item.attachments.push(p.file.id)
          this.attachments.push(p.file)
        },
        removeFile (p: any) {
          let i = this.item.attachments.indexOf(p)
          if (i > -1) {
            this.item.attachments.splice(i, 1)
          }
          let list = this.attachments.map((p2: any) => p2.id || p2.ID)
          let j = list.indexOf(p)
          this.attachments.splice(j, 1)
          deleteFile({id: p, token: this.token})
        },
        // end attachments
        addComment () {
          if (this.newComment.trim()) {
            if (!this.item.comments) {
              this.item.comments = []
            }
            this.item.comments.push({
              timestamp: new Date(),
              author: this.profile.name || 'User',
              message: this.newComment.trim()
            })
            this.newComment = ''
          }
        },
        addRemoveType (item: any) {
          if (this.item.type === item.id) {
            this.item.type = ''
          } else {
            this.item.type = item.id
          }
        },
        addRemoveStatus (item: any) {
          if (this.item.status === item.id) {
            this.item.status = ''
          } else {
            this.item.status = item.id
            // Set completion date when status is completed
            if (item.id === 'completed' && !this.item.date_completed) {
              this.item.date_completed = new Date()
            } else if (item.id !== 'completed') {
              this.item.date_completed = null
            }
          }
        },
        getSubscriptionById (id: string) {
          return getSubscription({ token: this.token, id: id })
        },
        searchSubscriptions (keywords: string) {
          return getSubscriptions({
            token: this.token,
            limit: 10,
            page: 1,
            keywords: keywords
          })
        },
        formatSubscriptionDisplay (subscription: any) {
          // Format subscription display similar to other components
          if (subscription.address) {
            return `${subscription.sid || subscription.id} - ${subscription.address.block}-${subscription.address.level}-${subscription.address.unit} ${subscription.address.building}`
          } else if (subscription.sid) {
            return `${subscription.sid} - ${subscription.customer || 'Unknown Customer'}`
          } else {
            return `${subscription.id || subscription.ID} - ${subscription.customer || 'Unknown Customer'}`
          }
        },
        validateForm () {
          let p = true
          if (!this.item.type || this.item.type.trim().length == 0) {
            this.errs['type'] = 'c.fieldRequired'
            p = false
          }
          if (!this.item.subscription || this.item.subscription.trim().length == 0) {
            this.errs['subscription'] = 'c.fieldRequired'
            p = false
          }
          if (!this.item.remark || this.item.remark.trim().length == 0) {
            this.errs['remark'] = 'c.fieldRequired'
            p = false
          } else if (this.item.remark.length < 10) {
            this.errs['remark'] = 'c.fieldNotLessThan10'
            p = false
          }
          if (!this.item.status || this.item.status.trim().length == 0) {
            this.errs['status'] = 'c.fieldRequired'
            p = false
          }
          return p
        },
        preventsubmit () {
          this.submitNow()
        },
        submitNow() {
          if (this.validateForm()) {
            this.save(this.item)
          }
          return false
        },
        formattedDate(date: any) {
          const newdate = new Date(date);

          return newdate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });
        }
    }
})
</script>