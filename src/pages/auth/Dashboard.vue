<template>
  <div>
    <adminheader :title="$t('navigations.dashboard')"></adminheader>
    <div v-if="summary1" class="p-5 bg-white mt-2">
      <div class="w-full text-white" v-if="isAdmin">
        <div class="inline-block w-full md:w-1/3">
          <div class="" :class="box1">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="users" dclass="inline-block w-5 h-5 mx-1" />
              {{$t('c.activesubscriptions')}}</div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.activesubscriptions}}
              <div style="font-size: 12px;" class="font-bold inline-block"> / {{summary1.all}}</div>
            </div>          
          </div>
        </div>
        <div class="inline-block w-full md:w-1/3">
          <div class="" :class="box1">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="report" dclass="inline-block w-6 h-6 mx-1" />
              {{$t('c.pendinginstall')}}</div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.pendinginstall}}
            </div>          
          </div>
        </div>
        <div class="inline-block w-full md:w-1/3">
          <div class="" :class="box1">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="report" dclass="inline-block w-6 h-6 mx-1" />
              {{$t('c.newsubscribemonth')}}</div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.newsubscribe || 0}}
            </div>          
          </div>
        </div>
        <div class="inline-block w-full md:w-1/3">
          <div class="" :class="box1">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="users" dclass="inline-block w-5 h-5 mx-1" />
              {{$t('c.currentmonthbill')}}</div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.currentmonthbillspaid || 0}}
              <div style="font-size: 12px;" class="font-bold inline-block"> / {{summary1.currentmonthbills}}</div>
            </div>          
          </div>
        </div>
        <div class="inline-block w-full md:w-1/3">
          <div @click="showPaymentBreakdownModal" class="cursor-pointer transform transition-all duration-200 hover:scale-105 hover:shadow-lg" :class="clickableBox">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="report" dclass="inline-block w-6 h-6 mx-1" />
              {{$t('c.totalcollected')}}
              <svgicon icon="arrowright" dclass="inline-block w-4 h-4 ml-2 opacity-70" />
            </div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.totalcurrentmonthpaid || 0}}
            </div>
          </div>
        </div>
        <div class="inline-block w-full md:w-1/3">
          <div class="" :class="box1">
            <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
              <svgicon icon="report" dclass="inline-block w-6 h-6 mx-1" />
              {{$t('c.totalsuspended')}}</div>
            <div class="text-center py-2 pb-5 text-5xl">
              {{summary1.suspended || 0}}
            </div>          
          </div>
        </div>
      </div>
      <div v-if="profile.installer && summary1.installer" class="inline-block w-full md:w-1/3">
        <div class="" :class="box1">
          <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
            <svgicon icon="users" dclass="inline-block w-5 h-5 mx-1" />
            Pending Installation</div>
          <div class="text-center py-2 pb-5 text-5xl text-white">
            {{summary1.installer.pendinginstall}}
          </div>          
        </div>
      </div>
      <div v-if="profile.installer && summary1.installer" class="inline-block w-full md:w-1/3">
        <div class="" :class="box1">
          <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
            <svgicon icon="pages" dclass="inline-block w-6 h-6 mx-1" />
            Pending Verification</div>
          <div class="text-center py-2 pb-5 text-5xl text-white">
            {{summary1.installer.pendingverification}}
          </div>          
        </div>
      </div>
      <div v-if="profile.installer && summary1.installer" class="inline-block w-full md:w-1/3">
        <div class="" :class="box1">
          <div class="text-left px-5 text-sm pt-5 pb-2 font-bold text-white">
            <svgicon icon="report" dclass="inline-block w-6 h-6 mx-1" />
            Active</div>
          <div class="text-center py-2 pb-5 text-5xl text-white">
            {{summary1.installer.active}}
          </div>          
        </div>
      </div>
      <div class="w-full text-white" v-else-if="summary1 && profile.customer">
        <div class="w-full">
          <div class="flex justify-between p-5 rounded m-2 bg-gradient-to-r from-blue-500 via-purple-500 to-yellow-500">
            <div class="text-left font-semibold text-white">
              <p>Your current amount</p>
              <p class="mt-2 text-2xl">RM{{ databases && databases.data[0] && formatMoney(databases.data[0].amountcurrent) || 0 }}</p>
              <p class="mt-2 text-yellow-400">Total: RM{{ databases && databases.data[0] && formatMoney(databases.data[0].totalamount) || 0 }}</p>
            </div>
            <div class="text-right text-white">
              <p class="font-semibold">Good morning {{ profile.name ? profile.name : profile.email }}</p>
              <p>Last login: {{ stringDateFormat(profile.lastlogin) }}</p>
            </div>   
          </div>
        </div>
      </div>
      <div v-if="profile.customer">
        <div v-if="databases == null">
          <loading />
        </div>
        <template v-else>
          <div class="text-xs my-2 ml-10" v-if="showBillNo">
            <div class="mb-1">
              {{$t('c.billnoFilter')}} <input class="ml-3 border shadow rounded p-2 text-md" v-model="billnoFilter" @keyup.enter="reload" />
              <button @click="reload" class="inline-block h-8 px-5 bg-yellow-500 hover:bg-yellow-700 text-white ml-2 rounded">{{$t('c.filter')}}</button>
            </div>
          </div>
          <dtable
              :columns="columns"
              :data="databases"
              columnColor="white">
            <template v-slot:action="slotProps">
              <button @click="printPDF(slotProps.item, slotProps.index)" class="inline-block h-8 pr-5 bg-blue-500 hover:bg-blue-700 text-white mr-2 rounded-full">
                <svgicon icon="print" dclass="inline-block w-4 h-4 mx-2" /> PDF
              </button>
              <qrcode-vue id="qrcode" class="hidden mb-8" :value="'https://paymentapi.highfi.com.my/api/payex/' + slotProps.item.id" level="M" render-as="canvas" />
              <br/>
            </template>
          </dtable>
          <dpagination
              :total="databases && databases.total || 0"
              :page="table.page"
              :limit="table.limit"
              :pageChange="pageChange"
              defaultColor="blue"
          />
        </template>
      </div>
    </div>
    <div v-else class="bg-white rounded my-3 mx-2 p-5 text-center">
      <div class="text-center">
        <loading />
      </div>
    </div>

    <!-- Payment Breakdown Modal -->
    <PopupModal
      v-if="showPaymentModal"
      defaultColor="blue"
      modalWidthPercent="80"
      title="Payment Breakdown"
      :btnNoText="$t('c.close')"
      :btnNoFunction="closePaymentModal">

      <div class="space-y-6">
        <!-- Prebills Payment Breakdown -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svgicon icon="report" dclass="inline-block w-5 h-5 mr-2" />
            Prebills Payment Breakdown
          </h3>
          <div v-if="prebillsPaymentBreakdown && prebillsPaymentBreakdown.length > 0" class="space-y-2">
            <div v-for="(item, index) in prebillsPaymentBreakdown" :key="`prebill-${index}`"
                 class="flex justify-between items-center py-3 px-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-800">{{ item._id }}</span>
              </div>
              <span class="text-lg font-bold text-blue-600">RM {{ formatMoney(getItemAmount(item)) }}</span>
            </div>
            <div class="border-t pt-3 mt-4">
              <div class="flex justify-between items-center py-2 px-4 bg-blue-50 rounded-lg">
                <span class="text-sm font-semibold text-blue-800">Total Prebills</span>
                <span class="text-lg font-bold text-blue-800">RM {{ formatMoney(calculateTotal(prebillsPaymentBreakdown)) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="text-center text-gray-500 py-8">
            <svgicon icon="report" dclass="inline-block w-8 h-8 mb-2 opacity-50" />
            <p>No prebills payment data available</p>
          </div>
        </div>

        <!-- Bills Payment Breakdown -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svgicon icon="invoice" dclass="inline-block w-5 h-5 mr-2" />
            Bills Payment Breakdown
          </h3>
          <div v-if="billsPaymentBreakdown && billsPaymentBreakdown.length > 0" class="space-y-2">
            <div v-for="(item, index) in billsPaymentBreakdown" :key="`bill-${index}`"
                 class="flex justify-between items-center py-3 px-4 bg-white rounded-lg border border-gray-200 hover:border-green-300 transition-colors">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-800">{{ item._id }}</span>
              </div>
              <span class="text-lg font-bold text-green-600">RM {{ formatMoney(getItemAmount(item)) }}</span>
            </div>
            <div class="border-t pt-3 mt-4">
              <div class="flex justify-between items-center py-2 px-4 bg-green-50 rounded-lg">
                <span class="text-sm font-semibold text-green-800">Total Bills</span>
                <span class="text-lg font-bold text-green-800">RM {{ formatMoney(calculateTotal(billsPaymentBreakdown)) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="text-center text-gray-500 py-8">
            <svgicon icon="invoice" dclass="inline-block w-8 h-8 mb-2 opacity-50" />
            <p>No bills payment data available</p>
          </div>
        </div>
      </div>
    </PopupModal>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import type { Font } from '@pdfme/common';
import { generate } from '@pdfme/generator'
import { imageBase64 } from '@/assets/logo_base64.js';
import invTemplate from '../auth/billings/template/invoice_v2.json'
import QrcodeVue from 'qrcode.vue'
import adminheader from '@/components/AdminHeader.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import { auth2Store } from '../../store/auth2-store'
import loading from '@/components/cvui/loading.vue'
import { getDashboardSummary, getClientBills, getPrebillingPaymentBreakdown, getBillingsPaymentBreakdown } from '../../api/index'
import { getSubscription, getSubscriptions, sendBill } from '../../api'
import moment from 'moment'
import svgicon from '@/components/cvui/svgcollection.vue'
import PopupModal from '@/components/cvui/Modal.vue'
export default defineComponent({
  setup () {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
    }
  },
  components: {
    adminheader,
    dtable,
    dpagination,
    loading,
    svgicon,
    QrcodeVue,
    imageBase64,
    PopupModal
  },
  methods: {
    getDashboardSummary () {
      getDashboardSummary({token: this.token}).then((res: any) => {
        this.summary1 = res
      })
    },
    getPrebillingPaymentBreakdown () {
      getPrebillingPaymentBreakdown({ token: this.token, startdate: this.currentMonthFirstDay, enddate: this.currentMonthLastDay }).then((res: any) => {
        this.prebillsPaymentBreakdown = res.data
      })
    },
    getBillingsPaymentBreakdown () {
      getBillingsPaymentBreakdown({ token: this.token, startdate: this.currentMonthFirstDay, enddate: this.currentMonthLastDay }).then((res: any) => {
        this.billsPaymentBreakdown = res.data
      })
    },
    printPDF (p: any, i: number) {
      if (p && p.subscriptions && p.subscriptions.length > 0 ) {
        let sid: string = p.subscriptions[0]
        getSubscription({ token: this.token, id: sid }).then((res: any) => {
          let r = res && res.data
          this.printPDF2(p, i, r)
        })
      } else {
        this.printPDF2(p, i, null)
      }
    },
    printPDF2 (p: any, i: number, sub: any) {
      const template: any = invTemplate;
      const c: any = this.profile

      let amts = ''
      let itms = ''

      // items
      p.items.forEach((item: any, index: number) => {
        itms += `${item.itemname} \n`
        // get \n count in item.itemname
        let ncount = item.itemname.split(/\r?\n/)

        amts += `${this.formatMoney(item.amount)} \n`
        if (ncount.length > 1) {
          for (let i = 1; i < ncount.length; i++) {
            amts += '\n'
          }
        }
      })

      var canvas = document.getElementById("qrcode") as HTMLCanvasElement;

      if(canvas){
        var pngDataUrl = canvas.toDataURL("image/png");
      } else {
        var pngDataUrl = ''
      }
      
      const inputs = [{
        "logo": imageBase64,
        "companyAddress": "A1-02-07, Level 2, Block A1, Sunway GEO Avenue\nJalan Lagoon Selatan, Sunway South Quay, Bandar Sunway\n47500 Subang Jaya",
        "itemTitle": "INVOICE",
        "customerNameWtTitleGreeting": `Dear ${c.title && c.title.toUpperCase() || ''} ${c.name}`,
        "sidLabel": "Subscriber ID",
        "sid": `${sub.sid || ''}`,
        "companyName": "HIIFI (M) SDN. BHD. (1406319-P)",
        "customerNameWtTitle": `${c.title && c.title.toUpperCase()  || ''} ${c.name}`,
        "summaryTitle": "Here's a summary bill for month " + this.shortMonthFormat(p.billdate),
        "billLabel": "Bill No",
        // "userLabel": "User",
        "billdateLabel": "Bill Date",
        "duedateLabel": "Due Date\n\nDeposit",
        // "creditLabel": "",
        // "credit": "",
        "billNo": p.billno || p.id,
        "user": c.email,
        // "addonTitle": this.addOnTitle(sub),
        "billDate": this.formatDate(p.billdate),
        "dueDate": (p.duedate ? this.formatDate(p.duedate) : '-/-/-' ) + "\n\n" + (sub.deposit ? this.formatMoney(sub.deposit) : '0.00' ),
        // "Line": "   ",
        // "Line2": "   ",
        // "Line3": "   ",
        "customerAddress": `${sub.address.address || '---'}`,
        "page": "Page 1/1",
        // "Line4": "   ",
        // "Line5": "   ",
        "empty": " ",
        "empty2": " ",
        "itemsLabel": "Items",
        "amountLabel": "Amount (MYR)",
        "items": itms,
        "amounts": amts,
        "subtotalLabel": "Subtotal",
        "taxLabel": "Tax",
        "bfLabel": "B/F",
        "subtotal": this.formatMoney(p.amountcurrent),
        "tax": this.formatMoney(p.taxcurrent),
        "bf": this.formatMoney(p.amountbf),
        "totalLabel": "Total Amount Payable",
        "totalLabel2": "Total ",
        "total": `${this.formatMoney(p.totalamount)}`,
        "total2": `${this.formatMoney(p.totalamount)}`,
        "empty3": " ",
        // "footerLabel1": "Notes:",
        // "footerLabel2": "1. All remittance shoud be made payable to :-\n    HIIFI(M) SDN. BHD.\n    CIMB **********\n\n2. Please include the invoice number at the recipient reference.\n3. Please share the bank in slip to HIIFI Support + 6019-719 9799\n",
        "footerLabel1": "",
        "footerLabel2": "",
        "footerfooter1": "Make your payment via FPX",
        "footerfooter2": `https://paymentapi.highfi.com.my/api/payex/${p.id}`,
        "qrcode": pngDataUrl
      }];
      if (template && inputs) {
        generate({ template, inputs, options: {font: this.font} }).then((pdf: any) => {
          // Browser
          const blob = new Blob([pdf.buffer], { type: 'application/pdf' });
          window.open(URL.createObjectURL(blob));

          // Node.js
          // fs.writeFileSync(path.join(__dirname, `test.pdf`), pdf);
        });
      }
      
    },
    formatMoney (p: any) {
      if (p != null) {
        return p.toFixed(2)
      } else {
        return '0.00'
      }
    },
    removeDuplicate (arraylist: any) {
      return arraylist = [...new Set(arraylist)]
    },
    getSubscribtionList () {      
      let subscriptionslist2 = this.subscriptions.map((p: any) => p.id)
      let subscriptions:any = []
      if (this.databases) {
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i].subscriptions
          subscriptions = subscriptions.concat(data)
        }
        subscriptions = this.removeDuplicate(subscriptions)
        subscriptions = subscriptions.filter((p: any) => subscriptionslist2.indexOf(p) === -1)
        subscriptions.filter((k:any) => {
          getSubscription({ token: this.token, id: k }).then((rs) => {
            this.subscriptions.push({
              id: k,
              value: this.formatadd(rs.data),
            })
          })
        })
      }
    },
    formatadd (p: any) {
      return p && p.address ? `${p.address.block}-${p.address.level}-${p.address.unit} ${p.address.building}` : '---'
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    shortMonthFormat (p: any) {
      let r = ''
      if (p) {
        r = moment(p).format('MM/YY')
      }
      return r
    },
    formatDate (p: any) {
      let r = ''
      if (p) {
        r = moment(p).format('DD/MM/YYYY')
      }
      return r
    },
    stringDateFormat (isoString: any) {
      const date = new Date(isoString);

      const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        weekday: "long",
        hour: "numeric",
        minute: "numeric",
        second: undefined, // Skip seconds
        hour12: true, // 12-hour format
      };

      const formattedDate = new Intl.DateTimeFormat("en-US", options).format(date);
      return formattedDate
    },
    loadDatabase () {
      if (this.profile) {
        if (this.subscribefilter) {
          let p = {...this.table, token: this.token, customer: this.profile.id, subscriptions: [this.subscribefilter] }

          if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
            p["billno"] = this.billnoFilter.trim()
            if (!p["params"]) {
              p["params"] = []
            }
            p["params"].push("billno")
          }

          getClientBills({token: this.token}).then((res: any) => {
            p = res
          })
        } else {      
          let p = {...this.table, token: this.token, customer: this.profile.id }
          
          if (this.billnoFilter && this.billnoFilter.trim().length > 0) {
            p["billno"] = this.billnoFilter.trim()
            if (!p["params"]) {
              p["params"] = []
            }
            p["params"].push("billno")
          }

          getClientBills({token: this.token}).then((res: any) => {
            this.summary2 = res        
          })
        }
        
      }         
    },
    pageChange (p:any) {
      this.table.page = p
      this.loadDatabase()
    },
    addOnTitle (s: any) {
      let c: any = this.profile
      return s ? `${s.address['building'] || 'The Grand Subang Jaya SS15'} ${s.address['block']}-${s.address['level']}-${s.address['unit']}\n${s.contact}` : `${c.contact}`
    },
    showPaymentBreakdownModal () {
      this.showPaymentModal = true
    },
    closePaymentModal () {
      this.showPaymentModal = false
    },
    getItemAmount (item: any) {
      return item.amount || item.total || item.value || item.price || 0
    },
    calculateTotal (items: any[]) {
      if (!items || !Array.isArray(items)) return 0
      return items.reduce((total, item) => total + this.getItemAmount(item), 0)
    }
  },
  computed: {
    columns () {
      let subscriptions: any = this.subscriptions
      let ar: any = [
        { title: 'billings.billdate', key: 'billdate', type: 'date', class: 'text-center' },
        { title: 'billings.total', key: 'totalamount', type: 'price', class: 'text-center' },        
        { title: 'billings.duedate', key: 'duedate', type: 'date', class: 'text-center' },        
        { title: 'billings.paid', key: 'amountpaid', type: 'price', class: 'text-center' },
        // { title: 'billings.subscriptions', key: 'subscriptions', objectlist: subscriptions, type: 'objectarray', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]      
      if (this.showBillNo) {
        ar.unshift({ title: 'billings.billno', key: 'billno', type: 'text', class: 'text-center' })
      }
      if (this.showUnit) {
        ar.unshift({ title: 'billings.subscriptions', key: 'subscriptions', objectlist: subscriptions, type: 'objectarray', class: 'text-center' })
      }
      if (this.showId) {
        ar.unshift({ title: 'billings.id', key: 'id', type: 'text', class: 'text-center' })
      }
      return ar 
    },
    databases () {
      return this.summary2
    },
    scopes () {
      let s: any = auth2Store.getState().profile
      
      return  s && s.scopes 
    },
    isAdmin () {
      let p: any = this.scopes
      if (p.includes('admin')) {
        return true;
      }
      return false;
    },
  },
  watch: {
    databases (p: any) {      
      if (p) {
        this.getSubscribtionList()
      }
    }
  },  
  mounted () {
    (async function (t: any) {
      const kk: Font = {
        Arial: {
          data: await fetch('/fonts/Arial/Arial2.ttf').then((res) => res.arrayBuffer()),
          fallback: true,
        },
        Effra: {
          data: await fetch('/fonts/Effra/Effra.ttf').then((res) => res.arrayBuffer()),
        },
        EffraBold: {
          data: await fetch('/fonts/Effra/EffraBold.ttf').then((res) => res.arrayBuffer()),
        },
        Gotham: {
          data: await fetch('/fonts/Gotham/GothamMedium.ttf').then((res) => res.arrayBuffer()),
        },
        GothamBold: {
            data: await fetch('/fonts/Gotham/GothamBold.ttf').then((res) => res.arrayBuffer()),
        },
        };
      t.font = kk;
    })(this);
    this.reload()
    this.getDashboardSummary()
    this.getPrebillingPaymentBreakdown()
    this.getBillingsPaymentBreakdown()
  },
  data() {
    const getMonthBoundaries = () => {
      const now = new Date();
      const firstDay = new Date(Date.UTC(now.getFullYear(), now.getMonth(), 1));
      const lastDay = new Date(Date.UTC(now.getFullYear(), now.getMonth() + 1, 0));
      lastDay.setDate(lastDay.getDate());

      const formatDate = (date: Date) => 
        date.toISOString().split('T')[0];

      return {
        firstDay: formatDate(firstDay),
        lastDay: formatDate(lastDay),
      };
    };

    const { firstDay, lastDay } = getMonthBoundaries();

    return {
      summary1: undefined as any,
      summary2: [] as any,
      box1: 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded m-2' as any,
      clickableBox: 'bg-pink-500 hover:bg-pink-600 rounded m-2 border-2 border-transparent hover:border-white hover:border-opacity-30' as any,
      table: {
        limit: 10,
        page: 1,
        keywords: ''
      } as any,
      keywords: '' as string,
      showBillNo: true as boolean,
      showId: false as boolean,
      subscription: undefined as any,
      showUnit: false as boolean,
      subscriptions: [] as any,
      subscribefilter: undefined as any,
      billnoFilter: '' as string,
      font: null as any,
      prebillsPaymentBreakdown: [] as any,
      billsPaymentBreakdown: [] as any,
      currentMonthFirstDay: firstDay as string,
      currentMonthLastDay: lastDay as string,
      showPaymentModal: false as boolean,
    };
  }
})
</script>
