<template>
  <PopupModal defaultColor="blue" modalWidthPercent="90"
    :title="item.id ? $t('buildings.update') : $t('buildings.create')" :btnYesText="$t('c.submit')"
    :btnYesFunction="submitNow" :btnNoText="$t('c.cancel')" :btnNoFunction="cancel" v-if="item">
    <form @submit.prevent="preventsubmit" autocomplete="">
      <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="key" :labelTitle="$t('buildings.key')"
        :required="true">
        <TextInput name="key" type="string" v-model="item.key" defaultColor="blue">
        </TextInput>
        <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="title" :labelTitle="$t('buildings.title')"
        :required="true">
        <TextInput name="title" type="string" v-model="item.title" defaultColor="blue">
        </TextInput>
        <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="area" :labelTitle="$t('buildings.area')"
        :required="true">
        <TextInput name="area" type="string" v-model="item.area" defaultColor="blue">
        </TextInput>
        <ErrTextCompt :errs="errs && errs.area"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="homepass"
        :labelTitle="$t('buildings.homepass')" :required="true">
        <TextInput name="homepass" type="number" v-model.number="item.homepass" defaultColor="blue">
        </TextInput>
        <ErrTextCompt :errs="errs && errs.homepass"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="homepass"
        :labelTitle="$t('buildings.rfs')" :required="true">
        <DatePicker
            v-model="item.rfsdate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
        <ErrTextCompt :errs="errs && errs.rfs"></ErrTextCompt>
      </FormItemCompt>
      <div class="mt-2">
        <p class="px-1 text-xs">Charges</p>
        <div class="w-full flex justify-center">
          <table class="w-3/4">
            <thead>
              <tr>
                <td class="border px-2 text-center bg-gray-200">Key</td>
                <td class="border px-2 text-center bg-gray-200">Value</td>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(charge, index) in item.charges" :class="index % 2 !== 0 ? 'bg-gray-50' : 'bg-white'">
                <td class="border px-2 text-center">
                  <input type="text" v-model="charge[0]" class="bg-transparent text-center w-full">
                </td>
                <td class="border px-2 text-center">
                  <input type="number" v-model.number="charge[1]" class="bg-transparent text-center w-full">
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="mt-4 flex justify-center">
          <button @click="pushArray()" class="text-center px-2 py-1 bg-blue-300 hover:bg-blue-400 ">+ Add more</button>
        </div>
      </div>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import SelectList from '@/components/cvui/form/SelectList.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import DateTimePicker from '@/components/cvui/form/DateTimePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import PopupModal from '@/components/cvui/Modal.vue'
export default defineComponent({
  props: {
    item: { type: Object, required: true },
    cancel: { type: Function },
    save: { type: Function },
    profile: { type: Object },
    token: {
      type: String,
      required: true
    }
  },
  computed: {
    //
  },
  mounted() {
    document.addEventListener('keydown', this.handleEscKey);
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  components: {
    PopupModal,
    FormItemCompt,
    TextInput,
    SelectList,
    Checkbox,
    DateTimePicker,
    ErrTextCompt,
    SelectOne,
    DatePicker
  },
  methods: {
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        if (this.cancel) {
          this.cancel()
        }
      }
    },
    pushArray() {
      if (!Array.isArray(this.item.charges)) {
        this.item.charges = [];
      }
      this.item.charges.push(["", ]);
    },
    validateForm() {
      let p = true
      if (!this.item.key || this.item.key.trim().length == 0) {
        this.errs['key'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.title || this.item.title.trim().length == 0) {
        this.errs['title'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.area || this.item.area.trim().length == 0) {
        this.errs['area'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.homepass) {
        this.errs['homepass'] = 'c.fieldRequired'
        p = false
      }
      return p
    },
    preventsubmit(e: any) {
      e.preventDefault()
    },
    submitNow(e: any) {
      if (this.validateForm()) {
        if (this.save != null) {
          this.save(this.item)
        }
      }
      return false
    },
  },
  data() {
    let errs: any = {}
    return {
      errs,
    }
  }
})
</script>