<template>
  <div>
    <adminheader :title="$t('navigations.buildings')" :addFunction="isAdmin || isDev ? addBuilding : ''"
      :reloadFunction="reload"></adminheader>

    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div v-if="databases == null">
      <dloading />
    </div>
    <template v-else>
      <dtable :columns="columns" :data="databases" columnColor="white">
        <template v-slot:action="slotProps">
          <button :title="$t('c.edit')" @click="editRow(slotProps.item, slotProps.index)"
            class="inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full">
            <svgicon icon="edit" dclass="w-4 h-4 m-1 inline-block" />
          </button>
        </template>
      </dtable>
      <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
        :pageChange="pageChange" defaultColor="blue" />
    </template>
    <template v-if="item">
      <dform :item="item" :cancel="cancelNow" :token="token" :profile="profile" :save="saveFunc"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import { buildingStore } from '../../../store/building-store'
import { getSubscriberCount } from '../../../api'

export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    return {
      token: computed(() => authState.token),
      authStore: authStore,
      authState: authState,
      profile: computed(() => auth2State.profile),
      buildingStore: buildingStore,
      buildingState: buildingStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    svgicon,
  },
  mounted() {
    this.reload()
  },
  data() {
    let item: any = null
    let deleteItem: any = null
    let itemStyle: any = {
      key: '',
      title: '',
      area: '',
      homepass: '',
      rfs: true,
      charges: [
        ["JNXX", 8],
        ["JNXBB", 4],
        ["JCV", 2]
      ],
      status: true
    }
    let table: any = {
      limit: 10,
      page: 1,
      keywords: '',
      status: true,
      customer: true
    }
    let keywords: string = ''
    return {
      item,
      deleteItem,
      itemStyle,
      table,
      keywords,
      subscriberCounts: {} as Record<string, number>, // Store subscriber counts by building title
      loadingSubscriberCounts: false,
    }
  },
  methods: {
    searchNow() {
      this.table.page = 1
      this.table.keywords = this.keywords
      this.loadDatabase()
    },
    searchFunc(p: string) {
      this.keywords = p
      this.searchNow()
    },
    reload() {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase() {
      let p = { ...this.table, token: this.token }
      this.buildingStore.getBuildings(p)
    },
    async fetchSubscriberCount(buildingKey: string) {
      // getSubscriberCount
      try {
        const response = await getSubscriberCount({ token: this.token, building: buildingKey })
        return response.data
      } catch (error) {
        console.error('Error fetching subscriber count:', error)
        return 0
      }
    },
    async loadSubscriberCounts() {
      if (!this.databases || !this.databases.data) return

      this.loadingSubscriberCounts = true
      const counts: Record<string, number> = {}

      // Fetch subscriber counts for all buildings
      const promises = this.databases.data.map(async (building: any) => {
        if (building.key) {
          const count = await this.fetchSubscriberCount(building.key)
          counts[building.key] = count
        }
      })

      await Promise.all(promises)
      this.subscriberCounts = counts
      this.loadingSubscriberCounts = false
    },
    pageChange(p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem() {
      this.item = this.itemStyle
    },
    addBuilding() {
      this.initItem()
    },
    cancelNow() {
      this.item = null
      this.reload()
    },
    editRow(item: any, index: any) {
      if (!this.item) {
        if (item.ID) { this.item = Object.assign({ id: item.ID }, item) }
        else { this.item = Object.assign({}, item) }

        this.item = JSON.parse(JSON.stringify(this.item))
      }
    },
    duplicateRow(p: any, i: any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc(p: any) {
      if (p.id) {
        this.buildingStore.updateBuilding({ form: p, id: p.id, token: this.token })
      } else {
        this.buildingStore.createBuilding({ form: p, token: this.token })
      }
      this.item = null
      this.reload()
    },
  },
  computed: {
    columns() {
      return [
        // { title: 'buildings.id', key: 'id', type: 'string', class: 'text-center' },
        // { title: 'buildings.key', key: 'key', type: 'string', class: 'text-center' },
        { title: 'buildings.location', key: 'title', type: 'string', class: 'text-center' },
        { title: 'buildings.area', key: 'area', type: 'string', class: 'text-center' },
        { title: 'buildings.rfs', key: 'rfsdate', type: 'date', class: 'text-center' },
        { title: 'buildings.totalhomepass', key: 'homepass', type: 'string', class: 'text-center' },
        { title: 'buildings.subscribercount', key: 'subscriberCount', type: 'string', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases() {
      const buildings = buildingStore.getState().buildings
      if (!buildings || !buildings.data) return buildings

      // Add subscriber counts to building data
      const buildingsWithCounts = {
        ...buildings,
        data: buildings.data.map((building: any) => ({
          ...building,
          subscriberCount: this.loadingSubscriberCounts
            ? 'Loading...'
            : (this.subscriberCounts[building.key] !== undefined
                ? this.subscriberCounts[building.key]
                : '-')
        }))
      }

      return buildingsWithCounts
    },
    buildingUpdate () {
      return buildingStore.getState().buildingUpdate
    },
    buildingUpdateSuccess () {
      return buildingStore.getState().buildingUpdateSuccess
    },
    buildingUpdateError () {
      return buildingStore.getState().buildingUpdateError
    },
    buildingDeleteSuccess () {
      return buildingStore.getState().buildingDeleteSuccess
    },
    scopes() {
      let s: any = auth2Store.getState().profile

      return s && s.scopes
    },
    isSupportL1() {
      let p: any = this.scopes
      if (p.length === 1 && p.includes('supportl1')) {
        return true;
      }
      return false;
    },
    isAdmin() {
      let p: any = this.scopes
      if (p.includes('admin')) {
        return true;
      }
      return false;
    },
    isDev() {
      let p: any = this.scopes
      if (p.includes('dev')) {
        return true;
      }
      return false;
    },
  },
  watch: {
    buildingUpdateSuccess(p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('buildings.building'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    buildingUpdateError(p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('buildings.building'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    // Watch for changes in buildings data and load subscriber counts
    'buildingState.buildings': {
      handler(newBuildings) {
        if (newBuildings && newBuildings.data && newBuildings.data.length > 0) {
          this.loadSubscriberCounts()
        }
      },
      deep: true
    }
  },
})
</script>