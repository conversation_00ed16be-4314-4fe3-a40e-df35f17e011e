<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('tickets.followUpFormTitle')"
      :cancelFunction="cancel"
      v-if="item">
    <form @submit.prevent="preventsubmit" autocomplete="">
      <div v-if="false" class="">
          <div class="inline-block rounded bg-gray-100 px-5 py-1 text-sm text-blue-600 font-bold" v-if="item.id">{{item.id}}</div>
      </div>
      <div class="flex flex-col md:flex-row">
        <div class="w-full md:w-1/4 px-3 border-b-2 md:border-b-0 md:border-r-2 mb-5 md:mb-0 pb-5 md:pb-0">
          <h2 class="my-2 font-bold">{{$t('plans.details') + ':-'}}</h2>
          <div class="border-b hover:bg-gray-200" v-for="g in ['title', 'level', 'subscription', 'department', 'assignee', 'customer', 'closingdate']" :key="g">
            <div class="inline-block w-1/4 text-xs">{{$t('tickets.' + g)}}</div>
            <div class="inline-block  w-3/4 text-sm">
              <template v-if="g === 'assignee'">
                <template :key="'ass_' + String(ascindex)" v-for="(asc, ascindex) in params(item,g)">
                  {{filterUser(asc) && filterUser(asc).name}}<template>{{', '}}</template>
                </template>
              </template>
              <template v-else-if="g === 'customer'">
                {{filterUser(item[g]) && filterUser(item[g]).name}}
              </template>
              <template v-else-if="g === 'department'">
                {{$t('tickets.dp_' + item[g])}}
              </template>
              <template v-else-if="g === 'closingdate'">
                {{item[g] ? moment(item[g]).format('DD-MM-YYYY') : '---'}}
              </template>
              <template v-else>{{item[g] || '---'}}</template>
            </div>
          </div>
          <h2 class="my-2 font-bold">{{$t('tickets.attachments')}}</h2>
          <ShowFiles
              v-if="item.attachments && item.attachments.length > 0"
              :files="questionAttachments" />
          <div v-else>---</div>
        </div>
        <div class="w-full md:w-3/4 px-3">
          <div
              class="overflow-y-auto px-10"
              :class="item.closingdate ? 'doneCSS' : 'unDoneCSS'"
              v-if="refreshShow">
            <template :key="`tl_${String(index)}`" v-for="(logitem, index) in ticketLog">
              <div class="flex mt-2">
                <div class="mr-2 mt-2 flex-none">
                  <svgCollection
                      :icon="filterUser(logitem.user).customer ? 'user2' : 'admin'"
                      class="w-9 h-9 p-1 rounded-full"
                      :class="filterUser(logitem.user).customer ? 'bg-blue-200 text-blue-800' : 'bg-blue-500 text-white'"></svgCollection>
                </div>
                <div class="flex-grow rounded p-2 bg-gray-100 mb-1 p-1 px-2 text-sm">
                  <div class="flex">
                    <b class="flex-1">{{filterUser(logitem.user) && filterUser(logitem.user).name}}</b>
                    <small>{{formatDate(logitem && logitem.created_at)}}</small>
                  </div>
                  <div class="text-sm mt-1 linefix">{{logitem.description}}</div>
                  <div class="mt-3" v-if="logitem.attachments && logitem.attachments.length > 0">
                    <ShowFiles
                        :files="logAttachment[logitem.id]"
                        :disableListView="true" />
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-if="!item.closingdate" class="flex flex-wrap -mx-3">
            <div class="w-full md:w-full px-3 mb-2 mt-2">
              <FormItemCompt
                  labelFor="description"
                  :labelTitle="$t('tickets.reply')"
                  :required="true">
                <TextareaInput
                    name="description"
                    v-model="followUpItem.description"
                    :placeholder="$t('c.frontPlc') + $t('tickets.reply')"
                    :rows="3"
                    defaultColor="blue"></TextareaInput>
                <ErrTextCompt :errs="errs && errs.description"></ErrTextCompt>
              </FormItemCompt>
              <FormItemCompt
                  labelFor="attachments"
                  :labelTitle="$t('subscriptions.attachments')">
                <!-- <UploadInput
                    :attachments="attachments"
                    :removeFile="removeFile"
                    :addFile="addFile"
                    :basePath="basePath"
                    :token="token"
                    uploadheightpx="120"
                    textmtpx="25"></UploadInput> -->
                <ShowFiles
                    :files="attachments"
                    :disableDelete="true"></ShowFiles>
              </FormItemCompt>
            </div>
            <div class="ml-auto">
              <button
                  @click="followupClick"
                  class="text-white bg-blue-500 hover:bg-blue-700 font-bold py-2 px-4 rounded mr-3">
                {{$t('c.submit')}}
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { ticketlogStore } from '../../../store/ticketlog-store'
import PopupModal from '@/components/cvui/ModalNoBtn.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
// import UploadInput from '@/components/cvui/form/UploadInput.vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import { getFile, deleteFile, basePath, getUserName } from '../../../api'
import userpic from '@/components/cvui/userpic.vue'
import moment from 'moment'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  setup() {
    const auth2State = auth2Store.getState()
    return {
        profile: computed(() => auth2State.profile ),
    }
  },
  props: {
    item: {type: Object, required: true },
    cancel: {type: Function, required: true },
    token: {type: String, required: true }
  },
  components: {
    PopupModal,
    FormItemCompt,
    TextInput,
    TextareaInput,
    ErrTextCompt,
    // UploadInput,
    ShowFiles,
    userpic,
    svgCollection,
  },
  mounted () {
    if (this.item) {
      this.followUpItem.ticket = this.item.id
      this.followUpItem.user = this.profile.id
      this.reload()
    }
  },
  methods: {
    getAttachment () {
      this.questionAttachments = []
      if (this.item.attachments) {
        for (let i = 0; i < this.item.attachments.length; i++) {
          let datai = this.item.attachments[i]
          getFile({ token: this.token, id: datai }).then(rs => {
            this.questionAttachments.push(rs.data)
          })
        }
      }
    },
    getLogAttachments () {
      if (this.ticketLog) {
        this.ticketLog.filter((tl: any) => {
          if (tl.attachments) {
            if (!this.logAttachment[tl.id]) {
              this.logAttachment[tl.id] = []
            }
            for (let i = 0; i < tl.attachments.length; i++) {
              let datai = tl.attachments[i]
              getFile({ token: this.token, id: datai }).then(rs => {
                this.logAttachment[tl.id].push(rs.data)
              })
            }
          }
        })
      }
    },
    params(p: any, i: any) {
      return p[i] || ''
    },
    reload () {
      this.ticketLog = [
        {
          ticket: this.item.id,
          description: this.item.description,
          attachments: this.item.attachments,
          user: this.item.customer,
          created_at: this.item.created_at,
          updated_at: this.item.updated_at
        }
      ]
      for (let i = 0; i < this.item.assignee.length; i++) {
        let assignee = this.item.assignee[i]
        this.getUserList(assignee)
      }
      this.getUserList(this.item.user)
      this.getUserList(this.item.customer)
      this.loadTicketLog(1, 5, '')
      this.getAttachment()
      this.getLogAttachments()
      this.refreshFunction()
    },
    loadTicketLog (pg: any, pageSize: any, keywords: string) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      ticketlogStore.getTicketlogs({ token: this.token, skip: skip, limit: pageSize, keywords: keywords, ticket: this.item.id }).then(res => {
        var d:any = res
        if (d.data) {
          this.ticketLog = this.ticketLog.concat(d.data)
          if ((d.total / pageSize) >= pg) {
            this.loadTicketLog(pg + 1, pageSize, keywords)
          }
        }
      })
    },
    getUserList (p:any) {
      getUserName({ token: this.token, id: p }).then((rs) => {
        this.userlist.push(rs)
      }).catch((err) => {
        if (err) {
          this.userlist.push({ id: p })
        }
      })
    },
    filterUser(p: any): any {
      return this.userlist.filter((k: any) => k.id === p)[0]
    },
    // start attachments
    addFile (p:any) {
      if (!this.followUpItem.attachments) {
      this.followUpItem.attachments = []
      }
      this.followUpItem.attachments.push(p.file.id)
      this.attachments.push(p.file)
    },
    removeFile (p:any) {
      let i = this.followUpItem.attachments.indexOf(p)
      if (i > -1) {
      this.followUpItem.attachments.splice(i, 1)
      }
      let list = this.attachments.map((p:any) => p.id || p.ID)
      let j = list.indexOf(p)
      this.attachments.splice(j, 1)
      deleteFile({id: p, token: this.token})
    },
    // end attachments
    validateForm () {
      let p = true
      if (!this.followUpItem.description || this.followUpItem.description.trim().length == 0) {
        this.errs['description'] = 'c.fieldRequired'
        p = false
      }
      return p
    },
    preventsubmit (e: any) {
      e.preventDefault()
    },
    followupClick () {
      if (this.followUpItem.id) {
        ticketlogStore.updateTicketlog({ form: this.followUpItem, id: this.followUpItem.id, token: this.token })
      } else {
        ticketlogStore.createTicketlog({ form: this.followUpItem, token: this.token })
      }
      this.reload()
    },
    refreshFunction () {
      this.refreshShow = false
      this.$nextTick(() => {
        this.refreshShow = true
      })
    },
    formatDate (p: any) {
      return moment(p, 'YYYY-MM-DDThh:mm:ssZ').format('DD-MM-YYYY HH:mm:ssa')
    },
  },
  data () {
    let errs: any = {}
    let followUpItem: any = {
      ticket: '',
      description: '',
      attachments: [],
      user: ''
    }
    let deleteItem: any = undefined
    let questionAttachments: any = []
    let attachments: any = []
    let userlist: any = []
    let ticketLog: any = []
    let logAttachment: any = {}
    return {
      errs,
      followUpItem,
      basePath,
      questionAttachments,
      attachments,
      userlist,
      ticketLog,
      logAttachment,
      moment,
      deleteItem,
      refreshShow: true
    }
  },
  computed: {
    ticketlogCreate () {
      return ticketlogStore.getState().ticketlogCreate
    },
    ticketlogCreateSuccess () {
      return ticketlogStore.getState().ticketlogCreateSuccess
    },
    ticketlogCreateError () {
      return ticketlogStore.getState().ticketlogCreateError
    },
    ticketlogUpdate () {
      return ticketlogStore.getState().ticketlogUpdate
    },
    ticketlogUpdateSuccess () {
      return ticketlogStore.getState().ticketlogUpdateSuccess
    },
    ticketlogUpdateError () {
      return ticketlogStore.getState().ticketlogUpdateError
    },
    ticketlogDeleteSuccess () {
      return ticketlogStore.getState().ticketlogDeleteSuccess
    }
  },
  watch: {
    ticketlogCreateSuccess (p) {
      if (p) {
        this.followUpItem.description = ''
        this.followUpItem.attachments = []
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.reply'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    ticketlogCreateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.reply'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    ticketlogUpdateSuccess (p) {
      if (p) {
        this.followUpItem.description = ''
        this.followUpItem.attachments = []
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.reply'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    ticketlogUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.reply'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    ticketlogDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  }
})
</script>
<style scoped>
.doneCSS {
  /* height: 27rem; */
}
.unDoneCSS {
  /* height: 27rem; */
}
@media (min-width: 768px) {
  .doneCSS {
    height: 39rem;
  }
  .unDoneCSS {
    height: 22rem;
  }
}
</style>
<!-- start customer -->
<!-- <div
    v-if="filterUser(logitem.user) && filterUser(logitem.user).customer"
    class="flex flex-col md:flex-row justify-start items-start mb-8">
  <div class="mb-2 md:mb-0 text-center">
    <userpic :user="filterUser(logitem.user)" :widthclass="'w-12 h-12 text-xl m-auto'" :textClass="'text-xl'" :mstyle="'padding: 2px;'" />
    <small>{{filterUser(logitem.user) && filterUser(logitem.user).name}}</small>
  </div>
  <div class="bg-blue-200 text-black rounded-lg shadow-md p-3 ml-0 md:ml-3 border-1 border-blue-500 relative max-w-lg md:max-w-md">
    <span class="absolute -top-1 md:top-4 left-12 md:left-0 w-3 h-3 bg-blue-200" style="transform: translateX(-6px) rotate(45deg); pointer-events: none;">&nbsp;</span>
    <p class="mb-1 leading-tight">
      {{logitem.description}}
    </p>
  </div>
</div> -->
<!-- end customer -->
<!-- start admin -->
<!-- <div v-else class="flex flex-col-reverse md:flex-row justify-end items-end md:items-start space-x-4 mb-8">
  <div class="bg-blue-500 text-blue-100 rounded-lg shadow-md p-3 mr-0 md:mr-3 border-1 border-blue-500 relative max-w-lg md:max-w-md">
    <span class="absolute -top-1 md:top-4 right-7 md:-right-3 w-3 h-3 bg-blue-500" style="transform: translateX(-6px) rotate(45deg); pointer-events: none;">&nbsp;</span>
    <p class="mb-1 leading-tight">
      {{logitem.description}}
    </p>
    <ShowFiles v-if="logitem.attachments && logitem.attachments.length > 0" :files="logitem.attachments" />
  </div>
  <div class="mb-2 md:mb-0 text-center">
    <userpic :user="filterUser(logitem.user)" class="ml-auto" :widthclass="'w-12 h-12 text-xl m-auto'" :textClass="'text-xl'" :mstyle="'padding: 2px;'" />
    <small>{{filterUser(logitem.user) && filterUser(logitem.user).name}}</small>
  </div>
</div> -->
<!-- end admin -->
