<template>
<PopupModal
    defaultColor="blue"
    modalWidthPercent="90"
    :title="$t('tickets.formTitle')"
    :btnYesText="$t(item.id ? 'tickets.update' : 'tickets.submitTicket')"
    :btnYesFunction="closingdate ? null : submitNow"
    :btnNoText="$t('c.cancel')"
    :btnNoFunction="cancel"
    v-if="item">
    <form @submit.prevent="preventsubmit" autocomplete="">
        <div v-if="isDev" class="">
            <div
                class="inline-block rounded bg-gray-100 px-5 py-1 text-sm font-bold"
                :class="'text-' + (item.id ? 'blue' : 'red') + '-600'">
              {{item.id || $t('tickets.new')}}
            </div>
        </div>
        <div class="text-xs flex items-center">
          <p>Date</p>
          <p class="ml-10 p-1 bg-gray-200 rounded-md">{{ item.id ? formattedDate(item.created_at): formattedDate(moment())  }}</p>
        </div>
        <div v-if="showCustomer || !prm('customer')">
            <FormItemCompt
                class="w-full inline-block px-1"
                labelFor="customer"
                :labelTitle="$t('tickets.customer')">
                <UserAssignInput
                    v-if="!showSearchBar"
                    :token="token"
                    :addUser="addCustomer"
                    :removeUser="removeCustomer"
                    :users="customer"
                    :searchUser="searchUser"
                    :readonly="profileIsCustomer || closingdate"
                    :multiple="false"
                    :plcHolder="$t('c.customerKeySearch')"
                    :noUserText="$t('c.nocustomer')"
                    :addText="$t('c.selectcustomer')"
                    defaultColor="blue"></UserAssignInput>
            </FormItemCompt>
            <div v-if="customer && customer.length > 0">
                <customerProfile :customer="customer[0]" />
            </div>
            <div>
                <div v-if="prm('customer')" class="bg-gray-200 rounded p-1 px-2 cursor-pointer inline-block text-xs" @click="toogleShowCustomer">{{$t('tickets.hideCustomer')}}</div>
            </div>
            <div v-if="prm('customer')" class="my-2">
                <div class="bg-blue-200 hover:bg-blue-500 hover:text-white text-gray-800 font-bold rounded p-2 cursor-pointer text-center block text-xs" @click="toogleShowCustomer">{{$t('tickets.ticketEntry')}}</div>
            </div>
            <FormItemCompt
                v-if="!prm('customer')"
                class="w-full inline-block px-1"
                labelFor="subscription"
                :labelTitle="$t('tickets.subscription')">
            </FormItemCompt>
            <div class="mx-3" v-if="!prm('customer')">
              <div class="flex justify-center">
                <button v-if="!showSearchBar" @click="showSearchBar = true" class="bg-gray-100 px-2 py-1 rounded shadow hover:bg-gray-200">Search by Subscriptions</button>
              </div>
              <div v-if="showSearchBar" class="flex w-full border rounded-lg bg-gray-200 px-2 py-2">
                <div @click="searchNow()" title="Search" class="mt-2 mr-3">
                  <i class="text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                  </i>
                </div>
                <input v-model="searchSubscription" type="text" class="w-full bg-gray-200 focus:outline-none"  placeholder="Search Subscriptions by ID" >
                <button @click="searchNow()" class="bg-blue-500 text-white rounded-lg px-3 py-2">Search</button>
                <button @click="showSearchBar = false" class="ml-2 bg-red-500 text-white rounded-lg px-3 py-2">Cancel</button>
              </div>
              <div class="mt-4 flex flex-wrap gap-3">
                <div @click="assignSubscription(item)" v-for="item in filteredSubscription" class="bg-gray-100 shadow px-2 py-1 cursor-pointer hover:bg-gray-200">
                  {{ item.address.address }}
                </div>
              </div>
            </div>
        </div>
        <template v-else>
            <div class="my-2 flex">
              <div class="flex-1">
                  <span class="text-xs">{{$t('tickets.customer')}}</span>
                  <div class="inline-block ml-3 p-1 px-2 text-xs bg-gray-200 rounded">{{(customer && customer.length > 0 && customer[0].name) || prm('customer')}}</div>
                  <!-- <div class="bg-gray-200 hover:bg-blue-300 rounded p-1 px-2 cursor-pointer inline-block text-xs ml-2" @click="toogleShowCustomer">{{$t('tickets.showCustomer')}}</div> -->
              </div>
              <div v-if="!closingdate && item.id">
                <div v-if="!profileIsCustomer" @click="closeTicket" class="rounded ml-2 bg-red-500 inline-block px-3 py-2 text-white font-bold cursor-pointer">{{$t('tickets.closetic')}}</div>
              </div>
              <div v-else-if="closingdate && item.id">
                <div v-if="!profileIsCustomer" @click="uncloseTicket" class="rounded ml-2 bg-green-500 inline-block px-3 py-2 text-white font-bold cursor-pointer">{{$t('tickets.unclosetic')}}</div>
              </div>
            </div>
            <div>
                <div>
                    <FormItemCompt
                        class="w-full md:w-1/4 inline-block px-1"
                        labelFor="ticketno"
                        :labelTitle="$t('tickets.ticketNo')">
                        <TextInput
                            name="ticketno"
                            :readonly="true"
                            v-model="item.ticketno"
                            :placeholder="$t('tickets.ticketNoPlc')"
                            defaultColor="blue"></TextInput>
                        <ErrTextCompt :errs="errs && errs.ticketNo"></ErrTextCompt>
                    </FormItemCompt>
                    <FormItemCompt
                        class="w-full md:w-3/4 inline-block px-1"
                        labelFor="title"
                        :labelTitle="$t('tickets.title')"
                        :required="true">
                        <TextInput
                            name="title"
                            v-model="item.title"
                            :readonly="profileIsCustomer || closingdate"
                            :placeholder="$t('c.frontPlc') + $t('tickets.title')"
                            defaultColor="blue"></TextInput>
                        <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
                    </FormItemCompt>
                </div>
                <div>
                  <FormItemCompt
                      class="w-full md:w-1/2 inline-block px-1"
                      labelFor="subscription"
                      :labelTitle="$t('tickets.subscription')">
                    <div>
                      <SelectOne
                          defaultColor="blue"
                          :list="subscriptionList"
                          :selectedItem="item.subscription"
                          :addOrRemoveFunction="addRemoveSubscription"></SelectOne>
                        <ErrTextCompt :errs="errs && errs.subscription"></ErrTextCompt>
                    </div>
                  </FormItemCompt>
                  <FormItemCompt
                      class="w-full md:w-1/4 inline-block px-1"
                      labelFor="department"
                      :labelTitle="$t('tickets.department')"
                      :required="true">
                    <div>
                      <SelectOne
                          defaultColor="blue"
                          :list="departmentList"
                          :selectedItem="item.department"
                          :addOrRemoveFunction="addRemoveDepartment"
                          :readonly="profileIsCustomer || closingdate"></SelectOne>
                      <ErrTextCompt :errs="errs && errs.department"></ErrTextCompt>
                    </div>
                  </FormItemCompt>
                  <FormItemCompt
                      class="w-full md:w-1/4 inline-block px-1"
                      labelFor="level"
                      :labelTitle="$t('tickets.level')"
                      :required="true">
                    <div>
                      <SelectOne
                          defaultColor="blue"
                          :list="[{id: '1', value: '1'},{id: '2', value: '2'},{id: '3', value: '3'}]"
                          :selectedItem="item.level"
                          :addOrRemoveFunction="addRemoveLevel"
                          :readonly="profileIsCustomer || closingdate"></SelectOne>
                      <ErrTextCompt :errs="errs && errs.level"></ErrTextCompt>
                    </div>
                  </FormItemCompt>
                </div>
                <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="description"
                    :labelTitle="$t('tickets.description')"
                    :required="true">
                    <TextareaInput
                        name="description"
                        v-model="item.description"
                        :readonly="profileIsCustomer || closingdate"
                        :placeholder="$t('c.frontPlc') + $t('tickets.description')"
                        defaultColor="blue"
                        :rows="5"></TextareaInput>
                    <ErrTextCompt :errs="errs && errs.description"></ErrTextCompt>
                </FormItemCompt>
                <FormItemCompt
                    labelFor="attachments"
                    :labelTitle="$t('subscriptions.attachments')">
                  <UploadInput
                      :attachments="attachments"
                      :removeFile="removeFile"
                      :addFile="addFile"
                      :basePath="basePath"
                      :token="token"
                      :readOnly="profileIsCustomer || closingdate"></UploadInput>
                </FormItemCompt>
                <!-- req from lai - hide -->
                <!-- <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="assignee"
                    :labelTitle="$t('tickets.assignee')">
                  <UserAssignInput
                      :token="token"
                      :addUser="addAssignee"
                      :removeUser="removeAssignee"
                      :users="assignees"
                      :readonly="profileIsCustomer || closingdate"
                      :multiple="true"
                      :noUserText="$t('tickets.noAssignee')"
                      defaultColor="blue"></UserAssignInput>
                  <ErrTextCompt :errs="errs && errs.assignee"></ErrTextCompt>
                </FormItemCompt> -->
            </div>
            <!-- <div v-if="prm('id')">
                <div>{{$t('tickets.followup')}}</div>
                <FormItemCompt
                    class="w-full inline-block px-1"
                    labelFor="description"
                    :labelTitle="$t('tickets.description')">
                    <TextareaInput
                        name="description"
                        v-model="item.description"
                        :placeholder="$t('c.frontPlc') + $t('tickets.description')"
                        defaultColor="blue"
                        :rows="5"></TextareaInput>
                    <ErrTextCompt :errs="errs && errs.description"></ErrTextCompt>
                </FormItemCompt>
                <div class="text-right">
                    <div class="rounded bg-blue-500 inline-block px-3 py-2 text-white font-bold">{{$t('tickets.followup')}}</div>
                    <div class="rounded ml-2 bg-green-500 inline-block px-3 py-2 text-white font-bold">{{$t('tickets.closetic')}}</div>
                </div>
            </div> -->
        </template>
    </form>
</PopupModal>
</template>
<script lang="ts">
import { computed, defineComponent } from 'vue'
import { getUserName, getFile, deleteFile, basePath, searchUser, getSubscriptions, getUsers, getUser, getUserById, openTicket } from '../../../api'
import config from '../../../config'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import UserAssignInput from '@/components/cvui/form/UserAssignInput.vue'
import UploadInput from '@/components/cvui/form/UploadInput.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import customerProfile from './customerProfile.vue'
import moment from 'moment'
import axios from 'axios'
export default defineComponent({
    name: 'TicketForm',
    components: {
        PopupModal,
        FormItemCompt,
        ErrTextCompt,
        TextInput,
        TextareaInput,
        UserAssignInput,
        UploadInput,
        DatePicker,
        customerProfile,
        SelectOne
    },
    props: {
        item: {
          type: Object,
          required: true,
        },
        token: {
          type: String,
          required: true
        },
        cancel: {
          type: Function,
          required: true
        },
        save: {
          type: Function,
          required: true
        },
        saveSilent: {
          type: Function,
          // required: true
        },
        profile: {
          type: Object,
          required: true,
        },
    },
    data () {
        let showCustomer: boolean = false
        let showReply: boolean = false
        let customer: any = []
        let assignees: any = []
        let attachments: any = []
        let errs: any = {}
        let departmentList: any = computed (() => {
          let array = []
          for (let i = 0; i < config.department.length; i++) {
            let data = config.department[i]
            array.push({ id: data, value: this.$t('tickets.dp_' + data) })
          }
          return array
        })
        let showSearchBar: boolean = false
        let subscriptionList: any = []
        let searchSubscription: any = ''
        let filteredSubscription: any = []
        return {
            showCustomer,
            showReply,
            customer,
            assignees,
            errs,
            basePath,
            attachments,
            departmentList,
            subscriptionList,
            searchUser,
            showSearchBar,
            moment,
            searchSubscription,
            filteredSubscription
        }
    },
    mounted () {
      if (this.item) {
        this.assignees = []
        this.customer = []
        this.attachments = []
        this.subscriptionList = []
        this.customer = []
        this.getAssignee()
        this.getCustomer()
        this.getSubscription()
        this.getAttachment()
      }
    },
    methods: {
        searchNow () {
          getSubscriptions({token: this.token, keywords: this.searchSubscription}).then((res: any) => {
            this.filteredSubscription = res.data
          })
        },
        assignSubscription (p: any) {
          this.item.subscription = p.id
          getUserById({token: this.token, id: p.customer}).then((res: any) => {
            const modifiedCustomerData = {
              ...res,
              ID: res.id
            };
            delete modifiedCustomerData.id;
            this.addCustomer(modifiedCustomerData)
          })            
        },
        getAssignee () {
          if (this.item.assignee) {
            let existIds = this.assignees.map((g: any) => g.id)
            for (var i = 0; i < this.item.assignee.length; i++) {
              let p = this.item.assignee[i]
              if (existIds.indexOf(p) == -1) {
                getUserName({ token: this.token, id: p }).then((rs: any) => {
                  this.assignees.push(rs)
                }).catch((err: any) => {
                  if (err) {
                    this.assignees.push({ id: p })
                  }
                })
              }
            }
          }
        },
        getCustomer () {
          if (this.item.customer) {
            getUserName({ token: this.token, id: this.item.customer }).then((rs: any) => {
              this.customer.push(rs)
            }).catch((err: any) => {
              if (err) {
                this.customer.push({ id: this.item.customer })
              }
            })
          }
        },
        getSubscription () {
          if (this.item.customer) {
            getSubscriptions({ token: this.token, skip: 0, customer: this.item.customer, params: ['customer'] }).then(rs => {
              let data = rs.data
              for (let sd = 0; sd < data.length; sd++) {
                let data2 = data[sd]
                this.subscriptionList.push({ id: data2.id, value: data2.username + ' / ' + moment(data2.activationdate).format('YYYY-MM-DD') + ' / ' + data2.address.address })
              }
            })
          }
        },
        getAttachment () {
          if (this.item.attachments) {
            for (let i = 0; i < this.item.attachments.length; i++) {
              let datai = this.item.attachments[i]
              getFile({ token: this.token, id: datai }).then(rs => {
                this.attachments.push(rs.data)
              })
            }
          }
        },
        // start attachments
        addFile (p: any) {
          if (!this.item.attachments) {
          this.item.attachments = []
          }
          this.item.attachments.push(p.file.id)
          this.attachments.push(p.file)
          if (this.saveSilent) {
            this.saveSilent(this.item)
          }
        },
        removeFile (p: any) {
          let i = this.item.attachments.indexOf(p)
          if (i > -1) {
            this.item.attachments.splice(i, 1)
            // this.saveSilent(this.item)
            if (this.saveSilent) {
              this.saveSilent(this.item)
            }
          }
          let list = this.attachments.map((p2: any) => p2.id || p2.ID)
          let j = list.indexOf(p)
          this.attachments.splice(j, 1)
          deleteFile({id: p, token: this.token})
        },
        // end attachments
        validateForm () {
          let p = true
          if (!this.item.title || this.item.title.trim().length == 0) {
            this.errs['title'] = 'c.fieldRequired'
            p = false
          }
          if (!this.item.description || this.item.description.trim().length == 0) {
            this.errs['description'] = 'c.fieldRequired'
            p = false
          } else if (this.item.description.length < 10) {
            this.errs['description'] = 'c.fieldNotLessThan10'
            p = false
          }
          if (!this.item.level || this.item.level.trim().length == 0) {
            this.errs['level'] = 'c.fieldRequired'
            p = false
          }
          if (!this.item.department || this.item.department.trim().length == 0) {
            this.errs['department'] = 'c.fieldRequired'
            p = false
          }
          return p
        },
        preventsubmit () {
          this.submitNow()
        },
        submitNow() {
          if (this.validateForm()) {
            this.save(this.item)
          }
          return false
        },
        loadReply () {
            this.showReply = true
        },
        toogleShowCustomer () {
            this.$nextTick(()=> {
                this.showCustomer = !this.showCustomer
            })
        },
        prm (i: any) {
            return this.item && this.item[i]
        },
        formattedDate(date: any) {
          const newdate = new Date(date);
          
          return newdate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });
        },
        // start customer
        addCustomer (p: any) {
            if (!this.item.customer) {
                this.item.customer = ''
            }
            if (this.item.customer !== p.ID) {
                this.item.customer = p.ID
                this.customer.push(p)
            }
            this.showCustomer = true
            this.errs = {}
        },
        removeCustomer (p: any) {
            if (this.item.customer === p.ID || this.item.customer === p.id) {
                this.item.customer = ''
                this.customer = []
            }
        },
        // end customer
        // start assignee
        addAssignee (p: any) {
            if (!this.item.assignee) {
                this.item.assignee = ''
            }
            if (this.item.assignee !== p.ID) {
                this.item.assignee = p.ID
                this.assignees.push(p)
            }
        },
        removeAssignee (p: any) {
            if (this.item.assignee === p.ID || this.item.assignee === p.id) {
                this.item.assignee = ''
                this.assignees = []
            }
        },
        // end assignee
        addRemoveLevel (item: any, index: any) {
          if (this.item.level === item.id) {
            this.item.level = ''
          } else {
            this.item.level = item.id
          }
        },
        addRemoveDepartment (item: any, index: any) {
          if (this.item.department === item.id) {
            this.item.department = ''
          } else {
            this.item.department = item.id
          }
        },
        addRemoveSubscription (item: any, index: any) {
          this.$router.push({ name: 'subscriptionsl1', params: { id: item.id, ticketId: this.item.id } })
          if (this.item.subscription === item.id) {
            this.item.subscription = ''
          } else {
            this.item.subscription = item.id
          }
        },
        closeTicket () {
          this.item.closingdate = new Date()
          this.save(this.item)
        },
        uncloseTicket () {          
          openTicket({id: this.item.id, token: this.token})
          this.save(this.item)
        }
    },
    computed: {
      profileIsCustomer (): any {
        let p : any = this.profile
        return p && p.customer
      },
      closingdate (): any {
        let p: any = this.item
        return p && p.closingdate
      },
      isDev (): boolean {
        return this.profile && this.profile.scopes && this.profile.scopes.indexOf('dev') > -1
      }
    }
})
</script>
