<template>
  <div>
    <adminheader
        :title="$t('navigations.tickets')"
        :addFunction="addTicket"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div class="mx-6 flex justify-end">
      <select v-model="statustxt" @change="filterFunc"
        class="text-sm placeholder-gray-500 border rounded bg-white p-2 focus:ring-blue-500">
        <option value="" disabled>Filter by status</option>
        <option value="1">Open</option>
        <option value="2">Close</option>
      </select>
    </div>
    <div v-if="databases === null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
            <button @click="editRow(slotProps.item, slotProps.index)" :title="$t('c.edit')" class="inline-block bg-blue-500 hover:bg-blue-700 text-white p-2 mr-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
            </button>
            <!-- <button @click="followUpRow(slotProps.item, slotProps.index)" :title="$t('tickets.followup')" class="inline-flex bg-purple-500 hover:bg-purple-700 text-white p-2 mr-2 rounded-full">
              <svgCollection icon="clipboardcheck" dclass="h-4 w-4"></svgCollection>
            </button> -->
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :save="saveFunc"
            :profile="profile"></dform>
    </template>
    <!-- <template v-if="followUpItem">
      <followUpForm
          :item="followUpItem"
          :cancel="cancelNow"
          :token="token"></followUpForm>
    </template> -->
  </div>
</template>
<script lang="ts">
// #TODO Create Ticket
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { ticketStore } from '../../../store/ticket-store'
import { getUserName, getNextSupportID, pushSupportID, getTicket } from '../../../api'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import followUpForm from './followUp_form.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    const ticketState = ticketStore.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        ticketStore: ticketStore,
        ticketState: ticketStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    followUpForm,
    svgCollection
  },
  mounted () {
    if (this.$route.params.id) {
      let ticketId = this.$route.params.id      
      getTicket({token: this.token, id: ticketId}).then((res: any) => {                        
          this.editRow(res.data, 0)
      }).catch((err: any) => {
          console.error(err);   
      })
    }
    this.reload()
    setTimeout(() => {
      this.getUserList()
    }, 300)
  },
  data () {
    let prof : any = auth2Store.getState().profile
    let item: any = null
    let deleteItem : any = null
    let itemStyle: any = {
      ticketno: '',
      title: '',
      description: '',
      subscription: '',
      level: '1',
      department: '',
      assignee: [prof.id],
      attachments: [],
      user: prof.customer ? '' : prof.id,
      customer: prof.customer ? prof.id : '',
      closingdate: null
    }
    let table: any = {
        limit: 10,
        page: 1,
        keywords: ''
    }
    let followUpItem: any = null
    let keywords: string = ''
    let userlist: any = []
    let statustxt: string = '1'
    return {
      prof,
      item,
      deleteItem,
      itemStyle,
      table,
      followUpItem,
      keywords,
      userlist,
      statustxt
    }
  },
  methods: {
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    searchFunc(p: string) {
      this.keywords = p
      this.searchNow()
    },
    filterFunc() {
      this.table.page = 1;
      this.table.openticket = this.statustxt;
      this.loadDatabase();
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.table.openticket = this.statustxt;
      this.loadDatabase()
      // this.getUserList()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      this.ticketStore.getTickets(p)
    },
    pageChange (p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = this.itemStyle
    },
    addTicket () {
      this.initItem()
      setTimeout(() => {
        getNextSupportID({token: this.token}).then((res: any) => {
          if (res && res.data) {
            this.item.ticketno = res.data
          }
        })
      }, 500)
    },
    cancelNow () {
      this.item = null
      this.itemStyle = {
        ticketno: '',
        title: '',
        description: '',
        subscription: '',
        level: '1',
        department: '',
        assignee: [this.prof.id],
        attachments: [],
        user: this.prof.customer ? '' : this.prof.id,
        customer: this.prof.customer ? this.prof.id : '',
        closingdate: null
      }
      this.followUpItem = null
      this.reload()
    },
    editRow (item: any, index: number) {
      if (!this.item) {
        this.item = Object.assign({id: item.ID}, item)
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
      }
    },
    duplicateRow (p: any, i: any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    followUpRow (item: any, index: any) {
      if (!this.followUpItem) {
        this.followUpItem = Object.assign({id: item.ID}, item)
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.followUpItem).indexOf(data[0]) === -1) {
            this.followUpItem[data[0]] = data[1]
          }
        }
      }
    },
    saveFunc (p: any) {
      if (p.id) {
        this.ticketStore.updateTicket({ form: p, id: p.id, token: this.token })
      } else {
        this.ticketStore.createTicket({ form: p, token: this.token })
      }
      this.reload()
    },
    deleteRow (p: any,i: any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.title + '?',
        proceedTxt:  this.$t('c.yes'),
        proceedFunc: () => { this.deleteNow(p.id)},
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      ticketStore.deleteTicket({ token: this.token, id })
    },
    getUserList () {
      let useridlist2 = this.userlist.map((p: any) => p.id)
      let useridlist = []
      if (this.databases) {
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i]
          if (data.customer) {
            useridlist.push(data.customer)
          }
          if (data.user) {
            useridlist.push(data.user)
          }
          if (data.assignee.length > 0) {
            useridlist.concat(data.assignee)
          }
        }
        useridlist = this.removeDuplicate(useridlist)
        useridlist = useridlist.filter((p: any) => useridlist2.indexOf(p) === -1)
        useridlist.filter(k => {
          getUserName({ token: this.token, id: k }).then((rs: any) => {
            this.userlist.push({
              id: k,
              value: rs.name || rs.email || k + ' (' + this.$t('c.noNameNoEmail') + ')'
            })
          })
        })
      }
    },
    removeDuplicate (arraylist: any) {
      return arraylist = [...new Set(arraylist)]
    }
  },
  computed: {
    columns () {
      let ul: any = this.userlist
      return [
        { title: 'tickets.ticketNo', key: 'ticketno', type: 'string', class: 'text-center' },
        { title: 'tickets.createdAt', key: 'created_at', type: 'date', class: 'text-center' },
        { title: 'tickets.title', key: 'title', type: 'string', class: 'text-center' },
        { title: 'tickets.customer', key: 'customer', type: 'string', objectlist: ul, class: 'text-center' },
        { title: 'tickets.assignee', key: 'assignee', type: 'objectarray', objectlist: ul, class: 'text-center' },
        // { title: 'tickets.closingdate', key: 'closingdate', type: 'date', class: 'text-center' },
        { title: 'tickets.status', key: 'closingdate', type: 'booleanTicket', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return ticketStore.getState().tickets
    },
    ticketCreate () {
      return ticketStore.getState().ticketCreate
    },
    ticketCreateSuccess () {
      return ticketStore.getState().ticketCreateSuccess
    },
    ticketCreateError () {
      return ticketStore.getState().ticketCreateError
    },
    ticketUpdate () {
      return ticketStore.getState().ticketUpdate
    },
    ticketUpdateSuccess () {
      return ticketStore.getState().ticketUpdateSuccess
    },
    ticketUpdateError () {
      return ticketStore.getState().ticketUpdateError
    },
    ticketDeleteSuccess () {
      return ticketStore.getState().ticketDeleteSuccess
    },
  },
  watch: {
    databases (p) {
      if (p) {
        this.getUserList()
      }
    },
    ticketCreateSuccess (p) {
      if (p) {
        this.item = null
        this.itemStyle = {
          ticketno: '',
          title: '',
          description: '',
          subscription: '',
          level: '1',
          department: '',
          assignee: [this.prof.id],
          attachments: [],
          user: this.prof.customer ? '' : this.prof.id,
          customer: this.prof.customer ? this.prof.id : '',
          closingdate: null
        }
        pushSupportID({token: this.token}).then((res: any) => {
          //
        })
        this.getUserList()
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.ticket'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    ticketCreateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('tickets.ticket'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    ticketUpdateSuccess (p) {
      if (p) {
        this.item = null
        this.itemStyle = {
          ticketno: '',
          title: '',
          description: '',
          subscription: '',
          level: '1',
          department: '',
          assignee: [this.prof.id],
          attachments: [],
          user: this.prof.customer ? '' : this.prof.id,
          customer: this.prof.customer ? this.prof.id : '',
          closingdate: null
        }
        this.getUserList()
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.ticket'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    ticketUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('tickets.ticket'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    ticketDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  },
})
</script>
