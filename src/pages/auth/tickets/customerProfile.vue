<template>
    <div>
        <div class="border-b hover:bg-gray-200" v-for="g in attrs" :key="g">
            <div class="inline-block w-1/4 text-xs">{{$t('tickets.' + g)}}</div>
            <div class="inline-block  w-3/4 text-sm">{{p(customer, g)}}</div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
    name: 'CustomerProfile',
    props: {
        customer: {
            type: Object,
            required: true,
        },
    },
    methods: {
        p(p: any, i: any){
            return p && p[i] || '---'
        }
    },
    data () {
        let attrs : any = ['name', 'email', 'identityno', 'mobile', 'mothersname', 'fathersname']
        return {
            attrs,
        }
    }
})
</script>
