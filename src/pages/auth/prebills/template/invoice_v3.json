{"basePdf": "data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PAovRmlsdGVyIC9GbGF0ZURlY29kZQovTGVuZ3RoIDM4Cj4+CnN0cmVhbQp4nCvkMlAwUDC1NNUzMVGwMDHUszRSKErlCtfiyuMK5AIAXQ8GCgplbmRzdHJlYW0KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL01lZGlhQm94IFswIDAgNTk1LjQ0IDg0MS45Ml0KL1Jlc291cmNlcyA8PAo+PgovQ29udGVudHMgNSAwIFIKL1BhcmVudCAyIDAgUgo+PgplbmRvYmoKMiAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzQgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjMgMCBvYmoKPDwKL3RyYXBwZWQgKGZhbHNlKQovQ3JlYXRvciAoU2VyaWYgQWZmaW5pdHkgRGVzaWduZXIgMS4xMC40KQovVGl0bGUgKFVudGl0bGVkLnBkZikKL0NyZWF0aW9uRGF0ZSAoRDoyMDIyMDEwNjE0MDg1OCswOScwMCcpCi9Qcm9kdWNlciAoaUxvdmVQREYpCi9Nb2REYXRlIChEOjIwMjIwMTA2MDUwOTA5WikKPj4KZW5kb2JqCjYgMCBvYmoKPDwKL1NpemUgNwovUm9vdCAxIDAgUgovSW5mbyAzIDAgUgovSUQgWzwyODhCM0VENTAyOEU0MDcyNERBNzNCOUE0Nzk4OUEwQT4gPEY1RkJGNjg4NkVERDZBQUNBNDRCNEZDRjBBRDUxRDlDPl0KL1R5cGUgL1hSZWYKL1cgWzEgMiAyXQovRmlsdGVyIC9GbGF0ZURlY29kZQovSW5kZXggWzAgN10KL0xlbmd0aCAzNgo+PgpzdHJlYW0KeJxjYGD4/5+RUZmBgZHhFZBgDAGxakAEP5BgEmFgAABlRwQJCmVuZHN0cmVhbQplbmRvYmoKc3RhcnR4cmVmCjUzMgolJUVPRgo=", "schemas": [{"companyName": {"type": "text", "position": {"x": 68, "y": 5.14}, "width": 79.45, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontName": "GothamBold"}, "logo": {"type": "image", "position": {"x": 10.12, "y": 5.14}, "width": 50, "height": 22, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1}, "companyAddress": {"type": "text", "position": {"x": 68, "y": 10}, "width": 96.64, "height": 11.76, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1, "fontName": "Gotham"}, "itemTitle": {"type": "text", "position": {"x": 8, "y": 27.94}, "width": 57.22, "height": 10.97, "alignment": "left", "fontSize": 24, "characterSpacing": 0, "lineHeight": 1, "fontName": "GothamBold", "fontColor": "#642469"}, "customerNameWtTitle": {"type": "text", "position": {"x": 8, "y": 40}, "width": 89, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontName": "EffraBold"}, "user": {"type": "text", "position": {"x": 8, "y": 45}, "width": 89, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "<PERSON><PERSON><PERSON>"}, "customerAddress": {"type": "text", "position": {"x": 8, "y": 50}, "width": 89, "height": 8.32, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1}, "usercontact": {"type": "text", "position": {"x": 8, "y": 81}, "width": 35, "height": 7, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1}, "sidLabel": {"type": "text", "position": {"x": 132, "y": 40}, "width": 30, "height": 7, "alignment": "right", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "EffraBold"}, "sid": {"type": "text", "position": {"x": 170, "y": 40}, "width": 30, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "<PERSON><PERSON><PERSON>"}, "billLabel": {"type": "text", "position": {"x": 140, "y": 45}, "width": 22, "height": 7, "alignment": "right", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "EffraBold"}, "billNo": {"type": "text", "position": {"x": 170, "y": 45}, "width": 35, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1}, "billdateLabel": {"type": "text", "position": {"x": 140, "y": 50}, "width": 22, "height": 7, "alignment": "right", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "EffraBold"}, "billDate": {"type": "text", "position": {"x": 170, "y": 50}, "width": 29, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1}, "summaryTitle": {"type": "text", "position": {"x": 6.96, "y": 60.5}, "width": 85, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469", "fontName": "EffraBold"}, "empty2": {"type": "text", "position": {"x": 6.5, "y": 70.5}, "width": 200, "height": 7, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a"}, "itemsLabel": {"type": "text", "position": {"x": 12.85, "y": 70.5}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "dateTimeLabel": {"type": "text", "position": {"x": 50, "y": 70.5}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "toNumberLabel": {"type": "text", "position": {"x": 100, "y": 70.5}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "durationLabel": {"type": "text", "position": {"x": 140, "y": 70.5}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "amountLabel": {"type": "text", "position": {"x": 165, "y": 70.5}, "width": 35, "height": 7, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "items": {"type": "text", "position": {"x": 9.79, "y": 77.74}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "dateTime": {"type": "text", "position": {"x": 50, "y": 77.74}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "toNumber": {"type": "text", "position": {"x": 100, "y": 77.74}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "duration": {"type": "text", "position": {"x": 140, "y": 77.74}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "amounts": {"type": "text", "position": {"x": 165, "y": 77.74}, "width": 37.38, "height": 48, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "totalLabel2": {"type": "text", "position": {"x": 140.74, "y": 140.43}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}, "total2": {"type": "text", "position": {"x": 175.04, "y": 140.43}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}, "empty3": {"type": "text", "position": {"x": 129.86, "y": 135.14}, "width": 70, "height": 0.5, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a"}, "footerLabel1": {"type": "text", "position": {"x": 12.69, "y": 180}, "width": 35, "height": 5, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#000000"}, "footerLabel2": {"type": "text", "position": {"x": 12.37, "y": 186}, "width": 124.17, "height": 21.14, "alignment": "left", "fontSize": 8, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#000000"}, "footerfooter1": {"type": "text", "position": {"x": 12.37, "y": 260}, "width": 150, "height": 5, "alignment": "left", "fontSize": 24, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#000000", "fontName": "GothamBold"}, "footerfooter2": {"type": "text", "position": {"x": 12.37, "y": 271.6}, "width": 170.17, "height": 21.14, "alignment": "left", "fontSize": 12, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#636363"}, "qrcode": {"type": "image", "position": {"x": 175.04, "y": 255.6}, "width": 21.14, "height": 21.14, "alignment": "left", "fontSize": 12, "characterSpacing": 0, "lineHeight": 1}}], "columns": ["sid", "<PERSON>d<PERSON><PERSON><PERSON>", "companyName", "logo", "companyAddress", "itemTitle", "customerNameWtTitle", "<PERSON><PERSON><PERSON><PERSON>", "billdateLabel", "user", "customerAddress", "usercontact", "depositLabel", "billNo", "billDate", "deposit", "empty", "totalLabel", "total", "summaryTitle", "empty2", "field22", "amountLabel", "items", "amounts", "subtotalLabel", "taxLabel", "subtotal", "tax", "bf<PERSON><PERSON>l", "bf", "totalLabel2", "total2", "empty3", "footerLabel1", "footerLabel2", "footerfooter1", "footerfooter2", "qrcode"]}