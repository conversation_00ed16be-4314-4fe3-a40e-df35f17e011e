<template>
  <div>
    <adminheader
        :title="$t('navigations.prebills')"
        :addFunction="addBilling"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div v-if="databases == null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
          <button @click="editRow(slotProps.item, slotProps.index)" class="inline-block w-8 h-8 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
            <svgcollection icon="edit" dclass="inline-block w-4 h-4 " />
          </button>
          <button @click="openqr(slotProps.item.id)" class="inline-block w-8 h-8 bg-blue-500 hover:bg-blue-700 text-white mr-2 rounded-full">
            <svgcollection icon="cash" dclass="inline-block w-4 h-4 " />
          </button>
          <button @click="getPayment(slotProps.item)" class="inline-block w-8 h-8 bg-green-500 hover:bg-green-700 text-white mr-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="inline-block w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z" />
            </svg>
          </button>
          <!-- <button @click="deleteRow(slotProps.item, slotProps.index)" class="inline-block w-8 h-8 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
            <svgcollection icon="trash" dclass="inline-block w-4 h-4 " />
          </button> -->
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :save="saveFunc"
            :saveSilent="saveSilent"></dform>
    </template>
    <template v-if="showqr">
      <PopupModal defaultColor="blue" modalWidthPercent="40" title="Bill Link"
        :btnYesText="$t('c.submit')" :btnNoText="$t('c.cancel')" :btnNoFunction="closeqr">
        <div class="flex flex-col items-center">
          <qrcode-vue :id="`qrcode_showqr`" class="mb-4" :value="'https://paymentapi.highfi.com.my/api/payexp/'+showqr" level="M" render-as="canvas" />
          <p class="text-gray-600">https://paymentapi.highfi.com.my/api/payexp/{{ showqr }}</p>
          <button @click="copyqr('https://paymentapi.highfi.com.my/api/payexp/'+ showqr)" class="px-2 py-1 rounded bg-gray-200 mt-2 font-semibold" 
            :class="copied ? 'bg-green-500 text-white': 'bg-gray-200'">
            {{ copied ? 'Copied to clipboard!👍' : 'Copy Link' }}
          </button>
        </div>
      </PopupModal>
    </template>
    <template v-if="Object.keys(showPayments).length > 0">
      <PopupModal defaultColor="blue" modalWidthPercent="80" title="Payments"
        :btnYesText="$t('c.submit')" :btnNoText="$t('c.cancel')" :btnNoFunction="closepayment">
        <div class="flex flex-col">
          <dtable
          :columns="paymentColumns"
          :data="showPayments"
          columnColor="white">
          </dtable>
          <dpagination
              :total="showPayments && showPayments.total || 0"
              :page="tablePayment.page"
              :limit="tablePayment.limit"
              :pageChange="pageChange"
              defaultColor="blue"
          />
        </div>
      </PopupModal>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { prebillsStore } from '../../../store/prebill-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import Svgcollection from '../../../components/cvui/svgcollection.vue'
import PopupModal from '@/components/cvui/Modal.vue'
import QrcodeVue from 'qrcode.vue'
import { getPayments } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        prebillsStore: prebillsStore,
        billingState: prebillsStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    Svgcollection,
    PopupModal,
    QrcodeVue
  },
  mounted () {
    this.reload()
  },
  data () {
    let profile = auth2Store.getState().profile
    let item: any = undefined
    let deleteItem: any = undefined
    let itemStyle: any = {
      // customer: this.profile.customer ? this.profile.id : '',
      user: profile && profile.id,
      subscriptions: [],
      items: [],
      billdate: '',
      duedate: '',
      amountcurrent: 0,
      amountpaid: 0,
      amountbf: 0,
      amountcf: 0,
      totalamount: 0,
      alreadycf: false,
      status: true
    }
    let table : any = {
        limit: 10,
        page: 1,
        keywords: ''
    }
    let tablePayment: any = {
          limit: 10,
          page: 1,
          keywords: '',
          bill: '',
          token: this.token,
          status: true,            
      }
    let keywords: string = ''
    let showqr: string = ''
    let showPayments: any =  {}
    let copied: boolean = false
    return {
      item,
      deleteItem,
      itemStyle,
      table,
      tablePayment,
      keywords,
      showqr,
      showPayments,
      copied
    }
  },
  methods: {
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    searchFunc (p: string) {
      // this.keywords = p
      this.table.billno = p
      this.searchNow()
    },
    getPayment (item: any) {    
      this.tablePayment.bill = item.id
      getPayments({...this.tablePayment, token: this.token }).then((res: any) => {
        this.showPayments = res
      })
    },
    closepayment () {
      this.showPayments = {}
    },
    openqr (id: any) {
      this.showqr = id
    },
    closeqr () {
      this.showqr = ''
    },
    copyqr (url: any) {
      navigator.clipboard.writeText(url)
      .then(() => {
        this.copied = true;
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      })
      .catch(err => {
        console.error('Failed to copy URL: ', err);
      });
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      this.prebillsStore.getPrebills(p)
    },
    pageChange (p:any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = JSON.parse(JSON.stringify(this.itemStyle))
    },
    addBilling () {
      this.initItem()
    },
    cancelNow () {
      this.item = null
      // this.reload()
    },
    editRow (item: any, index: Number) {
      if (!this.item) {
        this.item = JSON.parse(JSON.stringify(Object.assign({id: item.ID}, item)))
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
      }
    },
    duplicateRow (p:any, i:any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc (p:any) {
      if (p.id) {
        console.log(p)
        this.prebillsStore.updatePrebills({ form: p, id: p.id, token: this.token })
      } else {
        this.prebillsStore.createPrebills({ form: p, token: this.token })
      }
    },
    saveSilent (p:any) {
      this.prebillsStore.updatePrebills({ form: p, id: p.id, token: this.token })
    },
    deleteRow (p:any,i:any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.id + ' | ' + p.billno + '?',
        proceedTxt:  this.$t('c.okay'),
        proceedFunc: () => { this.deleteNow(p.id)},
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      prebillsStore.deletePrebills({ token: this.token, id })
    }
  },
  computed: {
    columns () {
      return [
        { title: 'billings.billingid', key: 'billno', type: 'string', class: 'text-center' },
        { title: 'billings.total', key: 'totalamount', type: 'price', class: 'text-center' },
        { title: 'billings.billdate', key: 'billdate', type: 'date', class: 'text-center' },
        { title: 'billings.billdate', key: 'billdate', type: 'string', class: 'text-center' },
        { title: 'billings.duedate', key: 'duedate', type: 'date', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    paymentColumns () {
      return [
        { title: 'payments.paymentdate', key: 'paymentdate', type: 'date', class: 'text-center' },
        { title: 'payments.createdate', key: 'created_at', type: 'date', class: 'text-center' },
        { title: 'payments.amount', key: 'amount', type: 'price', class: 'text-center' },        
        { title: 'payments.platform', key: 'platform', type: 'string', class: 'text-center' },
        { title: 'payments.statustxt', key: 'statustxt', type: 'string', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return prebillsStore.getState().prebills
    },
    billingUpdate () {
      return prebillsStore.getState().prebillsUpdate
    },
    billingUpdateSuccess () {
      return prebillsStore.getState().prebillsUpdateSuccess
    },
    billingUpdateError () {
      return prebillsStore.getState().prebillsUpdateError
    },
    billingDeleteSuccess () {
      return prebillsStore.getState().prebillsDeleteSuccess
    },
  },
  watch: {
    billingUpdateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('billings.billing'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    billingUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('billings.billing'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    billingDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  },
})
</script>
