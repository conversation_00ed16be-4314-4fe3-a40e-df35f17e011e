<template>
  <div>
    <adminheader
        :title="$t('navigations.technicalinstallationorder')"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div class="mx-6 flex justify-between">
      <div class="flex">
        <div class="block relative bg-white shadow rounded-lg">
        </div>
        <div>
          <span v-if="statustxt !== ''" @click="reload"
          class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 cursor-pointer"
          style="margin-left: 12px;">Remove filter</span>
        </div>
      </div>
      <select v-model="statustxt" @change="filterFunc"
        class="text-sm placeholder-gray-500 border rounded bg-white p-2 focus:ring-blue-500">
        <option value="" selected disabled>Filter by status</option>
        <option value="pendinginstall">Pending Installation</option>
        <option value="pendingverification">Pending Verification</option>
      </select>
    </div>
    <div v-if="databases == null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
            <button @click="editRow(slotProps.item, slotProps.index)" class="inline-block bg-blue-500 hover:bg-blue-700 text-white p-2 mr-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
            </button>
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :profile="profile"
            :cancel="cancelNow"
            :token="token"
            :showEquipments="true"
            :installerOnlyView="true"
            :save="saveFunc"
            :saveSilent="saveSilent"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { subscriptionStore } from '../../../store/subscription-store'
import { getUserName, getPlan } from '../../../api'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import moment, { Moment } from 'moment';
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    svgicon
  },
  mounted () {
    this.reload()
    // setTimeout(() => {
    //   this.getPlanList()
    //   this.getUserList()
    // }, 2000)
  },
  data () {
    let item: any = undefined
    let deleteItem: any = undefined
    let auth2State = auth2Store.getState()
    let profile:any = computed(() => auth2State.profile )
    let itemStyle = {
        user: profile.id,
        customer: '',
        agent: '',
        plan: '',
        suggestusername: [],
        username: '',
        userpassword: '',
        subscribedate: null,
        activationdate: null,
        contractenddate: null,
        terminationdate: null,
        attachments: [],
        remark: '',
        equipments: [],
        address: {
          address: '',
          unit: '',
          city: '',
          state: '',
          postcode: '',
          level: '',
          block: ''
        },
        billingaddress: {
          address: '',
          unit: '',
          city: '',
          state: '',
          postcode: '',
          level: '',
          block: ''
        },
        phone: '',
        status: true,
        statustxt: 'new',
        profile,
    }
    let table: any = {
      limit: 10,
      page: 1,
      keywords: ''
    }
    let keywords: string = ''
    let userlist: any = []
    let planlist: any = []
    let ageingDataList: any = []
    let customerProfile: any = undefined
    let statuslist: any = [
      {
        id: 'pendinginstall',
        value: this.$t('subscriptions.pendinginstall')
      },
      {
        id: 'new',
        value: this.$t('subscriptions.new')
      },
      {
        id: 'terminated',
        value: this.$t('subscriptions.terminated')
      },
      {
        id: 'returnorder',
        value: this.$t('subscriptions.returnorder')
      },
      {
        id: 'relocation',
        value: this.$t('subscriptions.relocation')
      },
      {
        id: 'active',
        value: this.$t('subscriptions.active')
      }
    ]
    let lastgetPlan: number = Date.now() - 5000
    let lastgetUser: number = Date.now() - 5000
    let statustxt: string = 'pendinginstall'
    return {
      statustxt,
      item,
      deleteItem,
      profile,
      itemStyle,
      table,
      userlist,
      planlist,
      ageingDataList,
      keywords,
      statuslist,
      lastgetPlan,
      lastgetUser,
      customerProfile,
      columns: computed(() => {
        let userlist = this.userlist
        let planlist = this.planlist
        return [
          { title: 'subscriptions.customer', key: 'customer', type: 'string', objectlist: userlist, class: 'text-center' },
          // { title: 'subscriptions.agent', key: 'agent', type: 'string', objectlist: userlist, class: 'text-center' },
          { title: 'subscriptions.plan', key: 'plan', type: 'string', objectlist: planlist, class: 'text-center' },
          // { title: 'subscriptions.contact', key: 'contact', type: 'string', class: 'text-center' },
          //  { title: 'subscriptions.status', key: 'statustxt', type: 'string', class: 'text-center' },
          { title: 'subscriptions.subscribeDate', key: 'subscribedate', type: 'date', class: 'text-center' },
          { title: 'subscriptions.ageing', key: 'subscribedate', type: 'string', objectlist: ageingDataList, class: 'text-center' },
          { title: 'subscriptions.status', key: 'statustxt', type: 'string',objectlist: statuslist, class: 'text-center' },
          { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
        ]
      })
    }
  },
  methods: {
    getListChecker () {
      this.getPlanList()
      this.getUserList()
      this.calculateAgeing()
    },
    subscribeNew () {
      this.item = {
        customer: '',
        agent: '',
        plan: '',
        suggestusername: ['','',''],
        username: '',
        userpassword: '',
        subscribedate: '',
        contractenddate: '',
        attachments: [],
        remark: '',
        address: {
          unit: '',
          level: '',
          block: '',
          address: '',
          postcode: '',
          city: '',
          state: '',
          country: '',
        },
        contact: '',
        contact2: '',
        deposit: 0,
        advancedpayment: 0,
        statustxt: 'new',
        status: true
      }
    },
    searchFunc (p: string) {
      this.keywords = p
      this.searchNow()
    },
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token, statustxt: this.statustxt }
      subscriptionStore.getSubscriptions(p)
    },
    pageChange (p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = this.itemStyle
    },
    addSubscription () {
      this.initItem()
    },
    cancelNow () {
      this.item = null
      this.reload()
    },
    editRow (item: any, index: any) {
      if (!this.item) {
        this.item = Object.assign({id: item.ID}, item)
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
        if (!this.item.installerdetails) {
          this.item.installerdetails = {};
        }
        this.item.voipno = this.item.voipno || '';
        this.item.installerdetails.contact = this.item.installerdetails.contact || '';
        this.item.installerdetails.routermac = this.item.installerdetails.routermac || '';
        this.item.installerdetails.readingfws = this.item.installerdetails.readingfws || '';
        this.item.installerdetails.readingmdf = this.item.installerdetails.readingmdf || '';
        this.item.installerdetails.date = this.item.installerdetails.date || '';
        this.item.installerdetails.inswo = this.item.installerdetails.inswo || '';
        this.item.installerdetails.insst = this.item.installerdetails.insst || '';
        this.item.installerdetails.insfws = this.item.installerdetails.insfws || '';
        this.item.installerdetails.insmdf = this.item.installerdetails.insmdf || '';
        this.item.installerdetails.inscbl = this.item.installerdetails.inscbl || '';
        this.item = JSON.parse(JSON.stringify(this.item))
      }
    },
    duplicateRow (p: any, i: any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc (p: any) {
      // number fix
      if (p.deposit) {
        p.deposit = parseFloat(p.deposit)
      }
      if (p.advancedpayment) {
        p.advancedpayment = parseFloat(p.advancedpayment)
      }
      if (p.id) {
        subscriptionStore.updateSubscription({ form: p, id: p.id, token: this.token })
      } else {
        subscriptionStore.createSubscription({ form: p, token: this.token })
      }
      this.reload()
    },
    saveSilent (p: any) {
      subscriptionStore.updateSubscription({ form: p, id: p.id, token: this.token })
    },
    deleteRow (p: any,i: any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.title + '?',
        proceedTxt:  this.$t('c.yes'),
        proceedFunc: () => { this.deleteNow(p.id)},
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      subscriptionStore.deleteSubscription({ token: this.token, id })
    },
    getUserList () {
      let useridlist2 = this.userlist.map((p: any) => p.id)
      let useridlist = []
      // this.userlist = []
      if (this.databases) {
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i]
          if (data.customer) {
            useridlist.push(data.customer)
          }
          if (data.agent) {
            useridlist.push(data.agent)
          }
          if (data.user) {
            useridlist.push(data.user)
          }
        }
        useridlist = this.removeDuplicate(useridlist)
        useridlist = useridlist.filter((p: any) => useridlist2.indexOf(p) === -1)
        useridlist.filter(k => {
          getUserName({ token: this.token, id: k }).then((rs: any) => {
            this.userlist.push({
              id: k,
              value: rs.name || rs.email || k + ' (' + this.$t('c.noNameNoEmail') + ')'
            })
          })
        })
      }
    },
    getPlanList () {
      let planidlist2 = this.planlist.map((p: any) => p.id)
      let planidlist = []
      // this.planlist = []
      if (this.databases) {
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i]
          planidlist.push(data.plan)
        }
        planidlist = this.removeDuplicate(planidlist)
        planidlist = planidlist.filter((p: any) => planidlist2.indexOf(p) === -1)
        // remove
        planidlist.filter(k => {
          getPlan({ token: this.token, id: k }).then((rs) => {
            this.planlist.push({
              id: k,
              value: rs.data.title || k + ' (' + this.$t('c.noTitle') + ')'
            })
          })
        })
      }
    },
    calculateAgeing() {
      const today = moment();
      
      this.databases.data.map((data: any) => {
        const subscribedDate = moment(data.subscribedate);
        const durationInDays = today.diff(subscribedDate, 'days');
        
        this.ageingDataList.push({
          id: data.subscribedate,
          value: durationInDays
        })
      });
    },
    removeDuplicate (arraylist: any) {
      return arraylist = [...new Set(arraylist)]
    },
    filterFunc() {
      this.table.page = 1;
      this.table.statustxt = this.statustxt;
      this.loadDatabase();
    }
  },
  computed: {
    databases () {
      return subscriptionStore.getState().subscriptions
    },
    subscriptionCreaten () {
      return subscriptionStore.getState().subscriptionCreate
    },
    subscriptionCreateSuccess () {
      return subscriptionStore.getState().subscriptionCreateSuccess
    },
    subscriptionCreateError () {
      return subscriptionStore.getState().subscriptionCreateError
    },
    subscriptionUpdate () {
      return subscriptionStore.getState().subscriptionUpdate
    },
    subscriptionUpdateSuccess () {
      return subscriptionStore.getState().subscriptionUpdateSuccess
    },
    subscriptionUpdateError () {
      return subscriptionStore.getState().subscriptionUpdateError
    },
    subscriptionDeleteSuccess () {
      return subscriptionStore.getState().subscriptionDeleteSuccess
    },
  },
  watch: {
    subscriptionCreateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    subscriptionCreateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    subscriptionUpdateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    subscriptionUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    subscriptionDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    },
    databases (p, o) {
      if (p && p != o) {
        this.getListChecker()
      }
    }
  },
})
</script>
