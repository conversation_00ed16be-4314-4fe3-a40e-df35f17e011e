<template>
  <PopupModal defaultColor="blue" modalWidthPercent="90" :title="$t('subscriptions.formTitle')"
    v-if="item" :btnNoHide="true" :btnNoFunction="cancel">
    <form @submit.prevent="preventsubmit" autocomplete="" v-if="showForm">
      <div class="text-right mr-20">
        <div v-if="showEquipments" class="bg-gray-200 inline-block cursor-pointer p-1 px-4 rounded"
          @click="printInstallOrder">{{ $t('installationorders.print') }}</div>
      </div>
      <FormItemCompt class="w-full inline-block px-1" labelFor="plan" :labelTitle="$t('subscriptions.plan')"
        :required="true">
        <template v-if="showEquipments && planlist">
          <div class="bg-purple-700 rounded px-5 text-white text-lg inline-block">{{ planTitle }}</div>
        </template>
        <template v-else>
          <div v-if="item.plan">
            <div class="bg-purple-700 rounded px-5 text-white text-lg inline-block">{{ planTitle }}</div>
          </div>
          <ErrTextCompt :errs="errs && errs.plan"></ErrTextCompt>
        </template>
      </FormItemCompt>
      <FormItemCompt class="w-full inline-block px-1" labelFor="plan" :labelTitle="$t('subscriptions.voip')">
        <template v-if="showEquipments">
          <div class="bg-purple-700 rounded px-5 text-white text-lg inline-block">{{ voipTitle }}</div>
        </template>
      </FormItemCompt>
      <div class="">
        <div class="inline-block w-full lg:w-1/2">
          <div class="p-5" v-if="!item.customer && !selectCustomer">
            <div class="inline-block w-1/2 px-1">
              <button @click="createCustomer" :class="btnClass1">New Customer</button>
            </div>
            <div class="inline-block w-1/2 px-1">
              <button @click="() => { selectCustomer = true; }" :class="btnClass1">Existing Customer</button>
            </div>
          </div>
          <div v-if="selectCustomer && !item.customer">
            <div class="inline-block float-right px-1">
              <button @click="createCustomer"
                class="bg-gray-200 hover:bg-purple-700 hover:text-white cursor-pointer text-xs px-2 py-1 rounded">{{
                  $t('subscriptions.regasnewcust') }}</button>
            </div>
          </div>
          <!-- <FormItemCompt v-if="selectCustomer || item.customer" class="w-full inline-block px-1" labelFor="customer"
            :labelTitle="$t('subscriptions.applicant')" :required="true">
            <template v-if="showEquipments">
            </template>
            <template v-else>
              <UserAssignInput :token="token" :addUser="addCustomer" :removeUser="removeCustomer"
                :searchUser="searchCustomer" :users="customer" :readonly="false" :multiple="false"
                :additionalParamsSearch="{ customer: true, params: ['customer'] }" itemcls="w-full"
                :plcHolder="$t('c.customerKeySearch')" :noUserText="$t('c.nocustomer')"
                :addText="$t('c.selectcustomer')" defaultColor="blue"></UserAssignInput>
              <ErrTextCompt :errs="errs && errs.customer"></ErrTextCompt>
            </template>
          </FormItemCompt> -->
        </div>
        <template v-if="!showEquipments">
          <FormItemCompt v-if="!functionblock('saleschannel')" class="inline-block w-1/2 lg:w-1/4"
            labelFor="saleschannel" :labelTitle="$t('subscriptions.saleschannel')">
            <select v-model="item.saleschannel" class="p-4 w-full rounded">
              <option v-for="(p, g) in saleschannellist" :key="`saleschannel_${String(g)}`" :value="p">
                {{ $t(`saleschannel.${p}`) }}</option>
            </select>
          </FormItemCompt>
          <div class="inline-block w-1/2 lg:w-1/4">
            <FormItemCompt class="w-full inline-block px-1" labelFor="agent" :labelTitle="$t('subscriptions.agent')"
              :required="true">
              <UserAssignInput :token="token" :addUser="addAgent" :removeUser="removeAgent" :searchUser="searchAgent"
                :additionalParamsSearch="{ agent: true, params: ['agent'] }" :users="agent" itemcls="w-full"
                :readonly="false" :multiple="false" :plcHolder="$t('subscriptions.agentKeySearch')"
                :noUserText="$t('subscriptions.noagent')" :addText="$t('subscriptions.selectagent')"
                defaultColor="blue"></UserAssignInput>
              <ErrTextCompt :errs="errs && errs.agent"></ErrTextCompt>
            </FormItemCompt>
          </div>
        </template>
      </div>
      <div v-if="customer && customer[0]" class="border rounded p-5">
        <div>
          <div class="pb-1 text-xs border-b mb-3">{{ $t('subscriptions.applicantinfo') }}</div>
          <div class="text-sm" :key="`${String(i)}_cust`" v-for="(p, i) in custlist">
            <div class="inline-block w-full lg:w-1/3 pr-5" :key="`${String(i)}_${String(k)}_cust`" v-for="(q, k) in p">
              <div class="inline-block  border-b text-gray-500 w-2/5 pl-2">{{ q.toUpperCase() }} </div>
              <div class="inline-block  border-b w-3/5">{{ custvalue(q) || '&nbsp;' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="border rounded p-5 mt-5">
        <div>
          <div class="pb-1 text-xs font-bold border-b mb-3">{{ $t('subscriptions.installtioninfo') }}
            <button @click="copyabove" class="ml-5 bg-gray-300 rounded px-2 cursor-pointer">{{
              $t('subscriptions.sameasabove')
            }}</button>
          </div>
          <FormItemCompt class="w-full inline-block px-1" labelFor="title" :labelTitle="$t('users.title')">
            <span class="cursor-pointer text-xs hover:bg-red-200 bg-blue-300 rounded px-2 py-1"
              @click="switchTitleString">
              <svgcollection icon="signal" dclass="inline-block w-3 h-3" /> {{ $t('users.others') }}
            </span>
            <template v-if="titleString">
              <TextInput name="title" v-model="item.title" :placeholder="$t('c.frontPlc') + $t('users.title')"
                defaultColor="blue" :disabled="true"></TextInput>
            </template>
            <div v-else class="py-3">
              <SelectOne :list="titleList" :selectedItem="item.title" showTitle="title" itemValue="id"
                :addOrRemoveFunction="selectTitle" defaultColor="blue" readonly></SelectOne>
            </div>
            <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full inline-block px-1" labelFor="name" :labelTitle="$t('users.name')"
            :required="true">
            <TextInput name="name" v-model="item.name" :placeholder="$t('c.frontPlc') + $t('users.name')"
              defaultColor="blue" readonly>
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/3 inline-block px-1" labelFor="email" :labelTitle="$t('users.email')"
            :required="true">
            <TextInput name="email" type="email" v-model="item.email"
              :placeholder="$t('c.frontPlc') + $t('users.email')" defaultColor="blue" :required="true" readonly>
            </TextInput>
            <ErrTextCompt :errs="errs && errs.email"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/3 inline-block px-1" labelFor="contact" :labelTitle="$t('users.contact')">
            <TextInput name="contact" type="text" v-model="item.contact"
              :placeholder="$t('c.frontPlc') + $t('users.contact')" defaultColor="blue" :required="true" readonly>
            </TextInput>
            <ErrTextCompt :errs="errs && errs.contact"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/3 inline-block px-1" labelFor="contact2"
            :labelTitle="$t('users.contact2')">
            <TextInput name="contact2" type="text" v-model="item.contact2"
              :placeholder="$t('c.frontPlc') + $t('users.contact2')" defaultColor="blue" readonly></TextInput>
            <ErrTextCompt :errs="errs && errs.contact2"></ErrTextCompt>
          </FormItemCompt>
        </div>
      </div>
      <div class="border rounded p-5 mt-5">
        <div class="text-xs border-b font-bold mb-3 pb-1">
          {{ $t('subscriptions.address') }}
        </div>
        <div>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="building"
            :labelTitle="$t('subscriptions.building')">
            <div>
              <select v-model="item.address['building']" class="p-4 rounded" disabled>
                <option v-for="(p, g) in buildinglist" :key="`building_${String(g)}`" :value="p.key">{{ p.title }}</option>
              </select>
            </div>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="block"
            :labelTitle="$t('subscriptions.block')">
            <TextInput name="block" type="text" v-model="item.address['block']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.block')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="level"
            :labelTitle="$t('subscriptions.level')">
            <TextInput name="level" type="text" v-model="item.address['level']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.level')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="unit"
            :labelTitle="$t('subscriptions.unit')">
            <TextInput name="unit" type="text" v-model="item.address['unit']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.unit')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
        </div>
        <FormItemCompt class="w-full inline-block px-1" labelFor="address" :labelTitle="$t('subscriptions.address')"
          :required="true">
          <TextareaInput name="address" type="text" v-model="item.address['address']"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.address')" :rows="2" defaultColor="blue" readonly>
          </TextareaInput>
          <ErrTextCompt :errs="errs && errs.address"></ErrTextCompt>
        </FormItemCompt>
        <div>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="postcode"
            :labelTitle="$t('subscriptions.postcode')">
            <TextInput name="unit" type="text" v-model="item.address['postcode']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.postcode')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="town"
            :labelTitle="$t('subscriptions.town')">
            <TextInput name="town" type="text" v-model="item.address['city']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.town')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="state"
            :labelTitle="$t('subscriptions.state')">
            <TextInput name="state" type="text" v-model="item.address['state']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.state')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
        </div>
      </div>
      <div class="border rounded p-5 mt-5" v-if="!installerOnlyView">
        <FormItemCompt class="w-full inline-block px-1" labelFor="address" :labelTitle="$t('subscriptions.billadd')"
          :required="true">
          <TextareaInput name="address" type="text" v-model="item.billingaddress['address']"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.billadd')" :rows="2" defaultColor="blue" readonly>
          </TextareaInput>
          <ErrTextCompt :errs="errs && errs.billingaddress"></ErrTextCompt>
        </FormItemCompt>
        <div>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="postcode"
            :labelTitle="$t('subscriptions.postcode')">
            <TextInput name="unit" type="text" v-model="item.billingaddress['postcode']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.postcode')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="town"
            :labelTitle="$t('subscriptions.town')">
            <TextInput name="town" type="text" v-model="item.billingaddress['city']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.town')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/4 inline-block px-1" labelFor="state"
            :labelTitle="$t('subscriptions.state')">
            <TextInput name="state" type="text" v-model="item.billingaddress['state']"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.state')" defaultColor="blue" readonly></TextInput>
          </FormItemCompt>
        </div>
      </div>
      <div class="p-5 border rounded my-2">
        <div class="text-sm border-b font-bold mb-3">{{ $t('subscriptions.specialprice') }}</div>
        <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="price"
          :labelTitle="$t('subscriptions.price')">
          <TextInput name="price" type="number" v-model="item.price" :placeholder="$t('subscriptions.price')"
            defaultColor="blue" readonly></TextInput>
        </FormItemCompt>
      </div>
      <div class="p-5 border rounded my-2" v-if="!showEquipments">
        <div class="text-sm border-b font-bold mb-3">{{ $t('subscriptions.prepayment') }}</div>
        <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="state"
          :labelTitle="$t('subscriptions.deposit')">
          <TextInput name="deposit" type="number" v-model="item.deposit"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.deposit')" defaultColor="blue" readonly></TextInput>
        </FormItemCompt>
        <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="state"
          :labelTitle="$t('subscriptions.advancedpayment')">
          <TextInput type="number" name="advancedpayment" v-model="item.advancedpayment"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.advancedpayment')" defaultColor="blue" readonly>
          </TextInput>
        </FormItemCompt>
        <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="state"
          :labelTitle="$t('subscriptions.freeusage')">
          <TextInput type="number" name="freeusage" v-model="item.freeusage"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.freeusage')" defaultColor="blue" readonly></TextInput>
        </FormItemCompt>
        <FormItemCompt class="w-full md:w-1/5 inline-block px-1" labelFor="state"
          :labelTitle="$t('subscriptions.installationfee')">
          <TextInput type="number" name="installationfee" v-model="item.installationfee"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.installationfee')" defaultColor="blue" readonly>
          </TextInput>
        </FormItemCompt>
        <div class="">
          <div class="text-xs inline-block w-full lg:w-1/4 text-gray-500">{{ $t('subscriptions.prepaymenthints1') }}
          </div>
          <div class="text-xs inline-block w-full lg:w-1/4 text-gray-500">{{ $t('subscriptions.prepaymenthints2') }}
          </div>
        </div>
        <div class="text-right">
          <div class="w-full md:w-1/5 inline-block px-1 pl-10 text-xs text-center">
            <div>{{ $t('subscriptions.needToPay') }}</div>
            <div class="text-red-600 pt-3 text-2xl px-10">{{ formatSum([item.deposit, item.advancedpayment]) }}</div>
          </div>
          <div class="w-full md:w-1/5 inline-block px-1 pl-10 text-xs text-center">
            <div>{{ $t('subscriptions.monthlypayment') }}</div>
            <div class="text-red-600 pt-3 text-2xl px-10">{{ monthlyPaymentPrice }}</div>
          </div>
        </div>
      </div>
      <div class="p-8 mt-2 rounded border">
        <div>
          <div class="mb-2 border-b pb-1">{{ $t('subscriptions.internet') }}</div>
          <FormItemCompt class="w-full md:w-1/2 inline-block px-1" labelFor="username"
            :labelTitle="$t('subscriptions.username')">
            <TextInput readonly name="username" type="text" v-model="item.username"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.username')" defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.username"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full md:w-1/2 inline-block px-1" labelFor="userpassword"
            :labelTitle="$t('subscriptions.userpassword')" :required="true">
            <TextInput name="userpassword" readonly type="text" v-model="item.userpassword"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.userpassword')" defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.userpassword"></ErrTextCompt>
          </FormItemCompt>
        </div>
        <div>
          <div class="mb-2 border-b pb-1">{{ $t('subscriptions.voip') }}</div>
          <FormItemCompt class="w-full md:w-1/3 inline-block px-1" labelFor="voipno" :labelTitle="$t('users.voipno')">
            <TextInput name="voipno" type="text" v-model="item.voipno"
              :placeholder="$t('c.frontPlc') + $t('users.voipno')" defaultColor="blue" readonly></TextInput>
            <ErrTextCompt :errs="errs && errs.voipno"></ErrTextCompt>
          </FormItemCompt>
        </div>
        <FormItemCompt class="w-1/2 lg:w-1/5 inline-block px-1" labelFor="subscribeDate"
          :labelTitle="$t('subscriptions.subscribeDate')">
          <DatePicker disabled v-model="item.subscribedate" :clearable="clearabledate" defaultColor="blue"></DatePicker>
        </FormItemCompt>
        <FormItemCompt class="w-1/2 lg:w-1/5 inline-block px-1" labelFor="activationDate"
          :labelTitle="$t('subscriptions.activationDate')" required>
          <DatePicker v-model="item.activationdate" :clearable="clearabledate" defaultColor="blue">
          </DatePicker>
        </FormItemCompt>
        <FormItemCompt class="w-1/2 lg:w-1/5 inline-block px-1" labelFor="contractMonths"
          :labelTitle="$t('subscriptions.contractMonths')">
          <TextInput name="contractMonths" type="number" v-model="item.contractmonths"
            :placeholder="$t('c.frontPlc') + $t('subscriptions.contractMonths')" defaultColor="blue" readonly>
          </TextInput>
        </FormItemCompt>
        <FormItemCompt class="w-1/2 lg:w-1/5 inline-block px-1" labelFor="contractEndDate"
          :labelTitle="$t('subscriptions.contractEndDate')">
          <DatePicker v-model="item.contractenddate" :clearable="clearabledate" defaultColor="blue" disabled>
          </DatePicker>
        </FormItemCompt>
        <FormItemCompt class="w-1/2 lg:w-1/5 inline-block px-1" labelFor="terminationDate"
          :labelTitle="$t('subscriptions.terminationDate')">
          <DatePicker v-model="item.terminationdate" :clearable="clearabledate" defaultColor="blue" disabled>
          </DatePicker>
        </FormItemCompt>
      </div>
      <div class="mt-3 p-4 border rounded inline-block w-full">
        <div class="p-4">
          <p class="border-b font-bold">Installer Details</p>
        </div>
        <FormItemCompt class="w-full inline-block px-1" labelFor="installer" :labelTitle="$t('subscriptions.installer')"
          :required="true">
          <UserAssignInput :token="token" :addUser="addInstaller" :removeUser="removeInstaller" :searchUser="searchInstaller"
            :additionalParamsSearch="{ installer: true, params: ['installer'] }" :users="installer" itemcls="w-full" :readonly="false"
            :multiple="false" :plcHolder="$t('subscriptions.installerKeySearch')" :noUserText="$t('subscriptions.noinstaller')"
            :addText="$t('subscriptions.selectinstaller')" defaultColor="blue"></UserAssignInput>
          <ErrTextCompt :errs="errs && errs.agent"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="name" labelTitle="Contact No"
            :required="true">
            <TextInput v-model="item.installerdetails.contact" :placeholder="$t('c.frontPlc') + $t('users.contact')"
              defaultColor="blue">
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="name" labelTitle="GPON S/N"
            :required="true">
            <TextInput v-model="item.gponsn" :placeholder="$t('c.frontPlc') + $t('subscriptions.gponsn')"
              defaultColor="blue">
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="port"
            :labelTitle="$t('subscriptions.port')" :required="true">
            <TextInput name="port" v-model="item.port" :placeholder="$t('c.frontPlc') + $t('subscriptions.port')"
              defaultColor="blue"></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="circuit"
            :labelTitle="$t('subscriptions.splitter')" :required="true">
            <TextInput name="splitter" v-model="item.splitter"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.splitter')" defaultColor="blue"></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="serial"
            :labelTitle="$t('subscriptions.serial')" :required="true">
            <TextInput name="serial" v-model="item.serial"
              :placeholder="$t('c.frontPlc') + $t('subscriptions.serial')" defaultColor="blue"></TextInput>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="name" labelTitle="Router Mac"
            :required="true">
            <TextInput v-model="item.installerdetails.routermac" :placeholder="$t('c.frontPlc') + $t('subscriptions.macrouter')"
              defaultColor="blue">
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="name" labelTitle="Reading FWS"
            :required="true">
            <TextInput v-model="item.installerdetails.readingfws" :placeholder="$t('c.frontPlc') + $t('subscriptions.readingfws')"
              defaultColor="blue">
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-1/2 lg:w-1/4 inline-block px-1" labelFor="name" labelTitle="Reading MDF"
            :required="true">
            <TextInput v-model="item.installerdetails.readingmdf" :placeholder="$t('c.frontPlc') + $t('subscriptions.readingmdf')"
              defaultColor="blue">
            </TextInput>
            <ErrTextCompt :errs="errs && errs.name"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt class="w-full lg:w-1/2 inline-block px-1" labelFor="date"
            labelTitle="Installation Date" :required="true">
            <DateTimePicker v-model="item.installerdetails.date" :clearable="clearabledate" defaultColor="blue"></DateTimePicker>
          </FormItemCompt>
      </div>
      <FormItemCompt class="w-full inline-block px-1" labelFor="installationremark"
        :labelTitle="$t('subscriptions.installationremark')" required>
        <TextareaInput name="installationremark" v-model="item.installationremark"
          :placeholder="$t('c.frontPlc') + $t('subscriptions.installationremark')" :rows="6" defaultColor="blue">
        </TextareaInput>
      </FormItemCompt>
      <div class="grid grid-cols-3 gap-2">
        <FormItemCompt v-for="(item, index) in formItems" :key="item.key" class="my-5 border rounded p-2" :labelFor="item.labelFor"
        :labelTitle="item.labelTitle" :required="item.required">
          <div>
            <ShowFiles :files="installerattachments[item.key]" :removeFile="(p) => removeFileInstaller(p, item.key)" />
          </div>
          <UploadInput :type="item.key" :addFile="(p) => addFileInstaller(p, item.key)" :basePath="basePath" :token="token" />
        </FormItemCompt>
      </div>
      <FormItemCompt class="w-full inline-block px-1" labelFor="statustxt" :labelTitle="$t('subscriptions.statusTxt')">
        <select v-model="item.statustxt" class="form-select p-2 rounded text-lg block w-full shadow" disabled>
          <option v-for="(status, index) in statuses" :key="`${String(index)}_status`" :value="String(status)">
            {{ $t(`subscriptions.${status}`) }}</option>
        </select>
      </FormItemCompt>
    </form>
    <p class="text-sm font-bold">Please fill in all required fields marked with <span class="text-red-500">*</span> to enable the submit button.</p>
    <div class="flex justify-end">
      <button v-if="isAdmin" @click="changeStatus" class="text-white font-bold py-2 px-4 rounded mr-3 bg-green-500 hover:bg-green-700">
        Change status to active
      </button>
      <button v-if="item.installationremark && item.installer && item.installerdetails.contact && item.gponsn && item.installerdetails.routermac && 
        item.installerdetails.readingfws && item.installerdetails.readingmdf && item.port && item.splitter && item.serial && item.installerdetails.date && item.installerdetails.inswo
        && item.installerdetails.insst && item.installerdetails.insfws && item.installerdetails.insmdf && item.installerdetails.inscbl" 
        @click="submitNow" class="text-white font-bold py-2 px-4 rounded mr-3 bg-blue-500 hover:bg-blue-700">
        {{ $t('c.submitforverification') }}
      </button>
      <button @click="cancel()" class="bg-transparent hover:bg-gray-500 font-semibold hover:text-white py-2 px-4 border hover:border-transparent rounded text-gray-700 border-gray-500">
        {{ $t('c.cancel') }}
      </button>
    </div>
  </PopupModal>
  <div id="divToPrint"></div>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import FileUpload from 'vue-upload-component'
import axios from 'axios'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import UserAssignInput from '@/components/cvui/form/UserAssignInput.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import DateTimePicker from '@/components/DateTimePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import UploadInput from '@/components/Upload3.vue'
import ShowFiles from '@/components/cvui/form/ShowFiles.vue'
import dtable from '@/components/cvui/table/index.vue'
import { updateUserDMAPlan, getNextSubscriptionID, pushSubscriptionID, getDMAUserCheck, generatebillspecific, createDMAUser, getPlans, getPlan, getFile, deleteFile, basePath, searchUser, createUser, getUserName, disableUser, disconnectUser, enableUser, getUsers, getUser, getUserById, uploadFiles, getBuildings } from '../../../api'
import svgcollection from '@/components/cvui/svgcollection.vue'
import userform from '../users/userform.vue'
import moment from 'moment'
import config from '../../../config'
import { auth2Store } from '../../../store/auth2-store'
export default defineComponent({
  props: {
    profile: {
      type: Object,
      required: true,
    },
    item: {
      type: Object,
      required: true
    },
    cancel: {
      type: Function,
      required: true
    },
    save: {
      type: Function,
      required: true
    },
    saveSilent: {
      type: Function,
      required: true
    },
    token: {
      type: String,
      required: true
    },
    showEquipments: {
      type: Boolean,
      default: false
    },
    installerOnlyView: {
      type: Boolean,
      default: false
    }
  },
  components: {
    FileUpload,
    PopupModal,
    FormItemCompt,
    TextInput,
    TextareaInput,
    UserAssignInput,
    SelectOne,
    DatePicker,
    DateTimePicker,
    ErrTextCompt,
    UploadInput,
    dtable,
    svgcollection,
    userform,
    ShowFiles,
  },
  methods: {
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        if (this.cancel) {
          this.cancel()
        }
      }
    },
    changeStatus () {
      this.item.statustxt = 'active'
      this.submitNow
    },
    generateSID() {
      if (!this.item.sid) {
        getNextSubscriptionID({ token: this.token }).then((res: any) => {
          this.item.sid = res.data
          pushSubscriptionID({ token: this.token })
        })
      }
    },
    functionblock(p: any) {
      if (config && config.functionsblock && config.functionsblock.indexOf(p) > -1) {
        return true
      } else {
        return false
      }
    },
    copyaboveToBill() {
      this.item.billingaddress = JSON.parse(JSON.stringify(this.item.address))
      if (!this.item.billingaddress) {
        this.item.billingaddress = {
          address: '',
          city: '',
          state: '',
          postcode: ''
        }
      }
      if (this.item.billingaddress["unit"]) {
        delete this.item.billingaddress["unit"]
      }
      if (this.item.billingaddress["level"]) {
        delete this.item.billingaddress["level"]
      }
      if (this.item.billingaddress["block"]) {
        delete this.item.billingaddress["block"]
      }
      if (this.item.billingaddress["building"]) {
        delete this.item.billingaddress["building"]
      }
    },
    autoUsername() {
      if (!this.item.username || this.item.username.length == 0) {
        let us = this.item.name.replace(/\s/g, '').toLowerCase() + '_' + this.randomNum(2)
        let ps = this.randomPass(8)
        this.item.username = us + '@highfi'
        this.item.userpassword = ps

        if (this.item.voip) {
          this.item.voipusername = us + '_voip' + '@highfi'
          this.item.voipuserpassword = ps
        }
      }
    },
    generatebill() {
      if (confirm('Are you sure to generate bill?')) {
        generatebillspecific({ token: this.token, id: this.item.id, date: moment(this.genbilldate).format('DD-MM-YYYY') }).then((res: any) => {
          alert('bill generated')
        })
      }
    },
    autoUsername2() {
      if (!this.item.username || this.item.username.length == 0) {
        this.item.username = ''
        this.item.userpassword = ''
        this.item.voipusername = ''
        this.item.voipuserpassword = ''
        this.regenerateUsername = false

        let p1 = 'b' + (this.item.address.block || '0') + '_l' + (this.item.address.level || '0') + '_u' + (this.item.address.unit || '0')
        let us = p1.toLowerCase() + '_' + this.randomNum(3)
        let ps = '1' + p1.toLowerCase().replace(/_/g, '')
        this.$nextTick(() => {
          this.item.username = us + '@highfi'
          this.item.userpassword = ps

          if (this.item.voip != null) {
            this.item.voipusername = us + '_voip' + '@highfi'
            this.item.voipuserpassword = ps
          }
          this.regenerateUsername = true
        })
      }
    },
    clearUsernamePassword() {
      if (confirm(this.$t('subscriptions.confirmClear'))) {
        this.$nextTick(() => {
          this.item.username = ''
          this.item.userpassword = ''
        })
      }
    },
    closeCreateCustomer() {
      this.customerItem = undefined
    },
    autoEndDate() {
      if (this.item.activationdate) {
        this.item.contractenddate = moment(this.item.activationdate).add(this.item.contractmonths, 'months').toDate()
      }
    },
    validateUser() {
      let p = true
      if (!this.customerItem.email || this.customerItem.email.trim().length == 0) {
        this.usererrs['email'] = 'c.fieldRequired'
        p = false
      }
      if (!this.customerItem.name || this.customerItem.name.trim().length == 0) {
        this.usererrs['name'] = 'c.fieldRequired'
        p = false
      }
      if (!this.customerItem.identityno || this.customerItem.identityno.trim().length == 0) {
        this.usererrs['identityno'] = 'c.fieldRequired'
        p = false
      }
      return p
    },
    formatSum(p: any) {
      let r = 0
      if (p) {
        p.forEach((g: any) => {
          if (!isNaN(g)) {
            r += parseFloat(g)
          }
        })
      }
      return r.toFixed(2)
    },

    randomPass(n: number): string {
      let chars = 'abcdefghjkmnpqrstuvwxyz0123456789'
      let pass = ''
      for (let x = 0; x < n; x++) {
        let i = Math.floor(Math.random() * chars.length)
        pass += chars.charAt(i)
      }
      return pass
    },
    randomNum(n: number): string {
      let chars = '0123456789'
      let pass = ''
      for (let x = 0; x < n; x++) {
        let i = Math.floor(Math.random() * chars.length)
        pass += chars.charAt(i)
      }
      return pass
    },
    createCustomerNow() {
      if (this.validateUser()) {
        createUser(Object.assign({ token: this.token }, { form: Object.assign({ password: this.randomPass(8) }, this.customerItem) })).then((res: any) => {
          if (res && res.data && res.data.id) {
            axios.get("https://ktic.com.my/api/hi5_status_update.php", {
              params: {
                security_token: "KTIC20240625-17678-897KBVHJV",
                order_no: res.data.sid,
                status: res.data.statustxt
              }
            })
            this.customerItem = undefined
            this.item.customer = res.data.id
            this.customer.push(res.data)
          }
        }).catch((err: any) => {
          alert('User Email Existed. Please try another.')
        })
      }
    },
    createCustomer() {
      this.selectCustomer = false
      this.customerItem = {
        email: '',
        identityNo: '',
        mobile: '',
        name: '',
        nationality: 'MY',
        gender: true,
        customer: true,
        mothersName: '',
        fathersName: '',
        scopes: [],
        status: true
      }
    },
    saveUser(item: any) {
      this.customerItem = undefined
      this.loadCustAgent()
    },
    validateForm() {
      let p: any = true
      if (!this.item.customer || this.item.customer.trim().length == 0) {
        this.errs['customer'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.plan || this.item.plan.trim().length == 0) {
        this.errs['plan'] = 'c.fieldRequired'
        p = false
      }
      if (this.item.price) {
        this.item.price = parseFloat(this.item.price)
      }

      return p
    },
    preventsubmit(e: any) {
      e.preventDefault()
    },
    submitNow(e: any) {

      if (this.validateForm()) {
        this.save(this.item)
      }
      return false
    },

    addCustomer(p: any) {
      if (p) {
        if (!this.item.customer) {
          this.item.customer = ''
        }
        if (this.item.customer !== p.ID) {
          this.item.customer = p.ID
          this.customer.push(p)
        }
        this.showCustomer = true
      }
    },
    removeCustomer(p: any) {
      if (p) {
        this.customer = []
        this.item.customer = ''
      }
    },
    addInstaller(p: any) {
      if (!this.item.installer) {
        this.item.installer = ''
      }
      if (this.item.installer !== p.ID) {
        this.item.installer = p.ID
        this.installer.push(p)
      }
      this.showInstaller = true
    },
    removeInstaller(p: any) {
      if (this.item.installer === p.ID || this.item.installer === p.id) {
        this.item.installer = ''
        this.installer = []
      }
    },
    loadInstaller(pg: any, pageSize: any, keywords: any) {
      getUserById({ token: this.token, id: keywords }).then(res => {
        var d: any = res        
        if (d) {
          //
        }
      })
    },
    addAgent(p: any) {
      if (!this.item.agent) {
        this.item.agent = ''
      }
      if (this.item.agent !== p.ID) {
        this.item.agent = p.ID
        this.agent.push(p)
      }
      this.showAgent = true
    },
    removeAgent(p: any) {
      if (this.item.agent === p.ID || this.item.agent === p.id) {
        this.item.agent = ''
        this.agent = []
      }
    },
    removeDuplicates(array: any) {
      let uniqueObjects: any = [];
      let ids = new Set();
      array.forEach((obj: any) => {
        if (!ids.has(obj.id)) {
          ids.add(obj.id);
          uniqueObjects.push(obj);
        }
      });
      return uniqueObjects;
    },

    loadplan(pg: any, pageSize: any, keywords: any) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      getPlans({ token: this.token, voip: false, skip: skip, limit: pageSize, keywords: keywords }).then(res => {
        var d: any = res
        if (d.data) {
          this.planlist = this.planlist.concat(d.data)
        }
      })
    },
    loadvoip(pg: any, pageSize: any, keywords: any) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      getPlans({ token: this.token, voip: true, skip: skip, limit: pageSize, keywords: keywords }).then(res => {
        var d: any = res
        if (d.data) {
          this.voiplist = this.voiplist.concat(d.data)
          if ((d.total / pageSize) >= pg) {

          }
        }
      })
    },
    showSelectPlan() {
      this.selectPlanShow = true
    },
    searchPlan() {
      this.loadplan(1, 10, this.planKeywrd)
    },
    addRemovePlan(item: any, index: any) {
      if (this.item.plan === item.id) {
        this.item.plan = ''
      } else {
        this.item.plan = item.id
      }
    },
    removePlan() {
      this.item.plan = ''
    },
    addRemoveVoip(item: any, index: any) {
      if (this.item.voip === item.id) {
        this.item.voip = ''
      } else {
        this.item.voip = item.id
      }
    },
    usernameAvailabilityFunc() {
      //
    },
    silentSaveFix() {
      this.saveSilent()
    },
    addFileInstaller(p: any, key: any) {      
      
      if (this.installerattachments[key].length > 0) {
        this.installerattachments[key] = []
      }
      this.installerattachments[key].push(p);
      this.item.installerdetails[key] = p.file.id;
      this.silentSaveFix();
    },
    removeFileInstaller(p: any, key: any) {
      this.item.installerdetails[key] = ""
      this.installerattachments[key] = []
      deleteFile({ id: p, token: this.token });
    },
    loadAttachmentsInstaller() {
      if (this.item.installerdetails) {
        const keys = ['inswo', 'insst', 'insmdf', 'insfws', 'inscbl'];

        keys.forEach((key) => {
          const fileId = this.item.installerdetails[key];
          if (fileId) {
            getFile({ token: this.token, id: fileId }).then((res: any) => {              
              if (res && res.data) {
                this.installerattachments[key].push(res['data'])
              }
            }).catch((err) => {
              console.error(`Error loading file for ${key}:`, err);
            });
          }
        });
      }
    },
    dcUser() {
      if (this.item.username) {
        disconnectUser({ token: this.token, data: { id: this.item.username } })
        alert('user disconnected.');
      }
    },
    dsUser() {
      if (this.item.username) {
        disableUser({ token: this.token, data: { id: this.item.username } })
        alert('user disabled');
      }
    },
    enUser() {
      if (this.item.username) {
        enableUser({ token: this.token, data: { id: this.item.username } })
        alert('user enabled');
      }
    },

    loadCustAgent() {
      this.customer = []
      this.agent = []
      if (this.item.customer && this.item.customer.trim().length > 0) {
        getUserName({ token: this.token, id: this.item.customer }).then(res => {
          this.customer.push(res)
        })
      }
      if (this.item.agent && this.item.agent.trim().length > 0) {
        getUserName({ token: this.token, id: this.item.agent }).then(res => {
          this.agent.push(res)
        })
      }
    },
    valuec(c: any, q: string) {
      const cc = config.countrieswithcode
      if (q == 'dob') {
        return moment(c).format('DD/MM/YYYY')
      } else if (q == 'title') {
        return (c && c.toUpperCase()) || ''
      } else if (q == 'nationality') {
        return cc.find((p: any) => c == p.code)?.name
      } else if (q == 'gender') {
        return c == false ? this.$t('c.female') : this.$t('c.male')
      } else {
        return c
      }
    },
    custvalue(q: any) {
      if (this.customer && this.customer.length > 0) {
        const custid = this.item.customer
        let c = this.customer.find((p: any) => custid == (p.id || p.ID))
        if (c) {
          return this.valuec(c[q], q)
        }
      } else {
        return false
      }
    },
    switchTitleString() {
      this.titleString = !this.titleString
    },
    selectTitle(item: any, index: any) {
      if (this.item.title === item.id) {
        this.item.title = ''
      } else {
        this.item.title = item.id
      }
    },
    copyabove(e: any) {
      e.preventDefault()
      const p = this.customer.find((g: any) => (g.id || g.ID) == this.item.customer)
      if (p) {
        this.item.title = p.title || ''
        this.item.email = p.email || ''
        this.item.name = p.name || ''
        this.item.contact = p.contact || ''
        this.item.contact2 = p.contact2 || ''
        this.refreshForm()
      }
    },
    refreshForm() {
      this.showForm = false
      this.$nextTick(() => {
        this.showForm = true
      })
    },
    syncUser() {

      getDMAUserCheck({ token: this.token, username: this.item.username }).then((res: any) => {
        if (res) {
          if (res.data && res.data == "username available") {
            if (confirm('Are you sure to sync & create user?')) {
              createDMAUser({ token: this.token, id: this.item.id }).then((res: any) => {
                if (res.data.affectedRows == 1) {
                  alert('user created')
                }
              })
            }
          }
        }
      }).catch((err: any) => {

        if (err.response.data && err.response.data.data && err.response.data.data == "username already occupied") {
          if (confirm('Username already existed in DMA\nDo you wish to sync the plan as well?')) {
            updateUserDMAPlan({ token: this.token, id: this.item.id }).then((res: any) => {
              if (res.data.affectedRows == 1) {
                alert('user plan updated')
              }
            })
          }
        }
      })
    },
    printInstallOrder() {

      window.open(this.$router.resolve({ name: 'printInstallOrder', params: { id: this.item.id } }).href, '_blank')
    },
    updateUserPlan() {
      updateUserDMAPlan({ token: this.token, id: this.item.id }).then((res: any) => {
        if (res.data.affectedRows == 1) {
          alert('user plan updated')
        }
      })
    },
    // upload files
    setParams (p: any, i: any) {
      p = i
    },
    setParams2 (p: any, q: any, i: any) {
      p[q] = i
    },
    params (p: any, i: any) {
      return p[i]
    },
    removeFile(f: any, file: any) {
      f.remove(file)
    },
    fetchPlanTitle () {
      getPlan({id: this.item.plan, token: this.token}).then(res => {        
        this.planTitle = res.data.title
      })
    },
    inputFilter(newFile: any, oldFile: any, prevent: any) {
      if (newFile && !oldFile) {
        if (!(/\.(gif?|jpg?|jpeg?|svg?|xlsx?|xls?|csv?|png?|webp?|pdf?|doc?|docx?|ppt?|pptx?)$/i.test(newFile.name))) {
          return prevent()
        }

        if (/\.(gif?|jpg?|jpeg?|png?|webp?)$/i.test(newFile.name)) {
          if (newFile && newFile.error === "" && newFile.file && (!oldFile || newFile.file !== oldFile.file)) {
            newFile.blob = ''
            let URL = (window.URL || window.webkitURL)
            if (URL) {
              newFile.blob = URL.createObjectURL(newFile.file)
            }
            newFile.thumb = ''
            if (newFile.blob && newFile.type.substr(0, 6) === 'image/') {
              newFile.thumb = newFile.blob
            }
          }
        }
      }
    },
    inputFile(newFile: any, oldFile: any, key: any) {
      console.log('handling:' + key);
      
      if (newFile && oldFile) {
        if (newFile.success) {
        this.addFileInstaller(newFile.response, key);
        }
      }
      if (!newFile && oldFile) {
        // remove
      }
    },
    loadBuilding(pg: number, pageSize: number, keywords: string) {
      const fetchData = (skip: number, limit: number) => {
        getBuildings({ token: this.token, skip, limit, keywords }).then((res: any) => {
          if (res.data) {            
            this.buildinglist = this.buildinglist.concat(res.data);
            this.buildinglist = this.removeDuplicates(this.buildinglist);

            const totalRetrieved = this.buildinglist.length;
            const totalAvailable = res.total;

            if (totalRetrieved < totalAvailable) {
              fetchData(totalRetrieved, limit);
            }
          }
        });
      };

      const initialSkip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0;
      fetchData(initialSkip, pageSize);
    },
  },
  setup(props: any, context: any) {
    const upload = ref(null)
    return {
      upload,
    }
  },
  mounted() {
    document.addEventListener('keydown', this.handleEscKey);
    this.loadplan(1, 10, '')
    this.loadvoip(1, 10, '')
    this.loadCustAgent()
    if (this.item.installer && this.item.installer.trim().length > 0) {
      this.loadInstaller(1, 10, this.item.installer)
    }
    this.loadAttachmentsInstaller()
    this.loadBuilding(1, 10, '')
    this.fetchPlanTitle()
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  data() {
    let showForm: Boolean = true
    let errs: any = {}
    let usererrs: any = {}
    let customer: any = []
    let agent: any = []
    let installer: any = []
    let planlist: any = []
    let voiplist: any = []
    let attachments: any = []
    let formItems: any = [
      { labelFor: 'inswo', labelTitle: 'WO', key: 'inswo', instanceId: 'wo-upload', required: true },
      { labelFor: 'insst', labelTitle: 'Speedtest Result', key: 'insst', instanceId: 'st-upload', required: true },
      { labelFor: 'insfws', labelTitle: 'FWS Reading', key: 'insfws', instanceId: 'fws-upload', required: true },
      { labelFor: 'insmdf', labelTitle: 'MDF Reading', key: 'insmdf', instanceId: 'mdf-upload', required: true },
      { labelFor: 'inscbl', labelTitle: 'Cable Labelling', key: 'inscbl', instanceId: 'cbl-upload', required: true },
    ]
    let installerattachments: any = {
      inswo: [],
      insst: [],
      insfws: [],
      insmdf: [],
      inscbl: []
    }
    let genbilldate: any = moment()
    let regenerateUsername: Boolean = true
    let equipmentItemStyle: any = {
      name: '',
      serial: '',
      brand: '',
      model: '',
      type: '',
      status: ''
    }
    let equipmentItem: any = undefined
    let errsEquipments: any = {}
    let showCustomer: Boolean = false
    let showAgent: Boolean = false
    let showInstaller: Boolean = false
    let customerItem: any = undefined
    let selectCustomer: Boolean = false
    let customerdb: any = undefined
    let titleString = false
    let titleList: any = config.titles

    let btnClass1: string = 'cursor-pointer bg-gray-300 py-2 hover:bg-purple-600 hover:text-white rounded w-full'
    const custlist: any = [
      ["title", "name", "identityno"],
      ["email", "dob", "nationality"],
      ["gender", "contact", "contact2"]
    ]
    const statuses: any = config.subscriptionStatus
    let clearabledate: Boolean = true
    let showSuggestUsername: Boolean = false
    let showbilldate: Boolean = true
    let buildinglist: any = []
    let saleschannellist: any = ['direct', 'agent', 'dealer']
    let selectPlanShow: Boolean = false
    let planKeywrd: string = ''
    let planTitle: string = ''
    return {
      showForm,
      planKeywrd,
      selectPlanShow,
      showSuggestUsername,
      regenerateUsername,
      errs,
      genbilldate,
      clearabledate,
      usererrs,
      customer,
      agent,
      installer,
      planlist,
      voiplist,
      attachments,
      formItems,
      installerattachments,
      basePath: `${basePath}api/upload`,
      equipmentItemStyle,
      equipmentItem,
      errsEquipments,
      showCustomer,
      showAgent,
      showInstaller,
      btnClass1,
      customerdb,
      searchCustomer: searchUser,
      searchAgent: searchUser,
      searchInstaller: searchUser,
      customerItem,
      selectCustomer,
      titleString,
      titleList,
      custlist,
      statuses,
      showbilldate,
      buildinglist,
      saleschannellist,
      planTitle
    }
  },
  computed: {
    scopes () {
      let s: any = auth2Store.getState().profile
      
      return  s && s.scopes 
    },
    isAdmin () {
      let p: any = this.scopes
      if (p.includes('admin')) {
        return true;
      }
      return false;
    },
    filteredPlans() {
      return this.planlist.filter((p: any) => p.title.indexOf(this.planKeywrd) > -1)
    },
    monthlyPaymentPrice() {
      var r = 0
      if (this.item.plan) {
        let g = this.planlist.find((p: any) => p.id == this.item.plan)
        if (g) {
          r = g.price
        }
      }
      if (this.item.voip) {
        let g = this.voiplist.find((p: any) => p.id == this.item.voip)
        if (g) {
          r += g.price
        }
      }
      if (this.item.price) {
        r = this.item.price
        if (typeof r == 'string') {
          r = parseFloat(r)
        }
      }
      return r.toFixed(2)
    },
    findPlan(): any {
      let x = this.planlist && this.planlist.find((p: any) => p.id == this.item.plan)
      return (x && x.title) || ''
    },
    voipTitle() {

      let t: String = ''
      if (this.item.voip) {
        t = this.voiplist.find((p: any) => p.id == this.item.voip)?.title
      }
      return t
    },
    equipmentColumns() {
      return [
        { title: 'subscriptions.name', key: 'name', type: 'string', class: 'text-center' },
        { title: 'subscriptions.serial', key: 'serial', type: 'string', class: 'text-center' },
        { title: 'subscriptions.brand', key: 'brand', type: 'string', class: 'text-center' },
        { title: 'subscriptions.model', key: 'model', type: 'string', class: 'text-center' },
        { title: 'subscriptions.type', key: 'type', type: 'string', class: 'text-center' },
        { title: 'subscriptions.status', key: 'status', type: 'string', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    }
  }
})
</script>