<template>
  <div>
    <adminheader :title="$t('navigations.subscriptions')" :reloadFunction="reload"></adminheader>
    <div class="text-right mx-10 my-5">
      <!-- <button class="bg-gray-200 px-5 py-1 hover:bg-gray-600 hover:text-white text-xs rounded" @click="getListChecker">Get Details</button> -->
    </div>
    <div>
      <div>
        <dtablesearch :searchFunc="searchFunc"></dtablesearch>
      </div>
    </div>
    <div class="mx-6 flex justify-between">
      <div class="flex">
        <div class="block relative bg-white shadow rounded-lg">
          <span @click="searchCustomer" title="Search"
            class="h-full cursor-pointer absolute inset-y-0 left-0 flex items-center pl-2">
            <svg viewBox="0 0 24 24" class="h-4 w-4 fill-current text-gray-500">
              <path
                d="M10 4a6 6 0 100 12 6 6 0 000-12zm-8 6a8 8 0 1114.32 4.906l5.387 5.387a1 1 0 01-1.414 1.414l-5.387-5.387A8 8 0 012 10z">
              </path>
            </svg>
          </span>
          <input v-on:keyup.enter="searchCustomer" v-model="customer" placeholder="Search customer"
            class="appearance-none rounded-l border border-gray-200 border-b block pl-8 pr-6 py-2 w-full bg-white text-sm placeholder-gray-400 text-gray-700 focus:bg-white focus:placeholder-gray-600 focus:text-gray-700 focus:outline-none" />
        </div>
        <div>
          <span v-if="statustxt !== '' || customer !== ''" @click="reload"
          class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 cursor-pointer"
          style="margin-left: 12px;">Remove filter</span>
        </div>
      </div>
      <select v-model="statustxt" @change="filterFunc"
        class="text-sm placeholder-gray-500 border rounded bg-white p-2 focus:ring-blue-500">
        <option value="" selected disabled>Filter by status</option>
        <option value="new">New</option>
        <option value="pendingdeposit">Pending Deposit</option>
        <option value="pendinginstall">Pending Installation</option>
        <option value="pendingactivation">Pending Activation</option>
        <option value="active">Active</option>
        <option value="suspended">Suspended</option>
        <option value="cancelled">Cancelled</option>
        <option value="terminated">Terminated</option>
        <option value="migration">Migration</option>
      </select>
    </div>
    <div v-if="databases == null">
      <dloading />
    </div>
    <template v-else>
      <dtable :columns="columns" :data="databases" columnColor="white">
        <template v-slot:action="slotProps">
          <button :title="$t('c.edit')" @click="editRow(slotProps.item, slotProps.index)"
            class="inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full">
            <svgicon icon="edit" dclass="w-4 h-4 m-1 inline-block" />
          </button>
          <button v-if="showSIDBtn && slotProps.item.statustxt == 'active' && !slotProps.item.sid"
            class="m-2 inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full"
            @click="assignSID(slotProps.item)">SID</button>
          <!-- <button :title="$t('c.billing')" @click="bitemRow(slotProps.item)" class="inline-block bg-green-500 hover:bg-green-700 text-white w-7 h-7 mr-2 rounded-full">
                <svgicon icon="cash" dclass="w-4 h-4 m-1 inline-block"/>
            </button> -->
          <!-- <button :title="$t('c.delete')" @click="deleteRow(slotProps.item, slotProps.index)" class="inline-block bg-red-500 hover:bg-red-700 text-white w-7 h-7 mr-2 rounded-full">
              <svgicon icon="trash" dclass="w-4 h-4 m-1 inline-block"/>
            </button>   -->
        </template>
      </dtable>
      <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
        :pageChange="pageChange" defaultColor="blue" />
    </template>
    <template v-if="item">
      <dform :profile="profile" :item="item" :cancel="cancelNow" :token="token" :save="saveFunc"
        :saveSilent="saveSilent"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import axios from 'axios'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { subscriptionStore } from '../../../store/subscription-store'
import { getUserName, getPlan, getNextSubscriptionID, pushSubscriptionID, getUsers, getSubscription } from '../../../api'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import { createSubscription, updateSubscription } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()

    return {
      token: computed(() => authState.token),
      authStore: authStore,
      authState: authState,
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    svgicon
  },
  mounted() {
    if (this.$route.params.id) {
      let subId = this.$route.params.id
      getSubscription({token: this.token, id: subId}).then((res: any) => {                        
            this.editRow(res.data, 0)
        }).catch((err: any) => {
            console.error(err);   
        })
      // this.$router.replace({ name: 'subscriptionsl1' });
    }
    this.reload()
    // setTimeout(() => {
    //   this.getPlanList()
    //   this.getUserList()
    // }, 500)
  },
  data() {
    let item: any = undefined
    let deleteItem: any = undefined
    let auth2State = auth2Store.getState()
    let profile: any = computed(() => auth2State.profile)
    let bitem: any = undefined
    let itemStyle = {
      user: profile.id,
      customer: '',
      agent: '',
      plan: '',
      voip: '',
      suggestusername: [],
      title: '',
      username: '',
      userpassword: '',
      deposit: 0,
      advancedpayment: 0,
      installationfee: 0,
      subscribedate: null,
      activationdate: null,
      contractenddate: null,
      terminationdate: null,
      contractmonths: 1,
      attachments: [],
      ebilling: true,
      remark: '',
      equipments: [],
      freeusage: 0,
      saleschannel: '',
      billingaddress: {
        address: '',
        city: '',
        state: '',
        postcode: '',
      },
      address: {
        address: '',
        unit: '',
        city: '',
        state: '',
        postcode: '',
        level: '',
        block: '',
        building: ''
      },
      phone: '',
      status: true,
      statustxt: 'new',
    }
    let table: any = {
      limit: 10,
      page: 1,
      keywords: '',
      statustxt: '',
      customer: '',
    }
    let keywords: string = ''
    let customer: string = ''
    let customerTable: any = []
    let searchingCustomer = false
    let userlist: any = []
    let planlist: any = []
    let customerProfile: any = undefined
    let statuslist: any = [
      {
        id: 'pendinginstall',
        value: this.$t('subscriptions.pendinginstall')
      },
      {
        id: 'new',
        value: this.$t('subscriptions.new')
      },
      {
        id: 'terminated',
        value: this.$t('subscriptions.terminated')
      },
      {
        id: 'active',
        value: this.$t('subscriptions.active')
      }
    ]
    let lastgetPlan: number = Date.now() - 1000
    let lastgetUser: number = Date.now() - 1000
    let showSIDBtn: boolean = false
    let statustxt: string = ''
    let getId: Object = null
    return {
      getId,
      statustxt,
      showSIDBtn,
      item,
      deleteItem,
      itemStyle,
      table,
      userlist,
      planlist,
      keywords,
      customer,
      customerTable,
      searchingCustomer,
      bitem,
      profile,
      lastgetPlan,
      lastgetUser,
      customerProfile,
      columns: computed(() => {
        let userlist = this.userlist
        let planlist = this.planlist
        return [
          { title: 'subscriptions.customer', key: 'customer', type: 'string', objectlist: userlist, class: 'text-center' },
          { title: 'subscriptions.sid', key: 'sid', type: 'string', class: 'text-center' },
          { title: 'subscriptions.plan', key: 'plan', type: 'string', objectlist: planlist, class: 'text-center' },
          { title: 'subscriptions.activationDate', key: 'activationdate', type: 'date', class: 'text-center' },
          { title: 'subscriptions.status', key: 'statustxt', type: 'string', objectlist: statuslist, class: 'text-center' },
          { title: 'subscriptions.freeusage', key: 'freeusage', type: 'string', objectlist: statuslist, class: 'text-center' },
          { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
        ]
      })
    }
  },
  methods: {
    getListChecker() {
        //if (Date.now() - this.lastgetPlan > 3000) {
        //  this.lastgetPlan = Date.now()
        this.getPlanList()
        //}
  
        // if (Date.now() - this.lastgetUser > 3000) {
        //   this.lastgetUser = Date.now()
        this.getUserList()
        // }
    },
    assignSID(item: any) {
      if (item.id && !item.sid) {
        getNextSubscriptionID({ token: this.token }).then((res: any) => {
          updateSubscription({ token: this.token, id: item.id, form: { sid: res.data } }).then((res2: any) => {
            pushSubscriptionID({ token: this.token })
            this.loadDatabase()
          })

        })
      }
    },
    searchCustomer() {
      if (this.customer != '') {
        this.searchingCustomer = true;
        getUsers({
          token: this.token,
          $keywords: this.customer
        })
        .then((rs: any) => {
          const dataArray = rs.data.map((user: any) => {              
            const customerId = user.id;
            this.table.customer = customerId;
            return { ...this.table, token: this.token };
          });
          subscriptionStore.getAllSubscriptionsByCustomers(dataArray).then(() => {
            this.searchingCustomer = false;
          }).catch(() => {
            this.searchingCustomer = false;
          });
        });
      }
    },
    searchNow() {
      this.table.page = 1
      this.table.keywords = this.keywords
      this.loadDatabase()
    },
    searchFunc(p: string) {
      this.keywords = p
      this.searchNow()
    },
    filterFunc() {
      this.table.page = 1;
      this.table.statustxt = this.statustxt;
      this.loadDatabase();
    },
    reload() {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.table.name = ''
      this.customer = ''
      this.table.customer = ''
      this.table.statustxt = ''
      this.statustxt = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      subscriptionStore.getSubscriptions(p)      
    },
    pageChange(p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem() {
      this.item = JSON.parse(JSON.stringify(this.itemStyle))
    },
    addSubscription() {
      this.initItem()
    },
    cancelNow() {
      if (this.$route.params.id) {
        this.$router.push({ name: 'ticketsCRUD', params: { id: this.$route.params.ticketId } })
      } else {
        this.item = null
      }
      // this.reload()
    },
    editRow(item: any, index: any) {
      if (!this.item) {
        this.item = JSON.parse(JSON.stringify(Object.assign({ id: item.ID }, item)))
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
        this.item = JSON.parse(JSON.stringify(this.item))
      }
    },
    // duplicateRow (p: any, i: any) {
    //   this.item = JSON.parse(JSON.stringify(p))
    //   delete this.item.id
    //   delete this.item.updated_at
    //   delete this.item.created_at
    // },
    floatfix(p: any) {
      let g = 0
      if (p && !isNaN(p)) {
        g = parseFloat(p.toFixed(2))
      }
      return g
    },
    intfix(p: any) {
      let g = 0
      if (p && !isNaN(p)) {
        g = parseInt(p)
      }
      return g
    },
    saveFunc(p: any) {
      // number fix
      p.deposit = this.floatfix(p.deposit)
      p.advancedpayment = this.floatfix(p.advancedpayment)
      p.balanceadvancepayment = this.floatfix(p.balanceadvancepayment)
      p.installationfee = this.floatfix(p.installationfee)
      p.monthlycharges = this.floatfix(p.monthlycharges)
      p.freeusage = this.intfix(p.freeusage)
      p.contractmonths = this.intfix(p.contractmonths)
      // if (p.deposit) {
      //   p.deposit = parseFloat(p.deposit)
      // }
      // if (p.advancedpayment) {
      //   p.advancedpayment = parseFloat(p.advancedpayment)
      // }
      // if (p.freeusage) {
      //   p.freeusage = parseInt(p.freeusage)
      // }
      if (p.id) {
        subscriptionStore.updateSubscription({ form: p, id: p.id, token: this.token })
        axios.get("https://ktic.com.my/api/hi5_status_update.php", {
          params: {
            security_token: "KTIC20240625-17678-897KBVHJV",
            order_no: p.sid,
            status: p.statustxt
          }
        })
      } else {
        getNextSubscriptionID({ token: this.token }).then((res: any) => {
          p.sid = res.data
          pushSubscriptionID({ token: this.token })
          subscriptionStore.createSubscription({ form: p, token: this.token })
        })

      }
      // this.reload()
    },
    saveSilent() {
      let p = this.item
      p.deposit = this.floatfix(p.deposit)
      p.advancedpayment = this.floatfix(p.advancedpayment)
      p.balanceadvancepayment = this.floatfix(p.balanceadvancepayment)
      p.installationfee = this.floatfix(p.installationfee)
      p.monthlycharges = this.floatfix(p.monthlycharges)
      p.freeusage = this.intfix(p.freeusage)
      p.contractmonths = this.intfix(p.contractmonths)
      if (p.id) {
        updateSubscription({ form: p, id: p.id, token: this.token }).then((rs: any) => {
          if (rs && rs.data) {
            this.item = JSON.parse(JSON.stringify(rs.data))
          }
        })
      } else {
        getNextSubscriptionID({ token: this.token }).then((res: any) => {
          p.sid = res.data
          pushSubscriptionID({ token: this.token })
          createSubscription({ form: p, token: this.token }).then((rs: any) => {
            if (rs && rs.data) {
              this.item = JSON.parse(JSON.stringify(rs.data))
            }
          })
        })
      }
    },
    deleteRow(p: any, i: any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.name + '?',
        proceedTxt: this.$t('c.yes'),
        proceedFunc: () => { this.deleteNow(p.id) },
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      subscriptionStore.deleteSubscription({ token: this.token, id })
    },
    getUserList() {
      // if (this.lastgetUser > Date.now()) {
      // this.lastgetUser = Date.now() + 500
      let useridlist2 = this.userlist.map((p: any) => p.id)
      let useridlist = []
      // this.userlist = []
        // console.log(this.databases);
      
      if (this.databases) {        
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i]
          if (data.customer) {
            useridlist.push(data.customer)
          }
          if (data.agent) {
            useridlist.push(data.agent)
          }
          if (data.user) {
            useridlist.push(data.user)
          }
        }
        useridlist = this.removeDuplicate(useridlist)
        useridlist = useridlist.filter((p: any) => useridlist2.indexOf(p) === -1)
        useridlist.filter(k => {
          getUserName({ token: this.token, id: k }).then((rs: any) => {
            this.userlist.push({
              id: k,
              value: rs.name || rs.email || k + ' (' + this.$t('c.noNameNoEmail') + ')'
            })
          })
        })
      }
      // } else {
      //   console.log('s ' + Date.now())
      //   // setTimeout(() => {
      //   //   this.getUserList()
      //   // }, 1000)
      // }      
    },
    getPlanList() {
      let planidlist2 = this.planlist.map((p: any) => p.id)
      let planidlist = []
      // this.planlist = []
      if (this.databases) {        
        for (let i = 0; i < this.databases.data.length; i++) {
          let data = this.databases.data[i]
          planidlist.push(data.plan)
        }
        planidlist = this.removeDuplicate(planidlist)
        planidlist = planidlist.filter((p: any) => planidlist2.indexOf(p) === -1)
        planidlist.filter(k => {
          getPlan({ token: this.token, id: k }).then((rs) => {
            this.planlist.push({
              id: k,
              value: rs.data.title || k + ' (' + this.$t('c.noTitle') + ')'
            })
          })
        })
      }
    },
    removeDuplicate(arraylist: any) {
      return arraylist = [...new Set(arraylist)]
    },
  },
  computed: {
    databases() {            
      return subscriptionStore.getState().subscriptions
    },
    subscriptionCreaten() {
      return subscriptionStore.getState().subscriptionCreate
    },
    subscriptionCreateSuccess() {
      return subscriptionStore.getState().subscriptionCreateSuccess
    },
    subscriptionCreateError() {
      return subscriptionStore.getState().subscriptionCreateError
    },
    subscriptionUpdate() {
      return subscriptionStore.getState().subscriptionUpdate
    },
    subscriptionUpdateSuccess() {
      return subscriptionStore.getState().subscriptionUpdateSuccess
    },
    subscriptionUpdateError() {
      return subscriptionStore.getState().subscriptionUpdateError
    },
    subscriptionDeleteSuccess() {
      return subscriptionStore.getState().subscriptionDeleteSuccess
    },
  },
  watch: {
    subscriptionCreateSuccess(p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
        this.getListChecker()
      }
    },
    subscriptionCreateError(p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    subscriptionUpdateSuccess(p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
        this.getListChecker()
      }
    },
    subscriptionUpdateError(p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('subscriptions.subscription'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    subscriptionDeleteSuccess(p) {
      if (p) {
        this.deleteItem = null
      }
    },
    databases(p, o) {         
      if (p && p != o) {
        this.getListChecker()
      }         
    }
  },
})
</script>
