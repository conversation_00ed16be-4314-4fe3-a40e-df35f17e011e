<template>
    <div>
      <adminheader
          :title="$t('navigations.autocount')"></adminheader>
      <div class="py-8 px-5 m-0 flex flex-wrap content-start items-start">
        <div class="inline-block m-2 w-32 h-24">
          <button class="bg-gray-200 text-gray-800 px-8 py-4 text-lg rounded shadow">
            Debtors
          </button>
        </div>
      </div>
    </div>
  </template>
  <script lang="ts">
  import { defineComponent } from 'vue'
  import adminheader from '@/components/AdminHeader.vue'
  export default defineComponent({
      components: {
          adminheader,
      },
  })
  </script>
  