<template>
  <div>
    <adminheader
        :title="$t('navigations.plans')"
        :addFunction="addPlan"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div v-if="databases == null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
            <button @click="editRow(slotProps.item, slotProps.index)" class="inline-block bg-blue-500 hover:bg-blue-700 text-white p-2 mr-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
            <button @click="deleteRow(slotProps.item, slotProps.index)" class="inline-block bg-red-500 hover:bg-red-700 text-white p-2 mr-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :save="saveFunc"
            :saveSilent="saveSilent"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { planStore } from '../../../store/plan-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    // const auth2State = auth2Store.getState()
    // const planState = planStore.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        // profile: computed(() => auth2State.profile ),
        planStore: planStore,
        planState: planStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform
  },
  mounted () {
    this.reload()
  },
  data () {
    let item: any = null
    let deleteItem: any = null
    let keywords: string = ''
    let table: any = {
      limit: 10,
      page: 1,
      keywords: ''
    }
    let itemStyle: any = {
      user:  auth2Store.getState().profile.id,
      planid: '',
      title: '',
      price: 0,
      tax: 0,
      details: '',
      images: [],
      startdate: '',
      enddate: '',
      voip: false,
      status: true
    }
    return {
      item,
      deleteItem,
      itemStyle,
      table,
      keywords,
    }
  },
  methods: {
    searchFunc (p: string) {
      this.keywords = p
      this.searchNow()
    },
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token, params: ['$hide'], '$hide': '1' }
      this.planStore.getPlans(p)
    },
    pageChange (p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = this.itemStyle
    },
    addPlan () {
      this.initItem()
    },
    cancelNow () {
      this.item = null
      this.reload()
    },
    editRow (item: any, index: any) {
      if (!this.item) {
        this.item = Object.assign({id: item.ID}, item)
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
      }
    },
    duplicateRow (p: any, i: any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc (p: any) {
      if (p.id) {
        this.planStore.updatePlan({ form: p, id: p.id, token: this.token })
      } else {
        this.planStore.createPlan({ form: p, token: this.token })
      }
    },
    saveSilent (p: any) {
      this.planStore.updatePlan({ form: p, id: p.id, token: this.token })
    },
    deleteRow (p: any,i: any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.planid + '?',
        proceedTxt:  this.$t('c.okay'),
        proceedFunc: () => { this.deleteNow(p.id)},
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      planStore.deletePlan({ token: this.token, id })
    }
  },
  computed: {
    columns () {
      return [
        { title: 'plans.title', key: 'title', type: 'string', class: 'text-center' },
        { title: 'plans.price', key: 'price', type: 'price', class: 'text-center text-xs' },
        // { title: 'plans.tax', key: 'tax', type: 'price', class: 'text-center text-xs' },
        { title: 'plans.hide', key: 'hide', type: 'string', class: 'text-center text-xs' },
        { title: 'plans.startdate', key: 'startdate', type: 'date', class: 'text-center' },
        { title: 'plans.enddate', key: 'enddate', type: 'date', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return  planStore.getState().plans
    },
    // planCreateByAdmin () {
    //   return planStore.getState().planCreateByAdmin
    // },
    // planCreateByAdminSuccess () {
    //   return planStore.getState().planCreateByAdminSuccess
    // },
    // planCreateByAdminError () {
    //   return planStore.getState().planCreateByAdminError
    // },
    planUpdate () {
      return planStore.getState().planUpdate
    },
    planUpdateSuccess () {
      return planStore.getState().planUpdateSuccess
    },
    planUpdateError () {
      return planStore.getState().planUpdateError
    },
    planDeleteSuccess () {
      return planStore.getState().planDeleteSuccess
    },
  },
  watch: {
    planCreateByAdminSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('plans.plan'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    planCreateByAdminError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('plans.plan'),
          msg: this.$t('c.createError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    planUpdateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('plans.plan'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    planUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('plans.plan'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    planDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  },
})
</script>
