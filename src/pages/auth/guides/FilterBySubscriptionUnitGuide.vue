<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Filter Bills by Subscription Unit Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Step-by-Step Guide to Filter Bills by Subscription Unit</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Users</span> in the main menu</li>
        <li>In the users table, locate the customer whose bills you want to filter</li>
        <li>Under the <span class="font-medium text-blue-700">Action</span> column, click on the <span class="bg-green-200 px-2 py-1 rounded">dollar sign ($) button</span></li>
        <li>A modal window will popup displaying the <span class="font-medium text-blue-700">invoices list</span> for the selected user</li>
        <li>In the modal, locate and click on the <span class="bg-blue-200 px-2 py-1 rounded">Get Subscription List</span> button</li>
        <li>The system will load and display all available <span class="font-medium text-blue-700">subscription units</span> for the customer</li>
        <li>Select the specific <span class="font-medium text-blue-700">subscription unit(s)</span> you want to filter by:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>Click on individual subscription units to select them</li>
          </ul>
        </li>
        <li>The invoice list will automatically update to show only bills associated with the selected subscription units</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/sendbill1.png" alt="Step 1-3: Navigate to users and click dollar sign" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Navigating to Users and clicking the dollar sign button to access invoices</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscription-filter-by-unit.jpeg" alt="Step 6-8: Select subscription units and filter" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Invoice modal popup and clicking "Get Subscription List" button</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterBySubscriptionUnitGuide'
}
</script>