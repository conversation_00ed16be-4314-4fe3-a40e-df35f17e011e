<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Sync Bills Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Step-by-Step Guide to Sync Bills</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Billings</span> in the main menu</li>
        <li>Browse through the billing list and select the billing record you want to sync</li>
        <li>Click on the Edit button for the selected billing</li>
        <li>In the billing edit view, locate and click on the <span class="bg-gray-200  px-2 py-1 rounded">Sync</span> button</li>
        <li>Wait for the system to process the synchronization</li>
        <li>Check the alert message that appears:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li><span class="text-green-600 font-medium">Success Alert:</span> Billing has been synchronized successfully</li>
            <li><span class="text-red-600 font-medium">Error Alert:</span> Synchronization failed - check the error details</li>
          </ul>
        </li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/syncbillguide.jpeg" alt="Step 1: Navigate to Billings" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Navigating to the Billings section</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/syncbillguide2.jpeg" alt="Step 2: Select billing and click edit" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Selecting a billing record and clicking the Edit button</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/syncbillguide3.jpeg" alt="Step 3: Click sync button" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Clicking the Sync button in the billing edit view</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/syncbillguide4.jpeg" alt="Step 4: Success or error alert" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 4: Success or error alert message after synchronization</p>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">2. Important Notes</h2>
      <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Processing Time:</span> Synchronization may take a few moments depending on the amount of data</li>
          <li><span class="font-medium">Network Connection:</span> Ensure stable internet connection for successful synchronization</li>
          <li><span class="font-medium">Error Handling:</span> If sync fails, check the error message for specific details and try again</li>
          <li><span class="font-medium">Data Backup:</span> Synchronization will update billing data - ensure you have backups if needed</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SyncBillsGuide'
}
</script>