<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Subscription Troubleshoot Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Step-by-Step Guide to Generate Subscription Troubleshoot Report</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Report</span> in the main menu</li>
        <li>From the report options, select <span class="font-medium text-blue-700">Subscription Troubleshoot</span></li>
        <li>Configure your filters:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>Customer Status (Active, Inactive, etc.)</li>
          </ul>
        </li>
        <li>Click the <span class="bg-gray-200 px-2 py-1 rounded">Generate</span> button to create the report</li>
        <li>Wait for the system to process and display the report data</li>
        <li><span class="text-gray-600">(Optional)</span> Click <span class="bg-green-200 px-2 py-1 rounded">Export CSV</span> to download the report as a CSV file</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/subscription-report.jpeg" alt="Step 1: Navigate to Report" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Navigating to the Report section</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscription-report2.jpeg" alt="Step 2: Choose subscription report" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Selecting Subscription Troubleshoot from available options</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscription-report3.jpeg" alt="Step 3: Filter and generate" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Configuring filters and clicking the Generate button</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscription-report4.jpeg" alt="Step 4: Export CSV (Optional)" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 4: Generated report with optional CSV export button</p>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">2. Important Notes</h2>
      <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Processing Time:</span> Large reports may take a few moments to generate depending on data volume</li>
          <li><span class="font-medium">Data Accuracy:</span> Reports reflect real-time data at the time of generation</li>
          <li><span class="font-medium">CSV Export:</span> Exported files retain all filtering applied during report generation</li>
          <li><span class="font-medium">File Format:</span> CSV files can be opened in Excel, Google Sheets, or other spreadsheet applications</li>
          <li><span class="font-medium">Permissions:</span> Ensure you have appropriate permissions to access subscription data</li>
          <li><span class="font-medium">Regular Updates:</span> Generate reports regularly for accurate business insights and tracking</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubscriptionTroubleshootGuide'
}
</script>