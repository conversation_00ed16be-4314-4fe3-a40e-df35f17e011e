<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Create Subscription Group Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Step-by-Step Guide to Create Subscription Group</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Subscription Group</span> in the main menu</li>
        <li>Click on the <span class="bg-blue-200 px-2 py-1 rounded">(+) button</span> to create a new group</li>
        <li>In the group creation form, click on <span class="font-medium text-blue-700">Select User</span> option</li>
        <li>Use the search functionality to find users:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>Enter user name, email, or ID in the search field</li>
            <li>Browse through the user list</li>
            <li>Use filters if available (department, role, status, etc.)</li>
          </ul>
        </li>
        <li>Select the users you want to add to the subscription group by checking their checkboxes</li>
        <li>Choose the <span class="font-medium text-blue-700">Action Type</span> from the dropdown menu:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li><span class="font-medium">Manual:</span> Manual processing and management</li>
            <li><span class="font-medium">Auto:</span> Automatic processing and billing</li>
            <li><span class="font-medium">Semi-Auto:</span> Semi-automatic with manual approval</li>
            <li>Other available action types as per your system</li>
          </ul>
        </li>
        <li>Review all selected users and settings</li>
        <li>Click the <span class="bg-green-200 px-2 py-1 rounded">Submit</span> button to create the subscription group</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/subscriptiongroup1.jpeg" alt="Step 1-2: Navigate and click plus button" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Navigating to Subscription Group and clicking the round plus button</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscriptiongroup2.jpeg" alt="Step 3-4: Select and search users" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Selecting users</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscriptiongroup3.jpeg" alt="Step 5-8: Select users, choose action type and submit" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Using search functionality to find specific users</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/subscriptiongroup4.jpeg" alt="Step 5-8: Select users, choose action type and submit" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 4: Choosing action type and submitting the subscription group</p>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">2. User Selection Best Practices</h2>
      <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Search Efficiently:</span> Use specific keywords to narrow down user results</li>
          <li><span class="font-medium">Verify Users:</span> Double-check user details before adding to the group</li>
          <li><span class="font-medium">User Status:</span> Ensure selected users have active accounts and appropriate permissions</li>
          <li><span class="font-medium">Review Process:</span> Always review the user list before final submission</li>
        </ul>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">3. Important Notes</h2>
      <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">User Permissions:</span> Ensure you have appropriate permissions to create subscription groups</li>
          <li><span class="font-medium">Action Type Impact:</span> Choose action type carefully as it affects billing and management processes</li>
          <li><span class="font-medium">Duplicate Prevention:</span> Verify users aren't already in conflicting subscription groups</li>
          <li><span class="font-medium">Save Progress:</span> Some systems allow saving drafts - use this feature for complex group setups</li>
          <li><span class="font-medium">Confirmation:</span> Wait for confirmation message before navigating away from the page</li>
          <li><span class="font-medium">Documentation:</span> Keep records of group creation for audit and management purposes</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubscriptionGroupGuide'
}
</script>