<template>
    <div>
        <adminheader
            :title="$t('navigations.guides')"></adminheader>
        <div class="mx-1 mr-2">
            <div class="w-full p-6">
                <!-- Search Input -->
                <div class="mb-6">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input
                            v-model="searchQuery"
                            type="text"
                            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                            :placeholder="$t('search.placeholder', 'Search guides...')"
                        >
                        <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button
                                @click="clearSearch"
                                class="text-gray-400 hover:text-gray-600 focus:outline-none"
                                type="button"
                            >
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div v-if="searchQuery" class="mb-4 text-sm text-gray-600">
                    {{ filteredReports.length }} {{ filteredReports.length === 1 ? 'guide' : 'guides' }} found
                </div>

                <div v-if="searchQuery && filteredReports.length === 0" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No guides found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search terms.</p>
                </div>

                <template v-for="p in filteredReports" :key="p.id">
                    <router-link 
                        v-if="hasScopes(p.scopes)" 
                        :to="p.path" 
                        class="group flex items-center p-4 mb-3 bg-white rounded-lg border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-200 hover:-translate-y-0.5"
                    >
                    <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-lg flex items-center justify-center group-hover:from-purple-200 group-hover:to-indigo-200 transition-colors duration-200">
                        <svgCollection 
                        :icon="p.icon" 
                        dclass="h-6 w-6 text-purple-600 group-hover:text-indigo-600 transition-colors duration-200"
                        ></svgCollection>
                    </div>  
                    <div class="ml-4 flex-1">
                        <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-700 transition-colors duration-200">
                        {{ p.title }}
                        </h3>
                    </div>                    
                    <div class="flex-shrink-0 ml-4">
                        <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                    </router-link>
                </template>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  components: {
    adminheader,
    svgCollection,
  },
  data() {
    return {
      searchQuery: ''
    }
  },
  methods: {
    hasScopes (p: any) {
      if (!p) {
        return true
      } else {
        let g: boolean = false
        if (this.scopes) {
          if (Array.isArray(p)) {
            for (let k = 0; k < this.scopes.length; k++) {
              if (p.indexOf(this.scopes[k]) > -1) {
                return true
              }
            }
            return false
          } else {
            return this.scopes.indexOf(p) > -1
          }          
        }
        return g
      }
    },
    clearSearch() {
      this.searchQuery = ''
    }
  },
  computed: {
    scopes () {
      let g: any = auth2Store.getState().profile
      return (g && g.scopes) || []
    },
    reportlist () {
      return [
        {
          id: 'consolidate',
          title: 'Consolidate Invoice Guide',
          icon: 'documenttext',
          path: {name: 'consolidateGuide'}
        },
        {
          id: 'subscriptionfilterguide',
          title: 'Subscription Filter Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionfilterguide'}
        },
        {
          id: 'syncbillingguide',
          title: 'Sync Billing Guide',
          icon: 'documenttext',
          path: {name: 'syncBillsGuide'}
        },
        {
          id: 'subscriptiontroubleshootguide',
          title: 'Subscription Troubleshoot Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionTroubleshootGuide'}
        },
        {
          id: 'customerGuide',
          title: 'Customer Guide',
          icon: 'documenttext',
          path: {name: 'customerGuide'}
        },
        {
          id: 'installerGuide',
          title: 'Installer Guide',
          icon: 'documenttext',
          path: {name: 'installerGuide'}
        },
        {
          id: 'subscriptionGroupGuide',
          title: 'Adding Subscription Group Guide',
          icon: 'documenttext',
          path: {name: 'subscriptionGroupGuide'}
        },
        {
          id: 'sendBillGuide',
          title: 'Send Bill to Customer Guide',
          icon: 'documenttext',
          path: {name: 'sendBillGuide'}
        },
        {
          id: 'filterBySubscriptionUnitGuide',
          title: 'Filter by Subscription Unit Guide',
          icon: 'documenttext',
          path: {name: 'filterBySubscriptionUnitGuide'}
        }
      ]
    },
    filteredReports() {
      if (!this.searchQuery) {
        return this.reportlist
      }
      
      const query = this.searchQuery.toLowerCase().trim()
      return this.reportlist.filter(guide => 
        guide.title.toLowerCase().includes(query) ||
        guide.id.toLowerCase().includes(query)
      )
    }
  }
})
</script>