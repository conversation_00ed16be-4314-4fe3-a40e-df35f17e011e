<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Subscription Filter Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. What are Subscription Filters?</h2>
      <p class="text-gray-700">Subscription filters allow you to search and filter through subscription data to quickly find specific customers, subscription types, or billing information based on various criteria.</p>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">2. Step-by-Step Guide to Use Subscription Filters</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Subscriptions</span> in the sidebar main menu</li>
        <li>Locate the filter section at the top right of the subscription list</li>
        <li>Choose your filter criteria from the available options:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>Pending Deposit</li>
            <li>Pending Installation</li>
            <li>Pending Activation</li>
            <li>Pending Verification</li>
            <li>Pending Completion</li>
            <li>Active</li>
            <li>Suspended</li>
            <li>Cancelled</li>
            <li>Terminated</li>
            <li>Migration</li>
            <li>Return Order</li>
            <li>Relocation</li>
          </ul>
        </li>
        <li>Click the filter you want to apply</li>
        <li>Review the filtered results in the subscription list below</li>
        <li>To clear filters, click the <span class="bg-blue-100 text-blue-700 px-1 rounded">Remove filter</span> button</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/filter_guide_1.jpeg" alt="Step 1: Filter section location" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Location of the filter section in Subscription</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/filter_guide_2.jpeg" alt="Step 2: Click filter by statis" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Clicking on the dropdown to filter by status</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/filter_guide_3.jpeg" alt="Step 3: Using filter options" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Applying filters and viewing results</p>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">3. Tips for Effective Filtering</h2>
      <div class="bg-blue-50 p-4 rounded-lg">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Partial Search:</span> You can search using partial customer names or subscription details</li>
          <li><span class="font-medium">Multiple Filters:</span> Combine different filter criteria for more precise results</li>
          <li><span class="font-medium">Case Insensitive:</span> Search is not case-sensitive, so you can type in any case</li>
          <li><span class="font-medium">Reset Filters:</span> Use the Remove filter button to start fresh with your search</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubscriptionFilterGuide'
}
</script>