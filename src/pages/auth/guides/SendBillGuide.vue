<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Send Bill to Customer Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Step-by-Step Guide to Send Bills to Customers</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Navigate to <span class="font-medium text-blue-700">Users</span> in the main menu</li>
        <li>In the users table, locate the customer you want to send a bill to</li>
        <li>Under the <span class="font-medium text-blue-700">Action</span> column, click on the <span class="bg-green-500 text-white px-2 py-1 rounded-full">$</span></li>
        <li>A modal window will popup displaying the <span class="font-medium text-blue-700">invoices list</span> for the selected user</li>
        <li>Scroll down in the modal and locate the <span class="font-medium text-blue-700">"Show send"</span> checkbox</li>
        <li>Check the <span class="bg-gray-200 px-2 py-1 rounded">Show send</span> checkbox to enable send functionality</li>
        <li>The <span class="bg-yellow-500 text-white px-2 py-1 rounded-full">Send Bill</span> button will appear under the Action column in the invoices list</li>
        <li>Click on the <span class="bg-yellow-500 text-white px-2 py-1 rounded-full">Send Bill</span> button for the invoice you want to send</li>
        <li>Scroll down to the end of the modal to view the <span class="font-medium text-blue-700">invoice number</span> and send options</li>
        <li>Click the <span class="bg-orange-200 px-2 py-1 rounded">Send Now</span> button to initiate the sending process</li>
        <li>A double confirmation alert will appear on the page</li>
        <li>Click <span class="bg-gray-100 px-2 py-1 rounded">Ok</span> to finalize and send the bill to the customer</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/sendbill1.png" alt="Step 1-3: Navigate to users and click dollar sign" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Navigating to Users and clicking the dollar sign button in the Action column</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/sendbill3.png" alt="Step 4-7: Modal popup and show send checkbox" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Invoice modal popup with "Show send" checkbox and Send Bill button activation</p>
        </div>

        <div class="flex flex-col items-center">
          <img src="/src/assets/sendbill4.png" alt="Step 8-12: Send now and confirmation" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Invoice number display, Send Now button, and confirmation dialog</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SendBillCustomerGuide'
}
</script>