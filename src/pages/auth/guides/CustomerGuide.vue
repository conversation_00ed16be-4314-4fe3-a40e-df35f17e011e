<template>
  <div class="p-5 max-w-4xl mx-auto font-sans">
    <button @click="$router.go(-1)" class="absolute top-6 left-30 bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded">Back</button>
    <h1 class="text-2xl md:text-3xl text-blue-600 mb-6 text-center border-b-2 border-gray-200 pb-3">Customer Dashboard Guide</h1>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">1. Accessing Your Customer Dashboard</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Visit the customer portal login page</li>
        <li>Enter your registered <span class="font-medium text-blue-700">email address</span> and <span class="font-medium text-blue-700">password</span></li>
        <li>Click the <span class="bg-blue-200 px-2 py-1 rounded">Login</span> button to access your dashboard</li>
        <li>Upon successful login, you will be redirected to your personal dashboard</li>
      </ol>

      <div class="mt-6 space-y-8">
        <div class="flex flex-col items-center">
          <img src="/src/assets/customer.png" alt="Customer Login Page" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
          <p class="mt-2 text-sm text-gray-600 italic">Figure 1: Customer login page with email and password fields</p>
        </div>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">2. Understanding Your Dashboard</h2>
      <p class="mb-4 text-gray-700">Your dashboard provides a comprehensive overview of your account status and billing information:</p>
      
      <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500 mb-4">
        <h3 class="font-semibold text-blue-800 mb-2">Dashboard Features:</h3>
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Current Month Total:</span> Displays the total amount due for the current month</li>
          <li><span class="font-medium">Bills Table:</span> Complete list of your bills with detailed information</li>
          <li><span class="font-medium">Quick Actions:</span> Easy access to payment and account management options</li>
          <li><span class="font-medium">Account Summary:</span> Overview of your account status and recent activities</li>
        </ul>
      </div>

      <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500 mb-6">
        <h3 class="font-semibold text-green-800 mb-2">Bills Table Information:</h3>
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Invoice Number:</span> Unique identifier for each bill</li>
          <li><span class="font-medium">Date:</span> Bill generation or due date</li>
          <li><span class="font-medium">Amount:</span> Total amount for each bill</li>
          <li><span class="font-medium">Status:</span> Payment status (Paid, Pending, Overdue)</li>
          <li><span class="font-medium">Actions:</span> View details, download, or pay options</li>
        </ul>
      </div>

      <div class="flex flex-col items-center">
        <img src="/src/assets/customer_2.png" alt="Customer Dashboard Overview" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
        <p class="mt-2 text-sm text-gray-600 italic">Figure 2: Customer dashboard showing current month total and bills table</p>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">3. Managing Your Personal Settings</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>From your dashboard, locate and click on the <span class="font-medium text-blue-700">Settings</span> or <span class="font-medium text-blue-700">Profile</span> option</li>
        <li>In the settings page, you can update your personal information including:
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>Name and contact information</li>
            <li>Email address and phone number</li>
            <li>Billing address</li>
            <li>Password and security settings</li>
            <li>Notification preferences</li>
          </ul>
        </li>
        <li>Make the necessary changes to your personal details</li>
        <li>Click <span class="bg-green-200 px-2 py-1 rounded">Save Changes</span> to update your information</li>
        <li>You will receive a confirmation message upon successful update</li>
      </ol>

      <div class="mt-6 flex flex-col items-center">
        <img src="/src/assets/customer_3.png" alt="Customer Settings Page" class="border border-gray-300 rounded-lg shadow-md max-w-full h-auto" />
        <p class="mt-2 text-sm text-gray-600 italic">Figure 3: Customer settings page for editing personal details</p>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">4. Dashboard Navigation Tips</h2>
      <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-gray-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Quick Payment:</span> Click on any bill amount to proceed with payment</li>
          <li><span class="font-medium">Bill Details:</span> Click on invoice numbers to view detailed bill information</li>
          <li><span class="font-medium">Download Options:</span> Most bills can be downloaded as PDF for your records</li>
          <li><span class="font-medium">Search & Filter:</span> Use search functionality to find specific bills or transactions</li>
          <li><span class="font-medium">Mobile Friendly:</span> Dashboard is optimized for both desktop and mobile devices</li>
        </ul>
      </div>
    </div>

    <div class="mb-8">
      <h2 class="text-xl text-blue-800 mb-3 font-semibold">5. Important Notes</h2>
      <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
        <ul class="space-y-2 text-gray-700">
          <li><span class="font-medium">Security:</span> Always log out after accessing your dashboard, especially on shared devices</li>
          <li><span class="font-medium">Data Updates:</span> Dashboard information is updated in real-time</li>
          <li><span class="font-medium">Payment Deadlines:</span> Pay attention to due dates to avoid late fees</li>
          <li><span class="font-medium">Contact Support:</span> Use the help or contact options if you encounter any issues</li>
          <li><span class="font-medium">Regular Monitoring:</span> Check your dashboard regularly to stay updated on your account status</li>
          <li><span class="font-medium">Backup Information:</span> Keep your contact details updated for important notifications</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomerDashboardGuide'
}
</script>