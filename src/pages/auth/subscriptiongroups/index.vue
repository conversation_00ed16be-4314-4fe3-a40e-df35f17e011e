<template>
    <div>
        <adminheader :title="$t('navigations.subscriptiongroups')"
            :addFunction="isAdmin || isDev ? addSubscriptionGroup : ''" :reloadFunction="reload"></adminheader>

        <dtablesearch :searchFunc="searchFunc"></dtablesearch>
        <div v-if="databases == null">
            <dloading />
        </div>
        <template v-else>
            <dtable :columns="columns" :data="databases" columnColor="white">
                <template v-slot:action="slotProps">
                    <button :title="$t('c.edit')" @click="editRow(slotProps.item, slotProps.index)"
                        class="inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full">
                        <svgicon icon="edit" dclass="w-4 h-4 m-1 inline-block" />
                    </button>
                    <button :title="$t('c.billing')" @click="billRow(slotProps.item, slotProps.index)"
                        class="inline-block bg-green-500 hover:bg-green-700 text-white w-7 h-7 mr-2 rounded-full">
                        <svgicon icon="cash" dclass="w-4 h-4 m-1 inline-block" />
                    </button>
                    <button :title="'Generate Statement'" @click="generateStatement(slotProps.item, slotProps.index)"
                        class="inline-block bg-pink-500 hover:bg-pink-700 text-white w-7 h-7 mr-2 rounded-full">
                        <svgicon icon="invoice" dclass="w-4 h-4 m-1 inline-block" />
                    </button>
                </template>
            </dtable>
            <dpagination :total="databases && databases.total || 0" :page="table.page" :limit="table.limit"
                :pageChange="pageChange" defaultColor="blue" />
        </template>
        <template v-if="item">
            <dform :item="item" :cancel="cancelNow" :token="token" :profile="profile" :save="saveFunc"></dform>
        </template>
        <template v-if="billItem">
            <billform :item="billItem" :cancelFunc="cancelBillItem"></billform>
        </template>
        <template v-if="showDateModal">
            <dateselectionmodal
                :item="selectedItemForStatement"
                :cancelFunc="cancelDateSelection"
                :generateFunc="generateStatementWithDates"></dateselectionmodal>
        </template>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { subscriptionGroupStore } from '../../../store/subscriptiongroup-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import billform from './billform.vue'
import dateselectionmodal from './dateselectionmodal.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import { getUser, getUserById, getUserName } from '../../../api'
import { jsPDF } from 'jspdf'
import moment from 'moment'
import { imageBase64 } from '@/assets/logo_base64.js'

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        const authState = authStore.getState()
        const auth2State = auth2Store.getState()
        return {
            token: computed(() => authState.token),
            authStore: authStore,
            authState: authState,
            profile: computed(() => auth2State.profile),
            subscriptionGroupStore: subscriptionGroupStore,
            subscriptionGroupState: subscriptionGroupStore.getState()
        }
    },
    components: {
        adminheader,
        dtablesearch,
        dtable,
        dpagination,
        dloading,
        dform,
        billform,
        dateselectionmodal,
        svgicon,
    },
    mounted() {
        this.reload()
    },
    data() {
        let item: any = null
        let billItem: any = null
        let deleteItem: any = null
        let showDateModal: boolean = false
        let selectedItemForStatement: any = null
        let itemStyle: any = {
            user: '',
            grpupcode: '',
            actiontype: '',
            status: true
        }
        let table: any = {
            limit: 10,
            page: 1,
            keywords: '',
            status: true,
        }
        let keywords: string = ''
        let userlist: any = []
        return {
            item,
            billItem,
            deleteItem,
            showDateModal,
            selectedItemForStatement,
            itemStyle,
            table,
            keywords,
            userlist
        }
    },
    methods: {
        getListChecker() {
            this.getUserList()
        },
        getUserList() {
            let useridlist2 = this.userlist.map((p: any) => p.id)
            let useridlist = []

            if (this.databases) {
                for (let i = 0; i < this.databases.data.length; i++) {
                    let data = this.databases.data[i]
                    if (data.customer) {
                        useridlist.push(data.customer)
                    }
                    if (data.agent) {
                        useridlist.push(data.agent)
                    }
                    if (data.user) {
                        useridlist.push(data.user)
                    }
                }
                useridlist = this.removeDuplicate(useridlist)
                useridlist = useridlist.filter((p: any) => useridlist2.indexOf(p) === -1)
                useridlist.filter(k => {
                    getUserName({ token: this.token, id: k }).then((rs: any) => {
                        this.userlist.push({
                            id: k,
                            value: rs.name || rs.email || k + ' (' + this.$t('c.noNameNoEmail') + ')'
                        })
                    })
                })
            }
        },
        removeDuplicate(arraylist: any) {
            return arraylist = [...new Set(arraylist)]
        },
        searchNow() {
            this.table.page = 1
            this.table.keywords = this.keywords
            this.loadDatabase()
        },
        searchFunc(p: string) {
            this.keywords = p
            this.searchNow()
        },
        reload() {
            this.table.page = 1
            this.table.keywords = ''
            this.keywords = ''
            this.loadDatabase()
        },
        loadDatabase() {
            let p = { ...this.table, token: this.token }
            this.subscriptionGroupStore.getSubscriptionGroups(p)
        },
        pageChange(p: any) {
            this.table.page = p
            this.loadDatabase()
        },
        initItem() {
            this.item = this.itemStyle
        },
        addSubscriptionGroup() {
            this.initItem()
        },
        cancelNow() {
            this.item = null
            this.reload()
        },
        cancelBillItem() {
            this.billItem = null
            this.reload()
        },
        editRow(item: any, index: any) {
            if (!this.item) {
                if (item.ID) { this.item = Object.assign({ id: item.ID }, item) }
                else { this.item = Object.assign({}, item) }

                this.item = JSON.parse(JSON.stringify(this.item))
            }
        },
        duplicateRow(p: any, i: any) {
            this.item = Object.assign({}, p)
            delete this.item.id
            delete this.item.updated_at
            delete this.item.created_at
        },
        billRow(item: any, index: any) {
            getUserById({ token: this.token, id: item.user }).then((res: any) => {
                this.billItem = res
            })
            // if (!this.billItem) {
            //     if (item.ID) { this.billItem = Object.assign({ id: item.ID }, item) }
            //     else { this.billItem = Object.assign({}, item) }

            //     this.billItem = JSON.parse(JSON.stringify(this.billItem))
            // }
        },
        saveFunc(p: any) {
            if (p.id) {
                this.subscriptionGroupStore.updateSubscriptionGroup({ form: p, id: p.id, token: this.token })
            } else {
                this.subscriptionGroupStore.createSubscriptionGroup({ form: p, token: this.token })
            }
            this.item = null
            this.billItem = null
            this.reload()
        },
        generateStatement(item: any, index: any) {
            // Show the date selection modal instead of directly generating
            this.selectedItemForStatement = item
            this.showDateModal = true
        },
        cancelDateSelection() {
            this.showDateModal = false
            this.selectedItemForStatement = null
        },
        generateStatementWithDates(params: any) {
            // Close the modal
            this.showDateModal = false
            this.selectedItemForStatement = null

            // Generate the statement with the selected dates
            this.generateBankStatement(params.item, params.startDate, params.endDate)
        },
        async generateBankStatement(subscriptionGroup: any, startDate?: Date, endDate?: Date) {
            try {
                // Create a new jsPDF instance
                const doc = new jsPDF()

                // Get user information
                const userName = this.getUserDisplayName(subscriptionGroup.user)
                const todayDate = moment().format('DD/MM/YYYY')

                // Use provided dates or default to previous month
                const statementStartDate = startDate ? moment(startDate) : moment().subtract(1, 'month').startOf('month')
                const statementEndDate = endDate ? moment(endDate) : moment().subtract(1, 'month').endOf('month')
                const statementPeriod = `${statementStartDate.format('DD/MM/YYYY')} - ${statementEndDate.format('DD/MM/YYYY')}`

                // Add logo
                doc.addImage(imageBase64, 'JPEG', 15, 15, 40, 40)

                // Add company header
                doc.setFontSize(16)
                doc.setFont('helvetica', 'bold')
                doc.text('HIIFI (M) SDN. BHD.', 70, 25)

                doc.setFontSize(10)
                doc.setFont('helvetica', 'normal')
                doc.text('A1-2-7, Level 2, Block A1, Sunway Geo Avenue', 70, 32)
                doc.text('Jalan Lagoon Selatan, 47500 Subang Jaya', 70, 37)
                doc.text('Tel: +603-5612 7799 | Email: <EMAIL>', 70, 42)

                // Statement title
                doc.setFontSize(18)
                doc.setFont('helvetica', 'bold')
                doc.text('ACCOUNT STATEMENT', 105, 70, { align: 'center' })

                // Account information
                doc.setFontSize(11)
                doc.setFont('helvetica', 'normal')
                doc.text(`Account Holder: ${userName}`, 20, 90)
                doc.text(`Group Code: ${subscriptionGroup.groupcode || 'N/A'}`, 20, 97)
                doc.text(`Statement Period: ${statementPeriod}`, 20, 104)
                doc.text(`Statement Date: ${todayDate}`, 20, 111)
                doc.text(`Account Type: ${subscriptionGroup.actiontype || 'Standard'}`, 20, 118)

                // Add a line separator
                doc.setLineWidth(0.5)
                doc.line(20, 125, 190, 125)

                // Transaction header
                doc.setFontSize(12)
                doc.setFont('helvetica', 'bold')
                doc.text('TRANSACTION SUMMARY', 20, 135)

                // Table headers
                doc.setFontSize(10)
                doc.setFont('helvetica', 'bold')
                doc.text('Date', 20, 150)
                doc.text('Description', 50, 150)
                doc.text('Reference', 120, 150)
                doc.text('Amount (MYR)', 160, 150)

                // Add line under headers
                doc.setLineWidth(0.3)
                doc.line(20, 152, 190, 152)

                // Generate dummy transactions for the selected period
                const transactions = this.generateDummyTransactions(statementStartDate, statementEndDate)
                let yPosition = 160
                let runningBalance = 1250.00

                doc.setFont('helvetica', 'normal')

                // Opening balance
                doc.text(statementStartDate.format('DD/MM/YYYY'), 20, yPosition)
                doc.text('Opening Balance', 50, yPosition)
                doc.text('BAL-' + statementStartDate.format('YYYYMM'), 120, yPosition)
                doc.text(this.formatMoney(runningBalance), 160, yPosition)
                yPosition += 8

                // Add transactions
                transactions.forEach((transaction: any) => {
                    if (yPosition > 270) {
                        doc.addPage()
                        yPosition = 30

                        // Repeat headers on new page
                        doc.setFont('helvetica', 'bold')
                        doc.text('Date', 20, yPosition)
                        doc.text('Description', 50, yPosition)
                        doc.text('Reference', 120, yPosition)
                        doc.text('Amount (MYR)', 160, yPosition)
                        doc.line(20, yPosition + 2, 190, yPosition + 2)
                        yPosition += 10
                        doc.setFont('helvetica', 'normal')
                    }

                    runningBalance += transaction.amount

                    doc.text(transaction.date, 20, yPosition)
                    doc.text(transaction.description, 50, yPosition)
                    doc.text(transaction.reference, 120, yPosition)
                    doc.text(this.formatMoney(transaction.amount), 160, yPosition)
                    yPosition += 8
                })

                // Closing balance
                yPosition += 5

                // Check if we need a new page for closing balance and summary
                if (yPosition > 240) {
                    doc.addPage()
                    yPosition = 30
                }

                doc.setFont('helvetica', 'bold')
                doc.text('Closing Balance:', 120, yPosition)
                doc.text(this.formatMoney(runningBalance), 160, yPosition)

                // Add summary box - ensure we have enough space (need at least 40 units)
                yPosition += 15
                if (yPosition > 240) {
                    doc.addPage()
                    yPosition = 30
                }

                doc.setLineWidth(0.5)
                doc.rect(20, yPosition, 170, 35)

                doc.setFontSize(11)
                doc.setFont('helvetica', 'bold')
                doc.text('ACCOUNT SUMMARY', 25, yPosition + 12)
                doc.setFont('helvetica', 'normal')
                doc.setFontSize(10)
                doc.text(`Total Credits: ${this.formatMoney(transactions.filter((t: any) => t.amount > 0).reduce((sum: number, t: any) => sum + t.amount, 0))}`, 25, yPosition + 22)
                doc.text(`Total Debits: ${this.formatMoney(Math.abs(transactions.filter((t: any) => t.amount < 0).reduce((sum: number, t: any) => sum + t.amount, 0)))}`, 25, yPosition + 30)
                doc.text(`Current Balance: ${this.formatMoney(runningBalance)}`, 120, yPosition + 22)
                doc.text(`Available Balance: ${this.formatMoney(runningBalance)}`, 120, yPosition + 30)

                // Footer - position it at the bottom of the current page
                const footerY = Math.max(yPosition + 45, 275)
                doc.setFontSize(8)
                doc.setFont('helvetica', 'italic')
                doc.text('This is a computer-generated statement. No signature is required.', 105, footerY, { align: 'center' })
                doc.text('For inquiries, please contact our customer service at +603-5612 7799', 105, footerY + 5, { align: 'center' })

                // Save and open the PDF
                const fileName = `account_statement_${subscriptionGroup.groupcode || 'unknown'}_${moment().format('YYYYMM')}.pdf`
                doc.save(fileName)

            } catch (error) {
                console.error('Error generating bank statement:', error)
                alert('Failed to generate account statement. Please try again later.')
            }
        },
        getUserDisplayName(userId: any) {
            const user = this.userlist.find((u: any) => u.id === userId)
            return user ? user.value : `User ${userId}`
        },
        generateDummyTransactions() {
            const transactions = []
            const currentMonth = moment().subtract(1, 'month')

            // Generate 10-15 random transactions for the previous month
            const transactionCount = Math.floor(Math.random() * 6) + 10

            for (let i = 0; i < transactionCount; i++) {
                const day = Math.floor(Math.random() * 28) + 1
                const transactionDate = currentMonth.clone().date(day)

                const transactionTypes = [
                    { desc: 'Monthly Subscription Fee', amount: -89.90, ref: 'SUB' },
                    { desc: 'Internet Service Charge', amount: -129.00, ref: 'ISC' },
                    { desc: 'Payment Received - Thank You', amount: 200.00, ref: 'PAY' },
                    { desc: 'Equipment Rental', amount: -25.00, ref: 'EQP' },
                    { desc: 'Installation Fee', amount: -150.00, ref: 'INS' },
                    { desc: 'Late Payment Charge', amount: -15.00, ref: 'LPC' },
                    { desc: 'Credit Adjustment', amount: 50.00, ref: 'ADJ' },
                    { desc: 'Refund Processing', amount: 75.00, ref: 'REF' },
                    { desc: 'Service Upgrade', amount: -45.00, ref: 'UPG' },
                    { desc: 'Promotional Credit', amount: 30.00, ref: 'PRO' }
                ]

                const randomTransaction = transactionTypes[Math.floor(Math.random() * transactionTypes.length)]

                transactions.push({
                    date: transactionDate.format('DD/MM/YYYY'),
                    description: randomTransaction.desc,
                    reference: randomTransaction.ref + '-' + transactionDate.format('YYYYMMDD') + String(i + 1).padStart(3, '0'),
                    amount: randomTransaction.amount
                })
            }

            // Sort transactions by date
            transactions.sort((a, b) => moment(a.date, 'DD/MM/YYYY').valueOf() - moment(b.date, 'DD/MM/YYYY').valueOf())

            return transactions
        },
        formatMoney(amount: number) {
            if (amount == null) return '0.00'
            return amount.toFixed(2)
        },
    },
    computed: {
        columns() {
            let userlist = this.userlist
            return [
                { title: 'subscriptiongroups.user', key: 'user', type: 'string', objectlist: userlist, class: 'text-center' },
                { title: 'subscriptiongroups.groupcode', key: 'groupcode', type: 'string', class: 'text-center' },
                { title: 'subscriptiongroups.actiontype', key: 'actiontype', type: 'string', class: 'text-center' },
                { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
            ]
        },
        databases() {
            return subscriptionGroupStore.getState().subscriptionGroups
        },
        subscriptionGroupUpdate() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdate
        },
        subscriptionGroupUpdateSuccess() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdateSuccess
        },
        subscriptionGroupUpdateError() {
            return subscriptionGroupStore.getState().subscriptionGroupUpdateError
        },
        subscriptionGroupDeleteSuccess() {
            return subscriptionGroupStore.getState().subscriptionGroupDeleteSuccess
        },
        scopes() {
            let s: any = auth2Store.getState().profile

            return s && s.scopes
        },
        isAdmin() {
            let p: any = this.scopes
            if (p.includes('admin')) {
                return true;
            }
            return false;
        },
        isDev() {
            let p: any = this.scopes
            if (p.includes('dev')) {
                return true;
            }
            return false;
        },
    },
    watch: {
        subscriptionGroupUpdateSuccess(p) {
            if (p) {
                this.item = null
                crossStore.SetNotmsg({
                    title: this.$t('c.updateTitle') + this.$t('subscriptionGroups.subscriptionGroup'),
                    msg: this.$t('c.updateSuccess'),
                    type: 'success'
                })
            }
            this.getListChecker()
        },
        subscriptionGroupUpdateError(p) {
            if (p) {
                crossStore.SetModalmsg({
                    title: this.$t('c.updateTitle') + this.$t('subscriptionGroups.subscriptionGroup'),
                    msg: this.$t('c.updateError'),
                    type: 'error',
                    proceedTxt: this.$t('c.okay')
                })
            }
            this.getListChecker()
        },
        databases(p, o) {
            if (p && p != o) {
                this.getListChecker()
            }
        }
    },
})
</script>