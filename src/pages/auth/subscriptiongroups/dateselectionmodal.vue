<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="80"
      :title="'Select Date Range for Statement'"
      :btnYesText="'Generate PDF'"
      :btnYesFunction="generateStatement"
      :btnNoText="'Close'"
      :btnNoFunction="cancel">
    <div class="p-4">
      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Generate Statement for:</h3>
        <p class="text-gray-600">Group/Subscription: {{ displayTitle }}</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <FormItemCompt
            labelFor="startdate"
            :labelTitle="'Start Date'"
            :required="true">
          <DatePicker
              v-model="startDate"
              :clearable="true"
              defaultColor="blue"></DatePicker>
          <ErrTextCompt :errs="errors && errors.startDate"></ErrTextCompt>
        </FormItemCompt>

        <FormItemCompt
            labelFor="enddate"
            :labelTitle="'End Date'"
            :required="true">
          <DatePicker
              v-model="endDate"
              :clearable="true"
              defaultColor="blue"></DatePicker>
          <ErrTextCompt :errs="errors && errors.endDate"></ErrTextCompt>
        </FormItemCompt>

        <FormItemCompt
            labelFor="subscription"
            :labelTitle="'Subscription'"
            :required="true">
          <select v-model="selectedSubscriptionId" class="border rounded p-2 w-full text-sm">
            <option value="" disabled>Select a subscription</option>
            <option v-for="s in subscriptions" :key="s.id" :value="s.id">
              {{ formatSubscriptionOption(s) }}
            </option>
          </select>
          <ErrTextCompt :errs="errors && errors.subscription"></ErrTextCompt>
        </FormItemCompt>

        <div class="mt-2">
          <button
            v-if="statement.length"
            @click="printPdf"
            class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
            Print PDF
          </button>
        </div>
      </div>

      <div class="mt-4">
        <div v-if="loading" class="text-sm text-gray-500">Loading statement...</div>
        <div v-if="error" class="text-sm text-red-600">{{ error }}</div>

        <div v-if="statement.length" class="overflow-auto border rounded">
          <table class="min-w-full text-xs">
            <thead class="bg-gray-100 text-gray-700">
              <tr>
                <th class="px-3 py-2 text-left">#</th>
                <th v-for="col in tableColumns" :key="col" class="px-3 py-2 text-left capitalize">{{ col }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, idx) in statement" :key="idx" class="border-t">
                <td class="px-3 py-1">{{ idx + 1 }}</td>
                <td v-for="col in tableColumns" :key="col" class="px-3 py-1 whitespace-nowrap">
                  {{ formatCell(row[col], col) }}
                </td>
              </tr>
              <tr v-if="!statement.length">
                <td :colspan="tableColumns.length + 1" class="text-center py-2">No records</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </PopupModal>
</template>

<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import moment from 'moment'
import { getStatementOfAccount, getSubscriptions } from '../../../api'
import { jsPDF } from 'jspdf'

export default defineComponent({
  props: {
    item: { type: Object, required: true },
    cancelFunc: { type: Function, required: true },
    // generateFunc kept for backward compatibility but not used anymore
    generateFunc: { type: Function, required: true },
    userlist: { type: Array, default: () => [] }
  },
  components: { PopupModal, FormItemCompt, DatePicker, ErrTextCompt },
  setup() {
    const authStore: any = inject('authStore')
    const authState = authStore.getState()
    return {
      token: computed(() => authState.token),
    }
  },
  data() {
    return {
      startDate: moment().subtract(1, 'month').startOf('month').toDate() as Date,
      endDate: moment().subtract(1, 'month').endOf('month').toDate() as Date,
      errors: {} as any,
      loading: false as boolean,
      error: '' as string,
      statement: [] as any[],
      subscriptions: [] as any[],
      selectedSubscriptionId: '' as string,
    }
  },
  mounted() {
    this.loadSubscriptionsForUser()
  },
  computed: {
    displayTitle(): string {
      // Try a few common identifiers for a user-friendly title
      return (
        (this.item && (this.item.groupcode || this.item.sid || this.item.name || this.item.email)) || 'N/A'
      )
    },
    subscriptionId(): string | null {
      // Use explicitly selected subscription if present
      if (this.selectedSubscriptionId) return this.selectedSubscriptionId
      const it: any = this.item || {}
      return (
        it.subscription?.id || it.subscription || it.id || null
      )
    },
    tableColumns(): string[] {
      if (!this.statement.length) return []
      // Use keys from first row; keep a reasonable order if standard fields exist
      const keys = Object.keys(this.statement[0])
      const preferred = ['date', 'description', 'reference', 'debit', 'credit', 'amount', 'balance']
      const ordered = preferred.filter(k => keys.includes(k))
      const rest = keys.filter(k => !ordered.includes(k))
      return [...ordered, ...rest]
    }
  },
  methods: {
    async loadSubscriptionsForUser() {
      try {
        const userId = (this.item && (this.item.user || this.item.customer)) || ''
        if (!userId) return
        const res: any = await getSubscriptions({ token: this.token, customer: userId, limit: 100, page: 1 })
        const rows = res?.data || []
        this.subscriptions = rows
        // Default selected to the first subscription if available
        this.selectedSubscriptionId = rows[0]?.id || ''
      } catch (e) {
        // swallow
      }
    },
    formatSubscriptionOption(s: any) {
      if (!s) return ''
      const addr = s.address ? `${s.address.block}-${s.address.level}-${s.address.unit} ${s.address.building}` : ''
      const sid = s.sid || s.id
      return addr ? `${sid} - ${addr}` : String(sid)
    },
    cancel() {
      this.cancelFunc()
    },
    async generateStatement() {
      // Validate dates
      this.errors = {}
      this.error = ''

      if (!this.startDate) {
        this.errors.startDate = ['Start date is required']
        return
      }
      if (!this.endDate) {
        this.errors.endDate = ['End date is required']
        return
      }
      if (moment(this.startDate).isAfter(moment(this.endDate))) {
        this.errors.endDate = ['End date must be after start date']
        return
      }
      if (!this.subscriptionId) {
        this.errors.subscription = ['Please select a subscription']
        return
      }

      this.loading = true
      try {
        const startdate = moment(this.startDate).format('YYYY-MM-DD')
        const enddate = moment(this.endDate).format('YYYY-MM-DD')
        const form = { startdate, enddate }
        const res: any = await getStatementOfAccount({
          token: this.token,
          subscription: this.subscriptionId,
          form
        })
        // Expecting PDF blob back
        const blob = new Blob([res], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `statement_${this.subscriptionId}_${startdate}_${enddate}.pdf`
        document.body.appendChild(a)
        a.click()
        a.remove()
        window.URL.revokeObjectURL(url)
        this.statement = []
      } catch (e: any) {
        this.error = e?.response?.data?.message || 'Failed to load statement'
        this.statement = []
      } finally {
        this.loading = false
      }
    },
    formatCell(val: any, col: string) {
      if (val == null) return ''
      if (col.toLowerCase().includes('date')) {
        // Try to parse as date
        const m = moment(val)
        return m.isValid() ? m.format('DD/MM/YYYY') : String(val)
      }
      if (['amount', 'debit', 'credit', 'balance', 'total'].includes(col.toLowerCase())) {
        const num = typeof val === 'number' ? val : parseFloat(val)
        return isNaN(num) ? String(val) : num.toFixed(2)
      }
      return String(val)
    },
    printPdf() {
      const doc = new jsPDF()
      const startdate = moment(this.startDate).format('DD/MM/YYYY')
      const enddate = moment(this.endDate).format('DD/MM/YYYY')

      // Header
      doc.setFontSize(14)
      doc.text('Statement of Account', 105, 15, { align: 'center' })
      doc.setFontSize(10)
      doc.text(`Period: ${startdate} - ${enddate}`, 105, 22, { align: 'center' })
      if (this.subscriptionId) doc.text(`Subscription: ${this.subscriptionId}`, 105, 28, { align: 'center' })

      // Table
      let y = 40
      doc.setFontSize(9)
      const cols = this.tableColumns
      const colWidths = cols.map(() => 180 / (cols.length || 1))

      // Headers
      let x = 15
      doc.setFont('helvetica', 'bold')
      doc.text('#', x, y)
      x += 8
      cols.forEach((c, idx) => {
        doc.text(String(c).toUpperCase(), x, y)
        x += colWidths[idx]
      })
      doc.setFont('helvetica', 'normal')
      y += 6
      doc.line(15, y, 195, y)
      y += 4

      // Rows
      this.statement.forEach((row: any, i: number) => {
        if (y > 280) { doc.addPage(); y = 20 }
        let xx = 15
        doc.text(String(i + 1), xx, y)
        xx += 8
        cols.forEach((c, idx) => {
          let text = this.formatCell(row[c], c)
          // Right align numeric columns
          const isNum = ['amount', 'debit', 'credit', 'balance', 'total'].includes(c.toLowerCase())
          if (isNum) {
            const w = colWidths[idx]
            const tx = xx + w - 2
            doc.text(String(text), tx, y, { align: 'right' as any })
          } else {
            doc.text(String(text), xx, y)
          }
          xx += colWidths[idx]
        })
        y += 6
      })

      doc.save(`statement_${this.subscriptionId || 'subscription'}_${moment(this.startDate).format('YYYYMMDD')}_${moment(this.endDate).format('YYYYMMDD')}.pdf`)
    }
  }
})
</script>
