<template>
  <PopupModal defaultColor="blue" modalWidthPercent="90"
    :title="item.id ? $t('subscriptiongroups.update') : $t('subscriptiongroups.create')" :btnYesText="$t('c.submit')"
    :btnYesFunction="submitNow" :btnNoText="$t('c.cancel')" :btnNoFunction="cancel" v-if="item">
    <form @submit.prevent="preventsubmit" autocomplete="">
      <div class="inline-block w-full">
        <FormItemCompt class="w-full inline-block px-1" labelFor="user" :labelTitle="$t('subscriptiongroups.user')"
          :required="true">
          <UserAssignInput :token="token" :addUser="addUser" :removeUser="removeUser" :searchUser="searchUser"
            :additionalParamsSearch="{ user: true, params: ['user'] }" :users="user" itemcls="w-full" :readonly="false"
            :multiple="false" :plcHolder="$t('subscriptiongroups.userKeySearch')" :noUserText="$t('subscriptiongroups.nouser')"
            :addText="$t('subscriptiongroups.selectuser')" defaultColor="blue"></UserAssignInput>
          <ErrTextCompt :errs="errs && errs.user"></ErrTextCompt>
        </FormItemCompt>
      </div>
      <FormItemCompt class="w-full md:w-1/2 inline-block px-1" labelFor="groupcode"
        :labelTitle="$t('subscriptiongroups.groupcode')" :required="true">
        <TextInput name="groupcode" type="string" v-model="item.groupcode" defaultColor="blue">
        </TextInput>
        <ErrTextCompt :errs="errs && errs.groupcode"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt class="w-full md:w-1/2 inline-block px-1" labelFor="actiontype"
        :labelTitle="$t('subscriptiongroups.actiontype')" :required="true">
        <!-- <TextInput name="actiontype" type="string" v-model="item.actiontype" defaultColor="blue">
        </TextInput> -->
        <select class="border-0 p-4 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-blue-400" v-model="item.actiontype">
          <option value="manual" selected>Manual</option>
          <option value="auto">Auto</option>
          <option value="etc">Etc.</option>
        </select>
        <ErrTextCompt :errs="errs && errs.actiontype"></ErrTextCompt>
      </FormItemCompt>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import SelectList from '@/components/cvui/form/SelectList.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import DateTimePicker from '@/components/cvui/form/DateTimePicker.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import PopupModal from '@/components/cvui/Modal.vue'
import UserAssignInput from '../../../components/cvui/form/UserAssignInput.vue'
import { getUserName, searchUser } from '../../../api'
export default defineComponent({
  props: {
    item: { type: Object, required: true },
    cancel: { type: Function },
    save: { type: Function },
    profile: { type: Object },
    token: {
      type: String,
      required: true
    }
  },
  computed: {
    //
  },
  mounted() {
    document.addEventListener('keydown', this.handleEscKey);
    this.loadUser()
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  components: {
    PopupModal,
    FormItemCompt,
    TextInput,
    SelectList,
    Checkbox,
    DateTimePicker,
    ErrTextCompt,
    SelectOne,
    DatePicker,
    UserAssignInput
  },
  methods: {
    addUser(p: any) {
      if (p) {
        if (!this.item.user) {
          this.item.user = ''
        }
        if (this.item.user !== p.ID) {
          this.item.user = p.ID
          this.user.push(p)
        }
        this.showUser = true
      }
    },
    removeUser(p: any) {
      if (p) {
        this.user = []
        this.item.user = ''
      }
    },
    loadUser () {
      this.user = []
      if (this.item.user && this.item.user.trim().length > 0) {
        getUserName({token: this.token, id: this.item.user}).then((res: any) => {
          this.user.push(res)
        })
      }
    },
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        if (this.cancel) {
          this.cancel()
        }
      }
    },
    pushArray() {
      if (!Array.isArray(this.item.charges)) {
        this.item.charges = [];
      }
      this.item.charges.push(["",]);
    },
    validateForm() {
      let p = true
      if (!this.item.user || this.item.user.trim().length == 0) {
        this.errs['user'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.groupcode || this.item.groupcode.trim().length == 0) {
        this.errs['groupcode'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.actiontype || this.item.actiontype.trim().length == 0) {
        this.errs['actiontype'] = 'c.fieldRequired'
        p = false
      }
      return p
    },
    preventsubmit(e: any) {
      e.preventDefault()
    },
    submitNow(e: any) {
      if (this.validateForm()) {
        if (this.save != null) {
          this.save(this.item)
        }
      }
      return false
    },
  },
  data() {
    let errs: any = {}
    let user: any = []
    let showUser: Boolean = false
    return {
      errs,
      user,
      showUser,
      searchUser: searchUser,
    }
  }
})
</script>