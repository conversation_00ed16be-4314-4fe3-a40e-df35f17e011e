<template>
    <adminheader :title="$t('navigations.ipaytest')"></adminheader>
    <div>
        <form ref="form" method="POST" :action="paymentUrl">
            <div class="ipayform p-3">
                <div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <FormItemCompt
                                labelFor="MerchantCode"
                                :labelTitle="$t('ipaytest.MerchantCode')">
                                <input type="text" name="MerchantCode" v-model="item.MerchantCode" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.MerchantCode"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <FormItemCompt
                                labelFor="PaymentId"
                                :labelTitle="$t('ipaytest.PaymentId')">
                                <input type="text" name="PaymentId" v-model="item.PaymentId" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.PaymentId"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <FormItemCompt
                                labelFor="RefNo"
                                :labelTitle="$t('ipaytest.RefNo')">
                                <input type="text" name="RefNo" v-model="item.RefNo" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.RefNo"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <!-- UserName -->
                            <FormItemCompt
                                labelFor="UserName"
                                :labelTitle="$t('ipaytest.UserName')">
                                <input type="text" name="UserName" v-model="item.UserName" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.UserName"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <!-- UserEmail -->
                            <FormItemCompt
                                labelFor="UserEmail"
                                :labelTitle="$t('ipaytest.UserEmail')">
                                <input type="text" name="UserEmail" v-model="item.UserEmail" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.UserEmail"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                    <div class="w-1/3 inline-block">
                        <div class="ml-2">
                            <!-- UserContact -->
                            <FormItemCompt
                                labelFor="UserContact"
                                :labelTitle="$t('ipaytest.UserContact')">
                                <input type="text" name="UserContact" v-model="item.UserContact" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                                <ErrTextCompt :errs="errs && errs.UserContact"></ErrTextCompt>
                            </FormItemCompt>
                        </div>
                    </div>
                </div>


                <FormItemCompt
                    labelFor="Currency"
                    :labelTitle="$t('ipaytest.Currency')">
                    <input type="text" name="Currency" v-model="item.Currency" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.Currency"></ErrTextCompt>
                </FormItemCompt>

                <FormItemCompt
                    labelFor="Amount"
                    :labelTitle="$t('ipaytest.Amount')">
                    <input type="number" name="Amount" v-model="item.Amount" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.Amount"></ErrTextCompt>
                </FormItemCompt>

                <!-- ProdDesc -->
                <FormItemCompt
                    labelFor="ProdDesc"
                    :labelTitle="$t('ipaytest.ProdDesc')">
                    <input type="text" name="ProdDesc" v-model="item.ProdDesc" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.ProdDesc"></ErrTextCompt>
                </FormItemCompt>

                

                <!-- Remark -->
                <FormItemCompt
                    labelFor="Remark"
                    :labelTitle="$t('ipaytest.Remark')">
                    <textarea name="Remark" v-model="item.Remark" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" ></textarea>
                    <ErrTextCompt :errs="errs && errs.Remark"></ErrTextCompt>
                </FormItemCompt>

                <!-- Lang -->
                <FormItemCompt
                    labelFor="Lang"
                    :labelTitle="$t('ipaytest.Lang')">
                    <input type="text" name="Lang" v-model="item.Lang" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.Lang"></ErrTextCompt>
                </FormItemCompt>

                <!-- SignatureType -->
                <FormItemCompt
                    labelFor="SignatureType"
                    :labelTitle="$t('ipaytest.SignatureType')">
                    <input type="text" name="SignatureType" v-model="item.SignatureType" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.SignatureType"></ErrTextCompt>
                </FormItemCompt>

                <!-- Signature -->
                <FormItemCompt
                    labelFor="Test SHA256"
                    :labelTitle="$t('ipaytest.SHA256')">
                    <input type="text" name="tempStr" v-model="tempStr" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                </FormItemCompt>

                <div class="mb-5">{{sha256(tempStr)}}</div>
                <FormItemCompt
                    labelFor="Signature"
                    :labelTitle="$t('ipaytest.Signature')">
                    <input type="text" name="Signature" v-model="item.Signature" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.Signature"></ErrTextCompt>
                </FormItemCompt>

                <!-- ResponseURL -->
                <FormItemCompt
                    labelFor="ResponseURL"
                    :labelTitle="$t('ipaytest.ResponseURL')">
                    <input type="text" name="ResponseURL" v-model="item.ResponseURL" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.ResponseURL"></ErrTextCompt>
                </FormItemCompt>

                <!-- BackendURL -->
                <FormItemCompt
                    labelFor="BackendURL"
                    :labelTitle="$t('ipaytest.BackendURL')">
                    <input type="text" name="BackendURL" v-model="item.BackendURL" class="border-0 p-2 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full focus:ring-green-400" style="transition: all 0.15s ease 0s;" />
                    <ErrTextCompt :errs="errs && errs.BackendURL"></ErrTextCompt>
                </FormItemCompt>
                <div>
                    <div class="bg-gray-200 hover:bg-gray-500 hover:text-white cursor-pointer text-center py-2 rounded " @click="submitTest">{{$t('c.submit')}}</div>
                </div>
            </div>
        </form>
    </div>
</template>
<script lang="ts">
import adminheader from '@/components/AdminHeader.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { getIPay256Hash } from '../../../api'
import sha256 from 'js-sha256'

export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        return {
            token: computed(() => authStore.getState().token),
        }
    },
    components: {
        adminheader,
        FormItemCompt,
        ErrTextCompt,
    },
    methods : {
        submitTest () {
            //get hash first
            getIPay256Hash({token: this.token, item: this.item}).then((res: any) => {
                if (res.data) {
                    console.log(res.data)
                    console.log('submit Test')
                    this.item.Signature = res.data
                    setTimeout(() => {
                        console.log(this.item)
                        let refform: any = this.$refs.form
                        refform.submit()
                    }, 500)
                    
                } else {
                    console.log('get hash error')
                }                
            })
        },
        setUsername () {
            let p : any = this.profile
            this.item.UserName = p.id
            this.item.UserEmail = p.email
            this.item.UserContact = p.mobile || p.email
        },
        sha256 (str: string) {
            return sha256.sha256(str)
        },
    },
    computed: {
        profile () {
            return auth2Store.getState().profile
        },
    },
    mounted() {
        setTimeout(()=> {
            this.setUsername()
        }, 500)
    },
    data () {
        let errs: any = {}
        let item: any = {
            MerchantCode: 'M29131',
            PaymentId: '6',
            RefNo: '1',
            UserName: '',
            UserEmail: '',
            UserContact: '',
            Amount: '1.00',
            Currency: 'MYR',
            ProdDesc: 'Test Bill',
            Remark: 'Test Bill',
            Lang: 'UTF-8',
            SignatureType: 'SHA256',
            Signature: '',
            ResponseURL: 'https://hifi.codevision.my/en-US/auth/dev/ipaytest',
            BackendURL: 'https://hifiapi.codevision.my/api/payments/ipay',
        }
        let tempStr = ''
        let paymentUrl: string = 'https://payment.ipay88.com.my/ePayment/entry.asp'
        return {
            errs,
            paymentUrl,
            item,
            tempStr,
        }
    }
})
</script>
