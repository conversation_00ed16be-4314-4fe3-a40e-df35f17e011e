<template>
    <adminheader
        :title="$t('admin.devlog')"></adminheader>
    <div class="py-8 px-5 m-0 flex flex-wrap content-start items-start bg-white">
        <div class="markdownstyler" v-if="source&& source != ''">
            <Markdown :source="source" />
        </div>
    </div>
</template>
<script>
import { defineComponent } from 'vue'
import Markdown from 'vue3-markdown-it'
import adminheader from '@/components/AdminHeader.vue'
export default defineComponent({
    components: {
        Markdown,
        adminheader,
    },
    mounted () {
        fetch("/md/dev.md")
            .then((response) => {
                return response.text();
            })
            .then((text) => {
                this.source = text;
            });
    },
    data () {
        
        return {
            source: '',
        }
    }
})
</script>
