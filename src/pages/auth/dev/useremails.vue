<template>
    <div>
        <div class="m-2"><button class="w-full bg-gray-300 hover:bg-gray-400 text-white p-2 rounded" @click="loadAll()">LOAD ALL</button></div>
        <div>{{ userEmails }}</div>
        <!-- <div>{{datas}}</div> -->
    </div>
</template>
<script setup lang="ts">
import {ref, computed, inject } from 'vue'
import { getUsers } from '../../../api'
const datas: any = ref(undefined)
const userEmails = computed(() => {
    return datas.value?.data.map((user: any) => user.email)
})
const authStore: any = inject("authStore")
    const authState = authStore.getState()
const loadAll = (skip: number = 0, limit: number = 50) => {
    
    getUsers({ token: authState.token, limit, skip}).then((res: any) => {
        if (res.data) {
            if (datas.value == undefined) { datas.value = res }
            else {
                datas.value.data.push(...res.data)
            }
            // if still have more , load more
            if (datas.value.data.length < res.total) {
                loadAll(datas.value.data.length, limit)
            }
        }
    })
}
</script>