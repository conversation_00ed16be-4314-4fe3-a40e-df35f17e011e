<template>
    <adminheader :title="$t('navigations.paytest')"></adminheader>
    <div>
        <div class="p-2 ">Dummy Bills <div @click="createDummy" class="text-xs hover:bg-blue-300 hover:text-gray-800 cursor-pointer inline-block ml-10 rounded p-2 bg-blue-500 text-white">Create Dummy Bill</div></div>
        <div class="p-0">
            <div class="border-b px-5 pt-5">Dummy Bill list</div>
            <template v-if="dumpdata && dumpdata.data">
                <div :key="p.id" class="text-sm py-3 px-5" v-for="p in dumpdata.data">
                    {{p}}
                    <div class="cursor-pointer cursor-pointer hover:text-red-500" @click="paynow">Test Pay Now</div>
                </div>
            </template>
            <div v-else class="">
                Nothing here                
            </div>
            
        </div>
    </div>
</template>
<script lang="ts">
import adminheader from '@/components/AdminHeader.vue'
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { createBilling, getBillings } from '../../../api'
export default defineComponent({
    setup() {
        const authStore: any = inject("authStore")
        return {
            token: computed(() => authStore.getState().token),
        }
    },
    components: {
        adminheader,
    },
    computed: {
        userid () {
            return auth2Store.getState().profile && auth2Store.getState().profile.id
        }
    },
    mounted () {
        this.getDummies ()
    },
    data () {
        let dumpdata: any = undefined
        return {
            dumpdata
        }
    },
    methods: {
        paynow () {
            console.log('sss')
        },
        getDummies () {
            getBillings({
                customer: this.userid,
                remark: "dummy",
                params: ['customer', 'remark'],
                token: this.token,
            }).then((res: any) => {
                this.dumpdata = res
            })
        },
        createDummy() {
            if (confirm("Are you sure?")) {
                createBilling({
                    token: this.token,
                    form: {
                        user: this.userid,
                        customer: this.userid,
                        billdate: new Date().toISOString(),
                        duedate: new Date().toISOString(),
                        amountcurrent: 10,
                        taxcurrent: 0,
                        amountpaid: 0,
                        amountbf: 0,
                        totalamount: 10,
                        alreadycf: false,
                        status: true,
                        remark: 'testdummy',
                        items:[{
                            itemname: 'testitem',
                            amount: 10,
                            tax: 0,
                        }],
                    }
                }).then((req: any) => {
                    console.log(req)
                    this.getDummies()
                })
            }
        }
    }
})
</script>