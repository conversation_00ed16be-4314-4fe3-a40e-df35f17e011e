<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('billings.formTitle')"
      :btnYesText="$t('c.submit')"
      :btnYesFunction="submitNow"
      :btnNoText="$t('c.cancel')"
      :btnNoFunction="cancel"
      v-if="item">
    <div class="text-left">
      <button v-if="isDev" class="rounded bg-gray-300 px-2 py-1" @click="showLogs">Show Logs</button>
    </div>
    <div v-if="logs && logs.length > 0" class="py-2 rounded bg-white">
      <h2 class="text-2xl font-semibold mb-4">Billing Change Log</h2>
      <div class="space-y-6">
        <div
          v-for="log in logs"
          :key="log.id"
          class="bg-white shadow rounded-xl p-4 border border-gray-200"
        >
          <div class="flex justify-between items-center mb-2">
            <div>
              <p class="text-sm text-gray-500">
                <span class="font-medium">Change type:</span> {{ log.changetype }}
              </p>
              <p class="text-sm text-gray-500">
                <span class="font-medium">By user:</span> {{ log.userEmail }}
              </p>
            </div>
            <div class="text-xs text-gray-400">
              {{ formatDate(log.timestamp) }}
            </div>
          </div>

          <p class="text-gray-700 mb-2">{{ log.description }}</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 class="font-semibold text-gray-600 mb-1">Old Values</h3>
              <div class="bg-gray-50 rounded-lg p-2">
                <div v-for="(val, key) in log.oldvalues" :key="key">
                  <span class="font-medium">{{ key }}:</span>
                  <span>{{ stringify(val) }}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 class="font-semibold text-gray-600 mb-1">New Values</h3>
              <div class="bg-gray-50 rounded-lg p-2">
                <div v-for="(val, key) in log.newvalues" :key="key">
                  <span class="font-medium">{{ key }}:</span>
                  <span>{{ stringify(val) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-3 text-xs text-gray-400">
            <p><span class="font-medium">IP:</span> {{ log.ipaddress }}</p>
            <p><span class="font-medium">User Agent:</span> {{ log.useragent }}</p>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="logs && logs.length == 0" class="py-2 rounded bg-white">
      No logs available
    </div>
    <!-- <div @click="openPaymentPage" class="inline-block text-xs cursor-pointer float-right bg-gray-300 p-1 rounded hover:text-white hover:bg-gray-700">PayWith Subplace</div> -->
    <form v-if="refreshflag" @submit.prevent="preventsubmit" autocomplete="">
      <div class="flex justify-between">
          <div class="text-xs rounded inline-block bg-gray-100 px-2 py-1" v-if="item.id">{{item.id}}</div>
          <div class="inline-block rounded bg-gray-100 px-5 py-1 text-sm text-blue-600 font-bold" v-else>{{$t('c.new')}}</div>
          <div class="border px-2 py-1">
            <button @click.prevent="sync(item.id)"  class="mr-2 inline-block bg-gray-300 hover:bg-gray-700 hover:text-white text-xs p-1 rounded">{{syncLoading ? 'Loading...' : 'Sync' }}</button>
            <button @click.prevent="syncUpdate(item.id)"  class="inline-block bg-gray-300 hover:bg-gray-700 hover:text-white text-xs p-1 rounded">{{syncUpdateLoading ? 'Loading...' : 'Sync Update' }}</button>
          </div>
      </div>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="customer"
          :labelTitle="$t('billings.customer')">
          <UserAssignInput
              :token="token"
              :addUser="addCustomer"
              :removeUser="removeCustomer"
              :searchUser="searchCustomer"
              :users="customer"
              :readonly="false"
              :multiple="false"
              :plcHolder="$t('c.customerKeySearch')"
              :noUserText="$t('c.nocustomer')"
              :addText="$t('c.selectcustomer')"
              defaultColor="blue"></UserAssignInput>
      </FormItemCompt>
      <!-- <div>
        <FormItemCompt
              class="w-full md:w-1/3 inline-block px-1 align-top"
              labelFor="tax"
              :labelTitle="$t('billings.billno')"
              :required="true">
            <TextInput
                name="billno"
                type="text"
                v-model="item.billno"
                :placeholder="$t('c.frontPlc') + $t('billings.billno')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.billno"></ErrTextCompt>
          </FormItemCompt>
      </div> -->
      <div
          class="px-1"
          labelFor="subscriptions">
          <div v-if="showSubBtn">
            <div @click="loadSubscriptions" class="inline-block cursor-pointer hover:bg-gray-600 hover:text-white text-xs bg-gray-200 px-2 rounded py-1 m-1">{{$t('billings.loadSubscriptions')}}</div>
          </div>
          <div v-if="refreshflag2" class="block m-2" :key="sc.id" v-for="sc in subscription">
            <div v-if="sc.plan">{{$t('subscriptions.plan')}} : {{planinfo(sc.plan)}}</div>
            <div v-if="sc.voip">{{$t('subscriptions.voip')}} : {{planinfo(sc.voip)}}</div>
          </div>
      </div>
      <FormItemCompt
          class="w-full md:w-1/2 inline-block px-1"
          labelFor="billdate"
          :labelTitle="$t('billings.billdate')"
          :required="true">
        <DatePicker
            v-model="item.billdate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
        <ErrTextCompt :errs="errs && errs.billdate"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt
          class="w-full md:w-1/2 inline-block px-1"
          labelFor="duedate"
          :labelTitle="$t('billings.duedate')"
          :required="true">
          <div @click="amonthlater" class="float-right cursor-pointer hover:bg-gray-600 hover:text-white text-xs bg-gray-200 px-2 rounded py-1 m-1">{{$t('billings.amonthlater')}}</div>
        <DatePicker
            v-model="item.duedate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
        <ErrTextCompt :errs="errs && errs.duedate"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="items"
          :labelTitle="$t('billings.items')"
          :required="true">
        <div class="text-right">
          <div class="rounded inline-block text-xs px-2 py-1 text-white cursor-pointer"
              :class="billItem ? 'hover:bg-gray-400 bg-gray-600' : 'hover:bg-blue-400 bg-blue-600'"
              @click="billItem = Object.assign({}, billItemStyle)">{{$t('c.addnew')}}</div>
        </div>
        <dtable
            :columns="billColumns"
            :data="{ data: item.items }"
            columnColor="white">
          <template v-slot:action="slotProps">
              <button @click="editBill(slotProps.item, slotProps.index)" class="inline-block bg-blue-500 hover:bg-blue-700 text-white p-2 mr-2 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
              </button>
              <button @click="deleteBill(slotProps.item, slotProps.index)" class="inline-block bg-red-500 hover:bg-red-700 text-white p-2 mr-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
          </template>
        </dtable>
        <ErrTextCompt :errs="errs && errs.items"></ErrTextCompt>
        <div class="p-5" v-if="billItem">
          <FormItemCompt
              class="w-full inline-block px-1 align-top"
              labelFor="itemName"
              :labelTitle="$t('billings.itemName')"
              :required="true">
            <TextareaInput
                name="itemName"
                v-model="billItem.itemname"
                :placeholder="$t('c.frontPlc') + $t('billings.itemName')"
                defaultColor="blue"></TextareaInput>
            <ErrTextCompt :errs="errsBills && errsBills.itemname"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/3 inline-block px-1 align-top"
              labelFor="amount"
              :labelTitle="$t('billings.amount')"
              :required="true">
            <TextInput
                name="amount"
                type="number"
                v-model="billItem.amount"
                :placeholder="$t('c.frontPlc') + $t('billings.amount')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errsBills && errsBills.amount"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/3 inline-block px-1 align-top"
              labelFor="tax"
              :labelTitle="$t('billings.tax')"
              :required="true">
            <TextInput
                name="tax"
                type="number"
                v-model="billItem.tax"
                :placeholder="$t('c.frontPlc') + $t('billings.tax')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errsBills && errsBills.tax"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/3 inline-block px-1 align-top"
              labelFor="subtotal"
              :labelTitle="$t('billings.subtotal')">
            <div class="px-2 py-3 border rounded text-right">{{ (parseFloat(billItem.amount) + (billItem.tax * billItem.amount / 100)).toFixed(2) }}</div>
          </FormItemCompt>
          <div class="flex flex-col md:flex-row">
            <div class="rounded w-full w-1/2 mx-1 py-2 text-xs text-center hover:bg-gray-400 bg-gray-600 text-white cursor-pointer"
                @click="billItem = null">{{$t('c.cancel')}}</div>
            <div class="rounded w-full w-1/2 mx-1 py-2 text-xs text-center hover:bg-blue-400 bg-blue-600 text-white cursor-pointer"
                @click="submitBills">{{$t('c.submit')}}</div>
          </div>
        </div>
        <ErrTextCompt :errs="errs && errs.bills"></ErrTextCompt>
      </FormItemCompt>
      <div class="text-right">
        <button class="text-xs px-2 py-1 rounded bg-gray-300 hover:bg-gray-200" @click="calculateAmountCurrent">{{$t('c.calculate')}}</button>
      </div>
      <div>
        <FormItemCompt
            v-if="totalShow"
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="amountcurrent"
            :labelTitle="$t('billings.amountcurrent')"
            :required="true">
          <TextInput
              name="amountcurrent"
              type="number"
              v-model="item.amountcurrent"
              placeholder="0"
              :readonly="true"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.amountcurrent"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            v-if="totalShow"
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="taxcurrent"
            :labelTitle="$t('billings.taxcurrent')"
            :required="true">
          <TextInput
              name="taxcurrent"
              type="number"
              v-model="item.taxcurrent"
              placeholder="0"
              :readonly="true"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.taxcurrent"></ErrTextCompt>
        </FormItemCompt>
        
        <FormItemCompt
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="amountbf"
            :labelTitle="$t('billings.amountbf')"
            :required="true">
          <TextInput
              name="amountbf"
              type="number"
              v-model="item.amountbf"
              placeholder="0"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.amountbf"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt v-if="totalShow"
            class="w-full md:w-1/4 inline-block px-1"
            labelFor="totalamount"
            :labelTitle="$t('billings.totalamount')"
            :required="true">
          <TextInput
              name="totalamount"
              type="number"
              v-model="item.totalamount"
              placeholder="0"
              :readonly="true"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.totalamount"></ErrTextCompt>
        </FormItemCompt>        
        <div>
          <FormItemCompt
              class="w-full md:w-1/4 inline-block px-1"
              labelFor="amountpaid"
              :labelTitle="$t('billings.amountpaid')"
              :required="true">
            <TextInput
                name="amountpaid"
                type="number"
                v-model="item.amountpaid"
                placeholder="0"
                :readonly="true"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.amountpaid"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/4 inline-block px-1"
              labelFor="amountcf"
              :labelTitle="$t('billings.amountcf')"
              :required="true">
            <TextInput
                name="amountcf"
                type="number"
                v-model="item.amountcf"
                placeholder="0"
                defaultColor="blue"
                readonly></TextInput>
            <ErrTextCompt :errs="errs && errs.amountcf"></ErrTextCompt>
          </FormItemCompt>
          <FormItemCompt
              class="w-full md:w-1/4 inline-block text-right px-1"
              labelFor="alreadycf"
              labelTitle="">
            <Checkbox
                v-model="item.alreadycf"
                :label="$t('billings.alreadycf')"
                defaultColor="blue"></Checkbox>
          </FormItemCompt>
        </div>
      </div>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import UserAssignInput from '@/components/cvui/form/UserAssignInput.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import dtable from '@/components/cvui/table/index.vue'
import { getSubscriptions, searchUser, getUserName, getSubscription, getPlan, syncAutocount, syncUpdateAutocount, getBillingLogs } from '../../../api'
import moment from 'moment'
import { crossStore } from '@/store/cross-store'
export default defineComponent({
  props: {
    profile: {
      type: Object,
      required: true,
    },
    item: { type: Object, required: true },
    cancel: { type: Function, required: true },
    save: { type: Function, required: true },
    saveSilent: { type: Function, required: true },
    token: { type: String, required: true }
  },
  watch : {
    "item.amountbf" (p: any) {
      if (p) {
        this.totalShow = false
        this.item.totalamount = (this.item.amountcurrent || 0) + parseFloat(p)
        this.$nextTick(() => {
          this.totalShow = true
        })
      }
    },
  },
  components: {
    PopupModal,
    FormItemCompt,
    TextInput,
    DatePicker,
    UserAssignInput,
    SelectOne,
    Checkbox,
    ErrTextCompt,
    dtable,
    TextareaInput
  },
  mounted () {
    document.addEventListener('keydown', this.handleEscKey);
    setTimeout(() => {
      if (this.item && this.item.customer) {
        this.loadCust()
      }
      if (this.item && (this.item.subscriptions || this.item.voip)) {
        this.loadSubscriptions()
      }
    }, 500)
    if (this.item.id){
      setTimeout(this.calculateAmountCurrent, 1000)
    }    
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  methods: {
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {     
        if (this.cancel){
          this.cancel()
        }   
      }
    },
    async sync(id: string) {
      this.syncLoading = true

      try {
        const res = await syncAutocount({ token: this.token, id })

        if (res.ok === true) {
          crossStore.SetModalmsg({
            title: 'Success!',
            msg: 'Sync success.',
            type: 'success',
            proceedTxt: this.$t('c.okay')
          })
        } else {
          crossStore.SetModalmsg({
            title: 'Error!',
            msg: res?.error?.message || 'Sync not successful.',
            type: 'error',
            proceedTxt: this.$t('c.okay')
          })
        }
      } catch (error) {
        const errMsg =
        error?.response?.data?.error?.message ||
        error?.response?.data?.result?.error?.message ||
        error?.message ||
        'Something went wrong.'

        crossStore.SetModalmsg({
          title: 'Error!',
          msg: errMsg,
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      } finally {
        this.syncLoading = false
      }
    },
    async syncUpdate (id: string) {
      this.syncUpdateLoading = true

      try {
        const res = await syncUpdateAutocount({ token: this.token, id })

        if (res.ok === true) {
          crossStore.SetModalmsg({
            title: 'Success!',
            msg: 'Sync success.',
            type: 'success',
            proceedTxt: this.$t('c.okay')
          })
        } else {
          crossStore.SetModalmsg({
            title: 'Error!',
            msg: res?.error?.message || 'Sync not successful.',
            type: 'error',
            proceedTxt: this.$t('c.okay')
          })
        }
      } catch (error) {
        const errMsg =
        error?.response?.data?.error?.message ||
        error?.response?.data?.result?.error?.message ||
        error?.message ||
        'Something went wrong.'

        crossStore.SetModalmsg({
          title: 'Error!',
          msg: errMsg,
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      } finally {
        this.syncUpdateLoading = false
      }
    },
    async showLogs() {
      const res = await getBillingLogs({ token: this.token, id: this.item.id });
      this.logs = res.data;

      // Fetch emails for each log
      for (const log of this.logs) {
        if(log.user) {
          this.fetchUserEmailForLog(log);
        }
      }
    },
    async fetchUserEmailForLog(log: any) {
      const res = await getUserName({ token: this.token, id: log.user });
      // mutate the log to include email
      log.userEmail = res.email;
    },
    stringify (val: any) {
      if (typeof val === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(val)) {
        const date = new Date(val);
        if (!isNaN(date.getTime())) {
          return date.toLocaleString();
        }
      }

      if (typeof val === 'object') return JSON.stringify(val);
      return val;
    },
    formatDate (iso: string) {
      const date = new Date(iso);
      return date.toLocaleString();
    },
    openPaymentPage () {
      const p: any = (this.$router.resolve({name: 'upaylink', params: {id: this.item.id}}))
      window.open(p.fullPath)
    },
    planinfo(p: string) {
      let title: string = ''
      if (p) {
        let g: any = this.planlist && this.planlist.length > 0 && this.planlist.find((k: any) => k.id === p)
        if (g) {
          title = g.title
        }
        // if (this.planlist.length > 0 && this.planlist.find((px: any) => px.id === p)) {
        //   title = this.planlist.find((py: any) => py.id === p).title
        // }
      }
      return title
    },
    getPlan (p: string) {
      getPlan({id: p, token: this.token}).then((res: any) => {
        if (res.data) {
          this.planlist.push(res.data)
          this.refresh2()
        }
      })
    },
    loadCust () {
      this.customer = []
      if (this.item.customer && this.item.customer.trim().length > 0) {
        getUserName({token: this.token, id: this.item.customer}).then(res => {
          this.customer.push(res)
        })
      }     
    },
    loadSubscriptions () {
      this.subscription = []
      if (this.item.subscriptions && this.item.subscriptions.length > 0) {
        // loop subscriptions
        this.item.subscriptions.forEach((sid: any) => {
          getSubscription({token: this.token, id: sid}).then(res => {
            this.subscription.push(res.data)
            if (res.data && res.data.plan) {
              this.getPlan(res.data.plan)
            }
            if (res.data && res.data.voip) {
              this.getPlan(res.data.voip)
            }          
          })
        })
        
      }
    },
    amonthlater () {
      if (this.item.billdate) {
        this.item.duedate = moment(this.item.billdate).add(1, 'M')
        this.refresh()
      }
    },
    refresh () {
      this.refreshflag = false
      this.$nextTick(() => {
        this.refreshflag = true
      })
    },
    validateForm () {
      let p = true
      let requiredstring = ['billdate', 'customer', 'duedate']
      this.errs = {}
      for (let i = 0; i < requiredstring.length; i++) {
        if (!this.item[requiredstring[i]]) {
          this.errs[requiredstring[i]] = 'c.fieldRequired'
          p = false
        }
      }
      if (!this.item.items || this.item.items.length <= 0) {
        this.errs['items'] = 'billings.itemsRequired'
        p = false
      }
      let requiredNumber = ['amountcurrent', 'amountpaid', 'amountbf', 'amountcf', 'totalamount']
      for (let n = 0; n < requiredNumber.length; n++) {
        if (isNaN(this.item[requiredNumber[n]])) {
          this.item[requiredNumber[n]] = 0
        } else {
          this.item[requiredNumber[n]] = parseFloat(this.item[requiredNumber[n]])
        }
      }
      return p
    },
    preventsubmit (e: any) {
      e.preventDefault()
    },
    submitNow (e: any) {
      // e.preventDefault()
      if (this.validateForm()) {
        this.save(this.item)
        this.cancel()
      }
      return false
    },
    // start customer
    addCustomer (p: any) {
      this.customer = []
        if (!this.item.customer) {
            this.item.customer = ''
        }
        if (this.item.customer !== p.ID) {
            this.item.customer = p.ID
            this.customer.push(p)
        }
        this.showCustomer = true
    },
    searchCustomer (data: any) {
        return searchUser(Object.assign({params: ["customer"], "customer": true}, data))
    },
    removeCustomer (p: any) {
        if (this.item.customer === p.ID || this.item.customer === p.id) {
            this.item.customer = ''
            this.customer = []
        }
    },
    // end customer
    
    // start bills
    editBill (item: any, index: any) {
      if (!this.billItem) {
        this.errsBills = {}
        this.billItem = JSON.parse(JSON.stringify(Object.assign({id: index}, item)))
        for (let i = 0; i < Object.entries(this.billItemStyle).length; i++) {
          let data = Object.entries(this.billItemStyle)[i]
          if (Object.keys(this.billItem).indexOf(data[0]) === -1) {
            this.billItem[data[0]] = data[1]
          }
        }
      }
    },
    deleteBill (item: any, index: any) {
      this.item.items.splice(index, 1)
      this.calculateAmountCurrent()
    },
    validateBills () {
      let p = true
      if (!this.billItem.itemname || this.billItem.itemname.trim().length === 0) {
        this.errsBills['itemname'] = 'c.fieldRequired'
        p = false
      }
      if (Number.isNaN(this.billItem.amount)) {
        this.errsBills['amount'] = 'c.fieldRequired'
        p = false
      } else {
        this.billItem.amount = parseFloat(this.billItem.amount)
      }
      if (Number.isNaN(this.billItem.tax)) {
        this.errsBills['tax'] = 'c.fieldRequired'
        p = false
      } else {
        this.billItem.tax = parseFloat(this.billItem.tax)
      }
      return p
    },
    submitBills () {
      if (this.validateBills()) {
        let ind = this.item.items.findIndex((k: any) => k.name === this.billItem.itemname)
        if (ind < 0) {
          this.item.items.push(this.billItem)
        } else {
          this.item.items.splice(ind, 1, this.billItem)
        }
        this.billItem = null
      }
      this.calculateAmountCurrent()
    },
    // end bills
    calculateAmountCurrent () {
      this.totalShow = false
      this.item.amountcurrent = 0
      this.item.taxcurrent = 0
      
      for (let i = 0; i < this.item.items.length; i++) {
        let obj = this.item.items[i]
        this.item.amountcurrent += parseFloat(obj.amount)
        this.item.taxcurrent += (parseFloat(obj.amount) * parseFloat(obj.tax) / 100)
      }
      this.item.totalamount = parseFloat(this.item.amountcurrent) + parseFloat(this.item.taxcurrent) + parseFloat(this.item.amountbf || 0)
      this.$nextTick(()=> {
          this.totalShow = true
      })
    },
    refresh2 () {
      this.refreshflag2 = false
      this.$nextTick(() => {
        this.refreshflag2 = true
      })
    }
  },
  data () {
    let billItemStyle = {
      itemname: '',
      amount: 0,
      tax: 0
    }
    let errs: any = {}
    let customer: any = []
    let subscription: any = []
    let billItem: any = null
    let errsBills: any = {}
    let totalShow: boolean = true
    let showCustomer: boolean = false
    let refreshflag: boolean = true
    let planlist: any = []
    let refreshflag2: boolean = true
    let showSubBtn: boolean = false
    let logs: any = null
    return {
      errs,
      customer,
      subscription,
      billItemStyle,
      billItem,
      errsBills,
      totalShow,
      showCustomer,
      refreshflag,
      planlist,
      refreshflag2,
      showSubBtn,
      logs,
      syncLoading: false,
      syncUpdateLoading: false
    }
  },
  computed: {
    isDev (): boolean {
      return this.profile && this.profile.scopes && this.profile.scopes.indexOf('dev') > -1
    },
    billColumns () {
      return [
        { title: 'billings.itemName', key: 'itemname', type: 'string', class: 'text-center' },
        { title: 'billings.amount', key: 'amount', type: 'string', class: 'text-center' },
        { title: 'billings.tax', key: 'tax', type: 'string', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    }
  }
})
</script>
