<template>
  <div>
    <adminheader
        :title="$t('navigations.billings')"
        :addFunction="addBilling"
        :reloadFunction="reload"></adminheader>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div class="w-full flex items-center justify-between px-6">
      <div class="flex items-center">
        <button @click="confirmSync" v-if="checkboxlist.length > 0" class="px-2 py-1 bg-green-500 hover:bg-green-700 text-white rounded-md">
          {{ syncLoading ? `Syncing ${syncCount} of ${checkboxlist.length}` : 'Sync All Checked Bills'}}
        </button>
      </div>
      <div class="flex items-center">
        <select @change="loadDatabase" v-model="table.$syncac" class="px-2 py-1 border border-gray-300 rounded-md">
          <option value="">All</option>
          <option value=true>Synced</option>
          <option value=false>Not Synced</option>
        </select>
      </div>
    </div>
    <div v-if="databases == null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          :checkboxFunc="checkboxFunc"
          :checkboxlist="checkboxlist"
          columnColor="white">
        <template v-slot:action="slotProps">
          <!-- <button @click="editRow(slotProps.item, slotProps.index)" class="inline-block w-8 h-8 bg-blue-500 hover:bg-blue-700 text-white mr-2 rounded-full">
            <svgcollection icon="view" dclass="inline-block w-4 h-4 " />
          </button>  -->
          <button @click="editRow(slotProps.item, slotProps.index)" class="inline-block w-8 h-8 bg-red-500 hover:bg-red-700 text-white mr-2 rounded-full">
            <svgcollection icon="edit" dclass="inline-block w-4 h-4 " />
          </button>
            <!-- <button @click="deleteRow(slotProps.item, slotProps.index)" class="inline-block bg-red-500 hover:bg-red-700 text-white p-2 mr-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button> -->
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          :limitChange="limitChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :profile="profile"
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :save="saveFunc"
            :saveSilent="saveSilent"></dform>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { billingStore } from '../../../store/billing-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import Svgcollection from '../../../components/cvui/svgcollection.vue'
import { syncAutocount } from '@/api'
import Swal from 'sweetalert2'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    // const billingState = billingStore.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        billingStore: billingStore,
        billingState: billingStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    Svgcollection
  },
  mounted () {
    this.reload()
  },
  data () {
    let profile = auth2Store.getState().profile
    let item: any = undefined
    let deleteItem: any = undefined
    let itemStyle: any = {
      // customer: this.profile.customer ? this.profile.id : '',
      user: profile && profile.id,
      subscriptions: [],
      items: [],
      billdate: '',
      duedate: '',
      amountcurrent: 0,
      amountpaid: 0,
      amountbf: 0,
      amountcf: 0,
      totalamount: 0,
      alreadycf: false,
      status: true
    }
    let table : any = {
        limit: 10,
        page: 1,
        keywords: '',
        $syncac: ''
    }
    let keywords: string = ''
    return {
      item,
      deleteItem,
      itemStyle,
      table,
      keywords,
      checkboxlist: [],
      syncLoading: false,
      syncCount: 0
    }
  },
  methods: {
    searchNow () {
        this.table.page = 1
        this.table.keywords = this.keywords
        this.loadDatabase()
    },
    searchFunc (p: string) {
      // this.keywords = p
      this.table.billno = p
      this.searchNow()
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      this.billingStore.getBillings(p)
    },
    pageChange (p:any) {
      this.table.page = p
      this.loadDatabase()
    },
    limitChange (p:any) {
      this.table.limit = p
      this.loadDatabase()
    },
    checkboxFunc (type: any, sitem: any) {
      if (type === 'checkall') {
        if (this.checkboxlist.length === this.databases.data.length) {
          this.checkboxlist = []
        } else {
          this.checkboxlist = [...this.databases.data]
        }
      } else if (type === 'checkone') {
        let index = this.checkboxlist.findIndex((k: any) => k.id === sitem.id)
        if (index >= 0) {
          this.checkboxlist.splice(index, 1)
        } else {
          this.checkboxlist.push(sitem)
        }
      }
    },
    confirmSync () {
      Swal.fire({
        title: 'Are you sure you want to sync all selected?',
        text: 'Make sure to select non synced bill only.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, sync it!'
      }).then((result) => {
        if (result.isConfirmed) {
          this.syncAll()
        }
      })
    },
    async syncAll() {
      this.syncLoading = true
      let successCount = 0
      let errorCount = 0
      let errors = []

      // Show loading dialog with progress
      Swal.fire({
        title: 'Syncing Bills...',
        html: `
          <div>
            <p>Processing <span id="current">0</span> of ${this.checkboxlist.length} bills</p>
            <div style="margin: 20px 0;">
              <div style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                <div id="progress-bar" style="background: #007bff; height: 100%; width: 0%; transition: width 0.3s;"></div>
              </div>
            </div>
            <p id="status">Starting sync...</p>
          </div>
        `,
        showConfirmButton: false,
        allowOutsideClick: false,
        allowEscapeKey: false,
        didOpen: () => {
          Swal.showLoading()
        }
      })
      try {
        for (let i = 0; i < this.checkboxlist.length; i++) {
          const bill = this.checkboxlist[i]
          const id = bill.id
          
          // Update progress
          const currentElement = document.getElementById('current')
          const progressBar = document.getElementById('progress-bar')
          const statusElement = document.getElementById('status')
          
          if (currentElement) currentElement.textContent = i + 1
          if (progressBar) {
            const percentage = ((i + 1) / this.checkboxlist.length) * 100
            progressBar.style.width = `${percentage}%`
          }
          if (statusElement) statusElement.textContent = `Syncing bill ${id}...`
          
          try {
            const res = await syncAutocount({ token: this.token, id })
            if (res.ok === true) {
              successCount++
              this.syncCount++
            } else {
              errorCount++
              errors.push(`Bill ${id}: ${res?.error?.message || 'Sync not successful.'}`)
            }
          } catch (itemError) {
            errorCount++
            const errMsg = itemError?.response?.data?.error?.message ||
                          itemError?.response?.data?.result?.error?.message ||
                          itemError?.message ||
                          'Something went wrong.'
            errors.push(`Bill ${id}: ${errMsg}`)
          }
        }

        // Close loading dialog
        Swal.close()

        // Show final result
        if (successCount > 0 && errorCount === 0) {
          // All successful
          await Swal.fire({
            title: 'Success!',
            text: `Successfully synced ${successCount} bill${successCount > 1 ? 's' : ''}.`,
            icon: 'success',
            confirmButtonText: this.$t('c.okay')
          })
        } else if (successCount > 0 && errorCount > 0) {
          // Mixed results
          await Swal.fire({
            title: 'Partial Success',
            html: `
              <div>
                <p><strong>Success:</strong> ${successCount} bill${successCount > 1 ? 's' : ''}</p>
                <p><strong>Failed:</strong> ${errorCount} bill${errorCount > 1 ? 's' : ''}</p>
                <details style="margin-top: 10px;">
                  <summary>View errors</summary>
                  <div style="text-align: left; max-height: 200px; overflow-y: auto; margin-top: 5px;">
                    ${errors.map(err => `<p style="margin: 2px 0; font-size: 12px;">${err}</p>`).join('')}
                  </div>
                </details>
              </div>
            `,
            icon: 'warning',
            confirmButtonText: this.$t('c.okay')
          })
        } else {
          // All failed
          await Swal.fire({
            title: 'Error!',
            html: `
              <div>
                <p>All sync operations failed.</p>
                <details style="margin-top: 10px;">
                  <summary>View errors</summary>
                  <div style="text-align: left; max-height: 200px; overflow-y: auto; margin-top: 5px;">
                    ${errors.map(err => `<p style="margin: 2px 0; font-size: 12px;">${err}</p>`).join('')}
                  </div>
                </details>
              </div>
            `,
            icon: 'error',
            confirmButtonText: this.$t('c.okay')
          })
        }

      } catch (error) {
        // Close loading dialog in case of system error
        Swal.close()
        
        const errMsg = error?.response?.data?.error?.message ||
                      error?.response?.data?.result?.error?.message ||
                      error?.message ||
                      'Something went wrong.'

        await Swal.fire({
          title: 'Error!',
          text: errMsg,
          icon: 'error',
          confirmButtonText: this.$t('c.okay')
        })
      } finally {
        this.syncLoading = false
      }
    },
    initItem () {
      this.item = JSON.parse(JSON.stringify(this.itemStyle))
    },
    addBilling () {
      this.initItem()
    },
    cancelNow () {
      this.item = null
      // this.reload()
    },
    editRow (item: any, index: Number) {
      if (!this.item) {
        this.item = JSON.parse(JSON.stringify(Object.assign({id: item.ID}, item)))
        for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
          let data = Object.entries(this.itemStyle)[i]
          if (Object.keys(this.item).indexOf(data[0]) === -1) {
            this.item[data[0]] = data[1]
          }
        }
      }
    },
    duplicateRow (p:any, i:any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc (p:any) {
      if (p.id) {
        console.log(p)
        this.billingStore.updateBilling({ form: p, id: p.id, token: this.token })
      } else {
        this.billingStore.createBilling({ form: p, token: this.token })
      }
    },
    saveSilent (p:any) {
      this.billingStore.updateBilling({ form: p, id: p.id, token: this.token })
    },
    deleteRow (p:any,i:any) {
      this.deleteItem = p
      crossStore.SetModalmsg({
        title: this.$t('c.deleteTitle'),
        msg: this.$t('c.confirmDelete') + ' ' + p.billingid + '?',
        proceedTxt:  this.$t('c.okay'),
        proceedFunc: () => { this.deleteNow(p.id)},
      })
    },
    deleteNow(id: any) {
      crossStore.SetModalmsg(null)
      billingStore.deleteBilling({ token: this.token, id })
    }
  },
  computed: {
    columns () {
      return [
        { title: '', key: 'id', type: 'checkbox', checkall: true, class: 'text-center' },
        { title: 'billings.billingid', key: 'billno', type: 'string', class: 'text-center' },
        { title: 'billings.total', key: 'totalamount', type: 'price', class: 'text-center' },
        { title: 'billings.billdate', key: 'billdate', type: 'date', class: 'text-center' },
        { title: 'billings.billdate', key: 'billdate', type: 'string', class: 'text-center' },
        { title: 'billings.duedate', key: 'duedate', type: 'date', class: 'text-center' },
        { title: 'billings.synced', key: 'acsync', type: 'boolean', class: 'text-center' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return billingStore.getState().billings
    },
    // billingCreateByAdmin () {
    //   return billingStore.getState().billingCreateByAdmin
    // },
    // billingCreateByAdminSuccess () {
    //   return billingStore.getState().billingCreateByAdminSuccess
    // },
    // billingCreateByAdminError () {
    //   return billingStore.getState().billingCreateByAdminError
    // },
    billingUpdate () {
      return billingStore.getState().billingUpdate
    },
    billingUpdateSuccess () {
      return billingStore.getState().billingUpdateSuccess
    },
    billingUpdateError () {
      return billingStore.getState().billingUpdateError
    },
    billingDeleteSuccess () {
      return billingStore.getState().billingDeleteSuccess
    },
  },
  watch: {
    // billingCreateByAdminSuccess (p) {
    //   if (p) {
    //     this.item = null
    //     crossStore.SetNotmsg({
    //       title: this.$t('c.createTitle') + this.$t('billings.billing'),
    //       msg: this.$t('c.createSuccess'),
    //       type: 'success'
    //     })
    //   }
    // },
    // billingCreateByAdminError (p) {
    //   if (p) {
    //     crossStore.SetModalmsg({
    //       title: this.$t('c.createTitle') + this.$t('billings.billing'),
    //       msg: this.$t('c.createError'),
    //       type: 'error',
    //       proceedTxt: this.$t('c.okay')
    //     })
    //   }
    // },
    billingUpdateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('billings.billing'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    billingUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('billings.billing'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    billingDeleteSuccess (p) {
      if (p) {
        this.deleteItem = null
      }
    }
  },
})
</script>
