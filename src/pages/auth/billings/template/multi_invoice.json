{"basePdf": "data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PAovRmlsdGVyIC9GbGF0ZURlY29kZQovTGVuZ3RoIDM4Cj4+CnN0cmVhbQp4nCvkMlAwUDC1NNUzMVGwMDHUszRSKErlCtfiyuMK5AIAXQ8GCgplbmRzdHJlYW0KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL01lZGlhQm94IFswIDAgNTk1LjQ0IDg0MS45Ml0KL1Jlc291cmNlcyA8PAo+PgovQ29udGVudHMgNSAwIFIKL1BhcmVudCAyIDAgUgo+PgplbmRvYmoKMiAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzQgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjMgMCBvYmoKPDwKL3RyYXBwZWQgKGZhbHNlKQovQ3JlYXRvciAoU2VyaWYgQWZmaW5pdHkgRGVzaWduZXIgMS4xMC40KQovVGl0bGUgKFVudGl0bGVkLnBkZikKL0NyZWF0aW9uRGF0ZSAoRDoyMDIyMDEwNjE0MDg1OCswOScwMCcpCi9Qcm9kdWNlciAoaUxvdmVQREYpCi9Nb2REYXRlIChEOjIwMjIwMTA2MDUwOTA5WikKPj4KZW5kb2JqCjYgMCBvYmoKPDwKL1NpemUgNwovUm9vdCAxIDAgUgovSW5mbyAzIDAgUgovSUQgWzwyODhCM0VENTAyOEU0MDcyNERBNzNCOUE0Nzk4OUEwQT4gPEY1RkJGNjg4NkVERDZBQUNBNDRCNEZDRjBBRDUxRDlDPl0KL1R5cGUgL1hSZWYKL1cgWzEgMiAyXQovRmlsdGVyIC9GbGF0ZURlY29kZQovSW5kZXggWzAgN10KL0xlbmd0aCAzNgo+PgpzdHJlYW0KeJxjYGD4/5+RUZmBgZHhFZBgDAGxakAEP5BgEmFgAABlRwQJCmVuZHN0cmVhbQplbmRvYmoKc3RhcnR4cmVmCjUzMgolJUVPRgo=", "schemas": [{"logo": {"type": "image", "position": {"x": 10.12, "y": 5.14}, "width": 50, "height": 24, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1}, "empty0": {"type": "text", "position": {"x": 10.12, "y": 27.14}, "width": 190, "height": 0.1, "alignment": "center", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#E5E4E2"}, "companyName": {"type": "text", "position": {"x": 8, "y": 29.84}, "width": 79.45, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontName": "GothamBold"}, "companyAddress": {"type": "text", "position": {"x": 8, "y": 35.24}, "width": 96.64, "height": 11.76, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1, "fontName": "Gotham"}, "itemTitle": {"type": "text", "position": {"x": 8, "y": 46.94}, "width": 57.22, "height": 10.97, "alignment": "left", "fontSize": 24, "characterSpacing": 0, "lineHeight": 1, "fontName": "GothamBold", "fontColor": "#642469"}, "customerNameWtTitle": {"type": "text", "position": {"x": 8, "y": 58}, "width": 89, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontName": "EffraBold"}, "user": {"type": "text", "position": {"x": 8, "y": 65}, "width": 89, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontName": "<PERSON><PERSON><PERSON>"}, "customerAddress": {"type": "text", "position": {"x": 8, "y": 72}, "width": 89, "height": 8.32, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1}, "usercontact": {"type": "text", "position": {"x": 8, "y": 81}, "width": 35, "height": 7, "alignment": "left", "fontSize": 9, "characterSpacing": 0, "lineHeight": 1}, "empty": {"type": "text", "position": {"x": 144, "y": 28.17}, "width": 61.99, "height": 17.85, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#642469"}, "totalLabel": {"type": "text", "position": {"x": 144, "y": 29.75}, "width": 62, "height": 7, "alignment": "center", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#ffffff", "fontName": "EffraBold", "backgroundColor": "#642469"}, "total": {"type": "text", "position": {"x": 144, "y": 35.63}, "width": 62, "height": 7, "alignment": "center", "fontSize": 20, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#ffffff", "fontName": "EffraBold", "backgroundColor": "#642469"}, "summaryTitle": {"type": "text", "position": {"x": 6.96, "y": 75.51}, "width": 85, "height": 7, "alignment": "left", "fontSize": 10, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469", "fontName": "EffraBold"}, "empty2": {"type": "text", "position": {"x": 6.5, "y": 83.02}, "width": 200, "height": 9.11, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a"}, "itemsLabel": {"type": "text", "position": {"x": 12.85, "y": 85}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "amountLabel": {"type": "text", "position": {"x": 165, "y": 85}, "width": 35, "height": 7, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "items": {"type": "text", "position": {"x": 9.79, "y": 93.74}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "amounts": {"type": "text", "position": {"x": 165, "y": 93.69}, "width": 37.38, "height": 48, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "taxLabel": {"type": "text", "position": {"x": 140, "y": 158}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "tax": {"type": "text", "position": {"x": 175.5, "y": 158}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "bfLabel": {"type": "text", "position": {"x": 140, "y": 163.5}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "bf": {"type": "text", "position": {"x": 175.5, "y": 163.5}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "totalLabel2": {"type": "text", "position": {"x": 140.74, "y": 170.43}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}, "total2": {"type": "text", "position": {"x": 175.04, "y": 170.43}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}}]}