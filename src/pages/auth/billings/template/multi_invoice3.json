{"basePdf": "data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PAovRmlsdGVyIC9GbGF0ZURlY29kZQovTGVuZ3RoIDM4Cj4+CnN0cmVhbQp4nCvkMlAwUDC1NNUzMVGwMDHUszRSKErlCtfiyuMK5AIAXQ8GCgplbmRzdHJlYW0KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL01lZGlhQm94IFswIDAgNTk1LjQ0IDg0MS45Ml0KL1Jlc291cmNlcyA8PAo+PgovQ29udGVudHMgNSAwIFIKL1BhcmVudCAyIDAgUgo+PgplbmRvYmoKMiAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzQgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjMgMCBvYmoKPDwKL3RyYXBwZWQgKGZhbHNlKQovQ3JlYXRvciAoU2VyaWYgQWZmaW5pdHkgRGVzaWduZXIgMS4xMC40KQovVGl0bGUgKFVudGl0bGVkLnBkZikKL0NyZWF0aW9uRGF0ZSAoRDoyMDIyMDEwNjE0MDg1OCswOScwMCcpCi9Qcm9kdWNlciAoaUxvdmVQREYpCi9Nb2REYXRlIChEOjIwMjIwMTA2MDUwOTA5WikKPj4KZW5kb2JqCjYgMCBvYmoKPDwKL1NpemUgNwovUm9vdCAxIDAgUgovSW5mbyAzIDAgUgovSUQgWzwyODhCM0VENTAyOEU0MDcyNERBNzNCOUE0Nzk4OUEwQT4gPEY1RkJGNjg4NkVERDZBQUNBNDRCNEZDRjBBRDUxRDlDPl0KL1R5cGUgL1hSZWYKL1cgWzEgMiAyXQovRmlsdGVyIC9GbGF0ZURlY29kZQovSW5kZXggWzAgN10KL0xlbmd0aCAzNgo+PgpzdHJlYW0KeJxjYGD4/5+RUZmBgZHhFZBgDAGxakAEP5BgEmFgAABlRwQJCmVuZHN0cmVhbQplbmRvYmoKc3RhcnR4cmVmCjUzMgolJUVPRgo=", "schemas": [{"logo": {"type": "image", "position": {"x": 10.12, "y": 5.14}, "width": 50, "height": 24, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1}, "empty0": {"type": "text", "position": {"x": 10.12, "y": 27.14}, "width": 190, "height": 0.1, "alignment": "center", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#E5E4E2"}, "empty2": {"type": "text", "position": {"x": 6.5, "y": 28}, "width": 200, "height": 9.11, "alignment": "left", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a"}, "itemsLabel": {"type": "text", "position": {"x": 12.85, "y": 30}, "width": 35, "height": 7, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "amountLabel": {"type": "text", "position": {"x": 165, "y": 30}, "width": 35, "height": 7, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#f18a6a", "fontName": "EffraBold"}, "items": {"type": "text", "position": {"x": 9.79, "y": 39}, "width": 99.56, "height": 48, "alignment": "left", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "amounts": {"type": "text", "position": {"x": 165, "y": 37}, "width": 37.38, "height": 48, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1.5}, "emptyLine": {"type": "text", "position": {"x": 10.12, "y": 250.14}, "width": 190, "height": 0.1, "alignment": "center", "fontSize": 13, "characterSpacing": 0, "lineHeight": 1, "backgroundColor": "#E5E4E2"}, "taxLabel": {"type": "text", "position": {"x": 140, "y": 258}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "tax": {"type": "text", "position": {"x": 175.5, "y": 258}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "bfLabel": {"type": "text", "position": {"x": 140, "y": 263.5}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "bf": {"type": "text", "position": {"x": 175.5, "y": 263.5}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1}, "totalLabel2": {"type": "text", "position": {"x": 140.74, "y": 270.43}, "width": 35, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}, "total2": {"type": "text", "position": {"x": 175.04, "y": 270.43}, "width": 23.09, "height": 5, "alignment": "right", "fontSize": 11, "characterSpacing": 0, "lineHeight": 1, "fontColor": "#642469"}}]}