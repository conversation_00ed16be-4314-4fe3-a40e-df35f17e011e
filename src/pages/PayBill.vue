<template>
    <div class="text-center" v-if="plan === undefined || subscription === undefined || plan === undefined">
        <loading />
    </div>
    <div class="flex items-center justify-center mt-20 font-sans" v-else>
        <div class="rounded shadow-xl w-2/3">
            <div class="flex flex-col pt-12">
                <div class="p-4" style="background-color: #f28669;">
                    <h1 class="text-4xl font-bold">YOUR BILL IS READY</h1>
                </div>
                <div class="grid grid-cols-2 font-bold">
                    <div class="p-4">
                        <p class="uppercase">HI, {{ subscription.title }} {{ subscription.name }}</p>
                        <p class="mt-4">Here's a summary of your {{ plan.title }}</p>
                        <p class="mt-4">Please pay before {{ formatdate(bill.duedate) }} to avoid any service interruption.</p>
                        <p class="mt-4">Thank you if you have already made your payment.</p>
                        <p class="mt-4">Have a great day.</p>
                        <p class="mt-4" style="color: #632568;;">HIGHFI</p>
                    </div>
                    <div class="text-white text-right p-4" style="background-color: #632568;">
                        <p>Bill Date: {{ formatdate(bill.billdate) }}</p>
                        <p>Bill Due: {{ formatdate(bill.duedate) }}</p>
                        <p class="mt-3 text-2xl">Account: {{ subscription.sid || subscription.id }}</p>
                        <p>Remaining Balance:RM {{ formatMoney(bill.amountbf) }}</p>
                        <p>Current Charges:RM {{ formatMoney(bill.amountcurrent) }}</p>
                        <p class="mt-3">Total Amount Due</p>
                        <p>RM {{ formatMoney(bill.totalamount) }}</p>
                        <a :href="'https://paymentapi.highfi.com.my/api/payex/' + bill.id" >
                            <button class="mt-3 px-1 py-2 text-black font-bold" style="background-color: #f28669;">PAY NOW</button>                        
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import { useRoute } from 'vue-router';
import { getBilling, getPlan, getSubscription } from '../api/index'
import loading from '../components/cvui/loading.vue';
import moment from 'moment';

export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const route = useRoute()
    return {
        token: computed(() => authState.token),
        route: route,
        
    }
  },
  data() {
    let bill: undefined
    let subscription: undefined
    let plan: undefined
    return {
        bill,
        subscription,
        plan
    }
  },
  components: {
    loading,
  },
  computed: {
    id () {
        return this.$route.params.id
    }
  },
  methods: {
    formatdate (p: any) {
        return moment(p).format('DD MMMM YYYY')
    },
    formatMoney (p: any) {
        if (Number.isNaN(p)) {
            return '0.00'
        } else {
            return p.toFixed(2)
        }
    },
    async getBillById() {
        await getBilling({id: this.id}).then((res: any) => {                        
            this.bill = res.data
            this.getSubscriptionById(this.bill.subscriptions[0])      
        }).catch((err: any) => {
            console.error(err);   
        })
    },
    async getSubscriptionById(susbcriptionId:any) {
        await getSubscription({token: this.token, id: susbcriptionId}).then((res: any) => {                        
            this.subscription = res.data
            this.getPlanById(this.subscription.plan)
        }).catch((err: any) => {
            console.error(err);   
        })
    },
    async getPlanById(planId:any) {
        await getPlan({token: this.token, id: planId}).then((res: any) => {                        
            this.plan = res.data  
        }).catch((err: any) => {
            console.error(err);   
        })
    },
  },
  mounted() { 
    this.getBillById()  
  }
})
</script>