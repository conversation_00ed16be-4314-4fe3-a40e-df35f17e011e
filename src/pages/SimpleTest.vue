<template>
  <div class="simple-test">
    <h1>Simple Test Page</h1>
    <p>This is a very simple test page with no dependencies.</p>
    <button @click="count++">Count: {{ count }}</button>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      count: 0
    }
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin: 20px;
  color: #333;
}
h1 {
  color: #333;
}
button {
  background-color: #4CAF50;
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 10px 0;
  cursor: pointer;
  border-radius: 4px;
}
</style>
