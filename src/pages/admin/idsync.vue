<template>
    <div>
        <adminheader
            :title="$t('navigations.idsync')"></adminheader>
        <div class="py-10">
            <div @click="generateAllUser" class="text-xs hover:bg-blue-300 hover:text-gray-800 cursor-pointer inline-block ml-10 rounded p-2 bg-blue-500 text-white">Generate All Active User</div>
            <div @click="checkSync" class="text-xs hover:bg-blue-300 hover:text-gray-800 cursor-pointer inline-block ml-10 rounded p-2 bg-blue-500 text-white" v-if="lists" >Check Sync</div>
            <div @click="syncAll" class="text-xs hover:bg-blue-300 hover:text-gray-800 cursor-pointer inline-block ml-10 rounded p-2 bg-blue-500 text-white" v-if="lists">Sync All Needed</div>
        </div>
        <div class="px-2 bg-white">
            <div class="grid bg-gray-600 text-white grid-cols-6 border-b gap-3">
                <div class="w-5">#</div>
                <div class="text-center">Name</div>
                <div class="text-center">Username</div>
                <div class="text-center">PlanID</div>
                <div class="text-center">Checking Status</div>
                <div class="text-center">Sync Status</div>
            </div>
            <div class="grid grid-cols-6 border-b gap-3 hover:bg-blue-200 cursor-pointer py-1" v-for="(p,pi) in lists">
                <div class="w-5">{{pi+1}}</div>
                <div class="text-center">{{p.name}}</div>
                <div class="text-center">{{p.username}}</div>
                <div class="text-center">{{p.plan && plans[p.plan] && plans[p.plan].title}}</div>
                <div class="text-center">
                    {{ checksyncStatus[p.id] ===  true ? 'Need to Sync' : (checksyncStatus[p.id] ===  false ? 'Existed' : '---') }}
                </div>
                <div class="text-center">
                    {{ syncStatus[p.id] ===  true ? 'Synced' : (syncStatus[p.id] ===  false ? 'Failed to Sync' : '---') }}
                </div>
            </div>
        </div>    
    </div>
</template>
<script setup lang="ts">
import {ref, inject} from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import { getSubscriptions, getPlan, getDMAUserCheck, createDMAUser } from '../../api'
const authStore: any = inject("authStore")
const authState = authStore.getState()
const lists: any = ref([])
const plans: any = ref({})
const checksyncStatus: any = ref({})
const syncStatus: any = ref({})
const syncAll: any = () => {
    syncStatus.value = {}
    for (let i = 0; i < lists.value.length; i++) {
        if (checksyncStatus.value[lists.value[i].id] === true) {
            // do sync
            doSyncNow(lists.value[i].id)
        }
    }
}

const doSyncNow = (id: string) => {
    createDMAUser({token: authState.token, id}).then((res: any) => {
        if (res.data.affectedRows == 1) {
            syncStatus.value[id] = true
        } else {
            syncStatus.value[id] = false
        }
    }).catch((err: any) => {
        syncStatus.value[id] = false
    })
}

const checkUsername = (username: string, id: string) => {
    getDMAUserCheck({
        token: authState.token, username
    }).then((res: any) => {
        console.log(res.data)
        checksyncStatus.value[id] = true
    }).catch((err: any) => {
        checksyncStatus.value[id] = false
    })
}

const checkSync = () => {
    checksyncStatus.value = {}
    // loop lists
    for (let i = 0; i < lists.value.length; i++) {
        checkUsername(lists.value[i].username, lists.value[i].id)
    }
}

const generateAllUser = () => {
    lists.value = []
    doGetReport()
}

const getPlanFunc = (p: any) => {
    getPlan({
        token: authState.token, id: p
    }).then((res: any) => {
        plans.value[p] = res.data
    })
}

const checkPlans = (l: any) => {
    l.forEach((p: any) => {
        if (!plans.value[p.plan]) {
            getPlanFunc(p.plan)
        }
    })
}

const doGetReport = () => {
    let limit: number = 30
    getSubscriptions({
        token: authState.token, skip: lists.value.length, limit, statustxt: 'active'
    }).then((res: any) => {
        lists.value = lists.value.concat(res.data)
        checkPlans(res.data)
        if (res && res.total > lists.value.length) {
            doGetReport()
        }        
    })
}
</script>