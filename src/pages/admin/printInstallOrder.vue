<template>
  <div class="mb-10">
    <div>
        <div @click="printOrder" class="text-center p-3 cursor-pointer bg-blue-300 hover:text-white hover:bg-blue-500 rounded m-2">
            {{$t('c.print')}}
        </div>
    </div>
    <div v-if="item && plan" class="myDivToPrint m-10">
        <div class="font-bold uppercase text-xl">{{$t('installationorders.order')}}</div>
        <div class="mt-5 border-b">
          <div class="font-bold">{{$t('installationorders.address')}}</div>
          <div>{{item.address.address || '---'}}</div>
      </div>
        <div class="mt-5 border-b">
          <div class="font-bold">{{$t('installationorders.title')}}</div>
          <div>{{plan.title || '---'}}</div>
        </div>
        <div class="mt-5 border-b inline-block w-full md:w-1/4">
          <div class="font-bold">{{$t('installationorders.contact')}}</div>
          <div>{{item.contact || '---'}}</div>
        </div>
        <div class="mt-5 border-b inline-block w-full md:w-1/4">
          <div class="font-bold">{{$t('installationorders.contact')}}</div>
          <div>{{item.contact2 || '---'}}</div>
        </div>
        <div class="mt-5 border-b inline-block w-full md:w-1/2">
          <div class="font-bold">{{$t('installationorders.email')}}</div>
          <div>{{item.email || '---'}}</div>
        </div>
        <div class="mt-5 border-b inline-block w-full md:w-1/2">
          <div class="font-bold">{{$t('installationorders.username')}}</div>
          <div>{{item.username || '---'}}</div>
        </div>
        <div class="mt-5 border-b inline-block w-full md:w-1/2">
          <div class="font-bold">{{$t('installationorders.userpassword')}}</div>
          <div>{{item.userpassword || '---'}}</div>
        </div>
        <template v-if="item.voip">
          <div class="mt-5 border-b inline-block w-full md:w-1/2">
            <div class="font-bold">{{$t('installationorders.voipusername')}}</div>
            <div>{{item.voipusername || '---'}}</div>
          </div>
          <div class="mt-5 border-b inline-block w-full md:w-1/2">
            <div class="font-bold">{{$t('installationorders.voipuserpassword')}}</div>
            <div>{{item.voipuserpassword || '---'}}</div>
          </div>
        </template>
        <div>
          <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.port')}}</div>
            <div class=" border-b">{{item.port || '---'}}</div>
          </div>
          <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.splitter')}}</div>
            <div class="border-b ">{{item.splitter || '---'}}</div>
          </div>
          <!-- <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.subscribeDate')}}</div>
            <div class="border-b ">{{ formatdate(item.subscribedate) || '---'}}</div>
          </div> -->
          <!-- <div class="mt-5 border-b inline-block w-full md:w-1/3">
            <div class="font-bold">{{$t('subscriptions.circuit')}}</div>
            <div>{{item.circuit || '---'}}</div>
          </div> -->
        </div>

        <div>
          <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.serial')}}</div>
            <div class="border-b ">{{item.serial || '---'}}</div>
          </div>
          <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.gponsn')}}</div>
            <div class="border-b ">{{item.gponsn || '---'}}</div>
          </div>
          <!-- <div class="mt-5 inline-block w-full md:w-1/3 pr-5">
            <div class="font-bold">{{$t('subscriptions.createDate')}}</div>
            <div class="border-b ">{{ formatdate(item.created_at) || '---'}}</div>
          </div> -->
        </div>

        <!-- <template v-if="item.equipments && item.equipments.length > 0">
          <div class="mt-5">
            <div class="font-bold">{{$t('subscriptions.equipments')}}</div>
            <div class="font-bold  text-xs border-b-2 border-purple-500 w-full">
              <div class="w-1/6 inline-block">#</div>
              <div class="w-2/6 inline-block">{{$t('subscriptions.name')}}</div>
              <div class="w-3/6 inline-block">{{$t('subscriptions.serial')}}</div>
            </div>

            <div class="pb-1 border-b mb-1" :key="String(ei) + '_ep'" v-for="(ep, ei) in item.equipments">
              <div class="w-1/6 inline-block">{{String(Number(ei) + 1)}}</div>
              <div class="w-2/6 inline-block">{{ep.name}}</div>
              <div class="w-3/6 inline-block">{{ep.serial}}</div>
            </div>
          </div>
        </template> -->
        
        <div class="mt-28 w-full text-sm">
          <!-- <div class="inline-block w-full md:w-1/2 mt-28">
            <div class="w-60 border-b-2 border-black" />
            <div>{{filterUser(item.agent) && filterUser(item.agent).value}}</div>
            <div>{{$t('installationorders.agent')}}</div>
            <div>{{$t('installationorders.date') + ':'}}</div>
          </div> -->
          <div class="inline-block w-1/3">
            <div class="w-full border-b-2 border-black" />
            <div>
              <div class="uppercase">{{$t('installationorders.customer') + ': '}}</div>
              <div>{{filterUser(item.customer) && filterUser(item.customer).value}}</div>
            </div>
            <div>{{$t('installationorders.date') + ':'}}</div>
          </div>
          <div class="inline-block w-1/3">&nbsp;</div>
          <div class="inline-block w-1/3">
            <div class="w-full border-b-2 border-black inline-block" />
            <div>
              <div class="uppercase">{{$t('installationorders.installer') + " :"}}</div>
              <div><br/></div>
              <!-- <div>{{filterUser(item.agent) && filterUser(item.agent).value}}</div> -->
            </div>
            <div>
              {{$t('installationorders.date')}} :
            </div>
          </div>
          <!-- <div class="inline-block w-full md:w-1/2 mt-28 text-left md:text-right">
            <div class="w-60 border-b-2 border-black inline-block" />
            <div>{{filterUser(item.customer) && filterUser(item.customer).value}}</div>
            <div>{{$t('installationorders.customer')}}</div>
            <div>
              <span class="hidden md:inline">:</span>
              {{$t('installationorders.date')}}
              <span class="inline md:hidden">:</span>
            </div>
          </div> -->
        </div>
    </div>
    <div class="text-gray-300 text-xs text-center">NOT SUPPORTING IE</div>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { getSubscription, getPlan } from '../../api/index'
import { getUserName } from '../../api'
import moment from 'moment'
export default defineComponent({
    setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()

    return {
        token: computed(() => authState.token),
    }
  },
  mounted () {
      if (this.$route.params.id) {
          this.getOrder(this.$route.params.id)
      }
  },
  data () {
      let item: any = undefined
      let plan: any = undefined
      let userlist: any = []
      return {
          item,
          plan,
          userlist,
          moment
      }
  },
  watch: {
      '$route.params.id' (val) {
          if (val) {
              this.getOrder(val)
          }
      }
  },
  methods: {
    formatdate (p: any) {
        return moment(p).format('DD-MM-YYYY')
    },
      getOrder (p: any) {
          getSubscription({token: this.token, id: p}).then((rs: any) => {
              if (rs && rs.data) {
                  this.item = rs.data
                  if (this.item.plan) {
                      getPlan({token: this.token, id: this.item.plan}).then((rs2: any) => {
                          if (rs2 && rs2.data) {
                              this.plan = rs2.data
                          }
                      })
                      let userl = ['customer', 'agent']
                      for (let i = 0; i < userl.length; i++) {
                        getUserName({ token: this.token, id: this.item[userl[i]] }).then((rs3: any) => {
                          this.userlist.push({
                            id: this.item[userl[i]],
                            value: rs3.name || rs3.email || ' (' + this.$t('c.noNameNoEmail') + ')'
                          })
                        })
                      }
                  }
              }
          })
      },
      printOrder () {
          window.print()
      },
      momentDate (p: any) {
        return moment(p).format('DD/MM/YYYY')
      },
      filterUser (p: any) {
        return this.userlist.filter((k: any) => k.id === p)[0]
      }
  }
})
</script>
<style scoped>
@media print {
    .myDivToPrint {
        background-color: white;
        height: 100%;
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        margin: 0;
        padding: 15px;
        font-size: 14px;
        line-height: 18px;
    }
}
</style>
