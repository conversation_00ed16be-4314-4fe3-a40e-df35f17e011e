<template>
    <div>
        <adminheader
        :title="$t('navigations.admin')"></adminheader>
        <div class="bg-white rounded-md m-2 p-4">
            <h1 class="border-b mb-2">Bulk Payment Update</h1>
            
            <div class="grid grid-cols-5 gap-4">
                <div class="col-span-2 p-2">
                    Number of Rows
                </div>
                <div class="col-span-2">
                    <input type="number" v-model="rownum" class="rounded border w-full p-2"/>
                </div>
                <div class="col-span-1">
                    &nbsp;
                </div>
                <div class="col-span-5">
                    <textarea v-model="jsonInput" class="rounded border w-full p-2"></textarea>
                    <div>
                        <button @click="importJSON" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded w-full">Import</button>
                    </div>
                    <div class="pt-5 text-xs">JSON Format Example</div>
                    <div class="text-xs bg-gray-200 text-gray-600 p-5 border rounded">[{"invoiceNo": "INV-042400018", "amount": "79", "date": "20.04.24", "remarks": ""}]</div>
                </div>
                <div class="col-span-5">
                    <button @click="getBillInfo" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-3">Get Bill Info</button>
                    <button @click="getPayInfo" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-3">Get Pay Info</button>
                    <button @click="submitPayment" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-3">Submit Generate</button>
                </div>
                <div class="font-bold text-sm">
                    InvoiceNo
                </div>
                <div class="font-bold text-sm">
                    Amount
                </div>
                <div class="font-bold text-sm">
                    Date
                </div>
                <div class="font-bold text-sm">
                    Remarks
                </div>
                <div class="font-bold text-sm">
                    Status
                </div>

                <template v-if="render" v-for="i in intrownum" :key="i+'_num'">
                    <div class="font-bold text-sm">
                        <input v-model="datainput[i-1].invoiceNo" class="rounded border w-full p-2"/>
                    </div>
                    <div class="font-bold text-sm">
                        <input v-model="datainput[i-1].amount" class="rounded border w-full p-2"/>
                    </div>
                    <div class="font-bold text-sm">
                        <input v-model="datainput[i-1].date" class="rounded border w-full p-2"/>
                    </div>
                    <div class="font-bold text-sm">
                        <input v-model="datainput[i-1].remarks" class="rounded border w-full p-2"/>
                    </div>
                    <div class="font-thin text-xs whitespace-pre-line">
                        {{ datainput[i-1].result || 'Pending' }}
                    </div>
                </template>
            </div>
        </div>

    </div>
</template>
<script setup lang="ts">
import adminheader from '@/components/AdminHeader.vue'
import { nextTick, ref, watch, computed, inject } from 'vue'
import { getBillings, getPayments, createPayment } from '../../api'
import moment from 'moment'
const authStore: any = inject("authStore")
const authState = authStore.getState()
const token = authState.token
const rownum:any = ref("1")
const datainput:any = ref([{}])
const jsonInput: any = ref('')
// const datainput:any = ref([{
//     invoiceNo: 'INV-042400018',
//     amount: '79',
//     date: '20.04.24',
//     remarks: ''
// }])

const importJSON: any = () => {
    try {
        const jsonData = JSON.parse(jsonInput.value)
        if (Array.isArray(jsonData)) {
            rownum.value = jsonData.length.toString()
            datainput.value = jsonData
        }
    } catch (error) {
    }
}

const render: any = ref(true)
const intrownum: any = computed(() => parseInt(rownum.value))
const submitPayment: any = () => {
    datainput.value.forEach(async (item: any) => {
        if (item.shouldPay) {
            // submit payment here
            const formatString = 'DD.MM.YY';
            const paymentdate = moment(item.date, formatString).toISOString()
            const pay:any = await createPayment({
                token, form: {
                    bill: item.bill[0].id,
                    amount: parseFloat(item.amount),
                    customer: item.bill[0].customer,                    
                    paymentdate: paymentdate,
                    remark: item.remarks,
                    statustxt: "paid",
                    platform: "manual"
                }
            })
            if (pay.data) {
                item.result += 'Payment Created\n'
                item.paymentdata = pay.data
            }
        }
    })

}


const getPayInfo: any = () => {
    datainput.value.forEach(async (item: any) => {
        if (item.bill) {
            // get item payment status here
            if (item.bill && item.bill[0].id) {
                const res:any = await getPayments({token, bill: item.bill[0].id})
                if (res.data && res.data.length > 0) {
                    item.result += 'Payment Found\n'
                    item.payment = res.data
                } else {
                    item.result += 'Payment Not Found\n'
                    item.shouldPay = true
                }
            }
        }
    })
}
const getBillInfo: any = () => {
    // loop all datainput and update payment status
    datainput.value.forEach(async (item: any) => {
        // update payment status here
        item.result = ''
        const res:any = await getBillings({token, billno: item.invoiceNo})
        if (res.data && res.data.length > 0) {
            item.bill = res.data
            item.result += 'Bill Data Obtained\n'
        } else {
            item.result += 'Bill Data Not Found\n'
        }
        
    })
}
watch(rownum, (val1: any, oldVal: any) => {
    render.value = false
    const newVal = parseInt(val1)
    nextTick(() => {
        render.value = true
    })
    if (newVal > oldVal) {
        while (newVal > datainput.value.length) {
            datainput.value.push({})
        }
    } else {

        while (newVal < datainput.value.length) {
            datainput.value.pop()
        }
    }
})
</script>