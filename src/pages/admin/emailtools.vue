<template>
    <div>
        <adminheader
        :title="$t('navigations.admin')"></adminheader>
        <div class="bg-white rounded-md m-2 p-4">
            <h2 class="text-xl font-bold mb-2">Email Tools</h2>
            <div class="flex flex-col">
                <div class="mb-2 gap-2">
                    <div class="">
                        <select v-model="statustxt" class="text-sm placeholder-gray-500 border rounded w-full bg-white p-2 focus:ring-blue-500">
                            <option value="" selected disabled>Customer with status</option>
                            <option value="new">New</option>
                            <option value="pendingdeposit">Pending Deposit</option>
                            <option value="pendinginstall">Pending Installation</option>
                            <option value="pendingactivation">Pending Activation</option>
                            <option value="active">Active</option>
                            <option value="suspended">Suspended</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="terminated">Terminated</option>
                            <option value="migration">Migration</option>
                        </select>
                        <div>
                            <button @click="getEmails" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">Get Emails</button>
                        </div>
                        <div v-if="emails && emails.length > 0">
                            <div v-for="email in emails" class="bg-gray-100 inline-block text-xs rounded border p-1 m-1">
                                {{ email }}
                            </div>
                        </div>
                        <div v-else class="text-gray-500 text-center p-10 rounded border mt-5 text-xs" >
                            {{ $t('admin.noEmails') }}
                        </div>
                    </div>
                    <div class="mt-5">
                        <label for="subject-input" class="block text-gray-700 text-sm font-bold mb-2">Subject:</label>
                        <input type="text" id="subject-input" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" v-model="subject">
                    </div>
                </div>
                <div>
                    <textarea id="message-input" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="10" v-model="message"></textarea>
                </div>
                <div>
                    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full" @click="sendEmailFunc">Send Email</button>
                </div>
            </div>
        </div>
    </div>
    <div v-if="loading" class="fixed top-0 left-0 w-full h-full bg-gray-900 bg-opacity-50 z-50">
        {{ emailsdone.length }} / {{ emails.length }} emails sent.
    </div>
</template>
<script setup lang="ts">
import { ref, inject } from 'vue'
import { getSubscriptionsEmails, sendEmail } from '../../api'
import adminheader from '@/components/AdminHeader.vue'
const authStore: any = inject("authStore")
const authState = authStore.getState()
const loading: any = ref(false)
const statustxt: any = ref('')
const emails: any = ref(["<EMAIL>"])
const emailsdone: any = ref([])
const subject: any = ref('')
const message: any = ref('')

const sendEmailFunc: any = async () => {
    if (emails.value.length > 0 && subject.value && message.value) {
        // stop whole page
        loading.value = true
        const body: string = message.value.replace(/\n/g, '<br>')

        const title: string = subject.value
        // send to all emails one by one, wait 5s between each email
        for (let i = 0; i < emails.value.length; i++) {
            const email: string = emails.value[i]
            sendEmail({form:{emails: [email], title: title, body: body}, token: authState.token}).then((res: any) => {
                if (res && res.data && res.data.success) {
                    emailsdone.value.push(email)
                }
            })
            await new Promise(resolve => setTimeout(resolve, 5000))
        }
        loading.value = false
        alert("Done Sending Emails")
        // stop whole page
    } else {
        alert('Please fill all fields')
    }
}
const getEmails: any = () => {
    if (statustxt.value) {
        // get emails by status
        getSubscriptionsEmails({statustxt: statustxt.value, token: authState.token}).then((res: any) => {
            if (res && res.data && res.data.length > 0) {
                emails.value = res.data
            } else {
                emails.value = []
            }
        })
    } else {
        alert('Please select a status')
    }
}
</script>  