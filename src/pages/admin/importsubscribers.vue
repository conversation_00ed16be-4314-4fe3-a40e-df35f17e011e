<template>
    <div>
        <adminheader
            :title="$t('navigations.admin')"></adminheader>

        <div class="w-full h-screen w-screen fixed left-0 top-0 z-30 bg100" v-if="processStart">
            <div class="m-10 p-10 bg-white rounded">
                <div class="text-center animate-bounce">{{$t('c.processing')}}</div>
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <div>{{$t('c.users')}}</div>
                        {{ uniqueCustomerProfilesToCreate.length }} / {{ Object.keys(uniqueCustomerProfilesDone).length }}
                    </div>
                    <div class="text-center">
                        <div>{{$t('c.subscriptions')}}</div>
                        {{ processData.length }} / {{ selectedItem.length }}
                    </div>
                </div>
                <div>
                    <div class="mb-5" v-for="(p,pi) in processData" :key="'iNt_'+pi">
                        <div>{{p}}</div>
                    </div>
                </div> 
                <div class="text-center">
                    <button @click="processStart = false" class="bg-red-500 text-white p-2 rounded w-full">{{$t('c.close')}}</button>
                </div>                   
            </div>            
        </div>    
        <div>
            <input class="hidden" name="csvinput" @change="loadCSVFile" ref="buttonImportCSV" type="file" accept=".csv" />
            <div @click="clickbuttonImportCSV" class="text-center p-3 cursor-pointer bg-blue-300 hover:text-white hover:bg-blue-500 rounded m-2">
                {{$t('c.importfromcsv')}}
            </div>
        </div>
        <div class="p-5" v-if="datapreview">
            <div class="text-right"><input type="checkbox" v-model="checkAll" />{{$t('c.checkall')}}</div>
            <div v-if="selectedItem.length > 0">
                <button @click="startImport" class="bg-red-500 text-white p-2 rounded w-full">{{$t('c.importnow')}}</button>
            </div>
        </div>
        <div class="w-full" v-if="datapreview">
            <div class="text-center mb-5">
                <div>
                    <input type="checkbox" v-model="existcheckbyusername" />Check by Username
                    <div @click="processCheckByUsername" class="bg-pink-500 text-white p-2 rounded w-full mt-2">
                        Process Check
                    </div>
                </div>

                <div class="mt-10">
                    <div @click="updateDatesByUsername" class="bg-pink-500 text-white p-2 rounded w-full mt-2">
                        Date Update
                    </div>
                </div>
            </div>
            <div class='overflow-x-auto'>
                <table class='table-auto overflow-scroll w-full'>
                    <thead>
                        <tr class="bg-gray-700 text-white text-xs">
                            <th></th>
                            <template v-if="existcheckbyusername">
                                <th class="p-2 break-words">username</th>
                                <th class="p-2 break-words">check status</th>
                                <th class="p-2 break-words">Date Updates</th>
                            </template>
                            <th class="p-2 break-words" v-for="h in getHeaders" :key="h">{{h}}</th>    
                                                    
                        </tr>
                    </thead>
                    <tbody class="text-xs">
                        <tr v-for="(d,di) in datapreview" :key="'item_'+di">
                            <td>{{di+ 1}}. <input v-model="selectedItem" :value="d" type="checkbox" /></td>
                            <template v-if="existcheckbyusername">
                                <td class="p-2 break-words">{{d["pppoe_username"]}}</td>
                                <td>
                                    {{ checkStatus1 && checkStatus1[d["pppoe_username"]] ? 'EXISTED' : '--' }}
                                </td>
                                <td>
                                    {{ dateUpdate && dateUpdate[d["pppoe_username"]] ? 'Updated' : '--' }}
                                </td>
                            </template>
                            <td class="p-2 break-words" v-for="h in getHeaders" :key="h">{{d[h]}}</td>                            
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>
<style scoped>
.bg100 {
    background: rgba(0,0,0,0.7);
}
</style>
<script lang="ts" setup>
import { ref, computed, watch, inject, onMounted } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import { createReturnUser } from '../../api'
import Papa from 'papaparse'
import { planStore } from '../../store/plan-store'
import md5 from 'md5'
import moment from 'moment'
import { createSubscription, getUserByUsername } from '../../api'
import { updateSubscription } from '../../api'

const authStore: any = inject("authStore")

const authState = authStore.getState()
const planState = planStore.getState()
const buttonImportCSV:any = ref(null)
const checkAll: any = ref(false)
const existcheckbyusername: any = ref(false)
const datapreview: any = ref(undefined)
const selectedItem: any = ref([])
const processData: any = ref([])
const processStart: any = ref(false)
const uniqueCustomerProfilesToCreate: any = ref([])
const uniqueCustomerProfilesDone: any = ref({})
const checkStatus1: any = ref({})
const dateUpdate: any = ref({})

const plans: any = computed(() => {
    return planState.plans
})

const updateDatesByUsername: any = () => {
    
    for (let i = 0; i < datapreview.value.length; i++) {
        let data: any = {}
        const p = datapreview.value[i]
        if (p["activation_date"] && p["activation_date"] != "" && p["activation_date"].trim() != "" ) {
            data["activationdate"] = findDate(p["activation_date"])
            data["subscribedate"] = findDate(p["activation_date"])
        }
        if (p["end_contract"] && p["end_contract"] != "" && p["end_contract"].trim() != "" ) {
            data["contractenddate"] = findDate(p["end_contract"])
        }
        if (p["suspend_date"] && p["suspend_date"] != "" && p["suspend_date"].trim() != "" ) {
            data["terminationdate"] = findDate(p["suspend_date"])
        }
        if (Object.keys(data).length > 0) {
            if(checkStatus1.value[p["pppoe_username"]]) {
                // update user
                updateSubscription({ token: authState.token, id: checkStatus1.value[p["pppoe_username"]]["id"], form: data}).then((res: any) => {
                    dateUpdate.value[res.data["username"]] = res.data
                })
            }
        }
    }
}

const findUserid = (p: any) => {
    // console.log(p)
    // console.log(uniqueCustomerProfilesDone.value)
    return (uniqueCustomerProfilesDone.value[p] && uniqueCustomerProfilesDone.value[p]["id"]) || ''
}

const processCheckByUsername = () => {
    // loop datapreview
    for (let i = 0; i < datapreview.value.length; i++) {
        const p = datapreview.value[i]
        const username = p["pppoe_username"]
        if (username) {
            // check if username exist in uniqueCustomerProfilesDone
            if (checkStatus1.value[username] || checkStatus1.value[username] === false) {
                // nothing here
            } else {
                getUserByUsername({token: authState.token, username}).then((res: any) => {
                    checkStatus1.value[username] = res
                })
            }
        }
    }

    
}

const findContract = (p: any) => {
    // "12 Months" / "24 Months"    
    if (p.trim() == "12 Months") {
        return 12
    } else if (p.trim() == "24 Months") {
        return 24
    } else {
        return 0
    }
}

const findBuilding: any = (p: any) => {
    const k: any = {
        "Verdi Eco-dominiums @ Cyberjaya": "Verdi Eco Cyberjaya",
        "The Grand Subang Jaya SS15": "The Grand Subang Jaya SS15",
        "Residensi Hijauan (The Greens)": "Residensi Hijauan",
        "Mutiara Ville Residence Tower C": "Mutiara Villes Cyberjaya",
        "Sentul Point": "Sentul Point",
        "Kenwingston Square @ Cyberjaya": "Kenwingston Cyberjaya",
        "Emira Residence": "Emira Residence",
        "Green Park Residence": "Green Park Residence",
        "M Centura": "M Centura",
        "Riverville Residences": "Riverville Residence",
        "Shaftsbury Putrajaya": "Shaftsbury Residence Putrajaya",
        "Shaftsbury Residence @ Cyberjaya": "Shaftsbury Cyberjaya"
    }
    return k[p] || ''
}

const findRate = (p: any) => {
    const k: any = ["special_rate_price", "package_id/lst_price"]
    if (p[k[0]] && p[k[0]] > 0) {
        return parseFloat(p[k[0]])
    } else {
        return parseFloat(p[k[1]])
    }
}

const createSubscribeJSON: any = (p: any, plan: any) => {
    // console.log(p)
    return {
        "customer": findUserid(p["partner_id/customer_code"]),
        "plan": plan && plan.id,
        "name":	p["partner_id/name"],
        "phone": p["partner_id/mobile"],
        "email": p["partner_id/email"],
        "contact": p["partner_id/mobile"],
        "contact2": p["partner_id/phone"] || p["partner_id/snd_mobile"] || '',
        "username": p["pppoe_username"],
        "userpassword": p["pppoe_password"],
        "lastbilldate": "2024-02-29T16:00:00.000Z",
        "monthlycharges": findRate(p),
        "price": findRate(p),
        "contractmonths": findContract(p["special_rate_price"]),
        "freeusage": 0,
        "attachments": [],
        "remark": "",
        "equipments": [],
        "ebilling": true,
        "address": {
            "block": "",
            "level": "",
            "unit": p["unit_no"],
            "address": p["partner_id/street"] + " " + p["partner_id/street2"],
            "city": p["partner_id/city"],
            "state": p["partner_id/state_id/display_name"],
            "postcode": p["partner_id/zip"],
            "building": findBuilding(p["svr_location_id/name"])
        },
        "billingaddress": {
            "address": p["partner_id/street"] + " " + p["partner_id/street2"],
            "city": p["partner_id/city"],
            "state": p["partner_id/state_id/display_name"],
            "postcode": p["partner_id/zip"],
        },
        "deposit": 0,
        "advancedpayment": 0,
        "balanceadvancepayment": 0,
        "statustxt": p["suspend"] == "FALSE" ? "active" : "terminated",
        "status": true,
        "new": false,
        "subscribedate": findDate(p["activation_date"]),
        "activationdate": findDate(p["activation_date"]),
        "contractenddate": findDate(p["end_contract"]),
        "terminationdate": findDate(p["suspend_date"]),
    }
}

const findDate = (inputDateString: any) => {
    // Parse the input date string using Moment.js
    if (!inputDateString) {
        return '';
    }
    // const parsedDate = moment.utc(inputDateString, "DD/MM/YYYY").subtract(8, 'hours'); // Assuming UTC-8
    const parsedDate = moment.utc(inputDateString, "M/D/YYYY").subtract(8, 'hours'); // Assuming UTC-8

    // Format the parsed date as required
    return parsedDate.format();
}

const getPlan: any = (p: any) => {
    let pp: any = planState.plans.data.find((item: any) => item.title == p)
    return pp|| false
}

const findUnique = (data: any, key: any) => {
    // key = "partner_id/customer_code"
    if (!key || key == "") {
        key = "partner_id/customer_code"
    }
    return Array.from(new Set(data.map((item: any) => item[key]))).filter((item2: any) => item2 != null);
}
const clickbuttonImportCSV = () => {
  buttonImportCSV.value.click()
}

const startImport = () => {
    if (selectedItem.value.length > 0) {
        processData.value = []
        processStart.value = true
        importnow()
    }
    
}
const importnow = () => {
    uniqueCustomerProfilesToCreate.value = findUnique(selectedItem.value, 'partner_id/customer_code')
    if (uniqueCustomerProfilesToCreate.value.length > 0) {
        // loop profiles
        for (let i = 0; i < uniqueCustomerProfilesToCreate.value.length; i++) {
            let p = uniqueCustomerProfilesToCreate.value[i]
            // get profile from processData which match the id
            let profile = selectedItem.value.find((item: any) => item["partner_id/customer_code"] == p)
            if (profile) {
                // createReturn User
                // random password
                const pa: string = Math.random().toString(36).slice(-10)
                // md5 the password
                
                const password = md5(pa) 

                createReturnUser({token: authState.token, form: {
                    "password": password,
                    "email": profile["partner_id/email"],
                    "identityno": profile["partner_id/customer_code"],
                    "mobile": profile["partner_id/mobile"],
                    "name": profile["partner_id/name"],
                    "customer": true,
                }}).then((rs: any) => {
                    uniqueCustomerProfilesDone.value[rs.data.identityno] = rs.data
                    // if done, then proceed to create subscriptions
                    if (Object.keys(uniqueCustomerProfilesDone.value).length == uniqueCustomerProfilesToCreate.value.length) {
                        createSubscriptionNow()
                    }
                })
            }
        }
    } 
}

const createSubscriptionNow = () => {
    // loop selectedItem.value
    for (let i = 0; i < selectedItem.value.length; i++) {
        let p: any = selectedItem.value[i]
        let plan1: any = getPlan(p["package_id/name"])
        if (plan1) {
            let k: any = createSubscribeJSON(p, plan1)
            createSubscription({token: authState.token, form: k}).then((rs: any) => {
                if (rs && rs.data) {
                    console.log(rs.data)
                    processData.value.push(rs.data)
                }
            })
        }        
    }
}

const getHeaders = computed(() => {
    return datapreview.value && datapreview.value[0] && Object.keys(datapreview.value[0])
})

const loadCSVFile: any = (e: any) => {
  var file = e.target.files[0]
  let k: any = Papa.parse(file, { header: true, complete: (results: any) => {
    console.log("Finished:", results.data);
    datapreview.value = results.data
  }})
} 

watch(checkAll, (p: any) => {
    if (p === true) {
        selectedItem.value = datapreview.value
    } else {
        selectedItem.value = []
    }
})

onMounted(() => {
    // load all plans
    let p = { limit: 50, token: authState.token, params: ['$hide'], '$hide': '1' }
    planStore.getPlans(p)
})

</script>