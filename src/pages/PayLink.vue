<template>
    <div class="text-xs text-right">{{$t('c.upaylink')}} - {{id}}</div>
    <div class="text-2xl text-center">
        {{$t('payments.fpxPaymentForms')}}
    </div>
    <div class="border rounded mx-auto max-w-sm my-5 p-5 bg-white">
        <div class="text-center" v-if="bill == undefined">
            <loading />
        </div>
        <div class="text-center text-sm text-red-600" v-else-if="bill === false">
            {{$t('payments.billinvalid')}}
            <div class="mt-12 mb-5">
                <img src="https://i.imgur.com/WxbbaZk.jpg" class="w-full" />
            </div>
        </div>
        <div v-else>
            <div class="text-right text-sm border-b mb-2">{{formatdate(bill.data.billdate)}}</div>
            <div class="text-right font-bold text-lg mb-5">
                {{bill.data.billno}}
            </div>
            <div class="font-bold border-b">{{$t('billings.items')}}</div>
            <div>
                <div v-for="(p, pi) in bill.data.items" :key="`${String(pi)}_items`">
                    <div class="text-sm">{{add1p(pi)}}. {{p.itemname}}</div>
                    <div class="text-right">{{formatMoney(p.amount)}}</div>
                </div>
            </div>
            <div class="flex border-t">
                <div class="w-1/4">{{$t('c.subtotal')}}</div>
                <div class="w-3/4 text-right">{{formatMoney(bill.data.amountcurrent)}}</div>
            </div>
            <div class="flex">
                <div class="w-1/4">{{$t('c.tax')}}</div>
                <div class="w-3/4 text-right">{{formatMoney(bill.data.taxcurrent)}}</div>
            </div>
            <div class="flex">
                <div class="w-1/4">{{$t('c.amtbf')}}</div>
                <div class="w-3/4 text-right">{{formatMoney(bill.data.amountbf)}}</div>
            </div>
            <div class="flex border-t border-b">
                <div class="w-1/4">{{$t('c.total')}}</div>
                <div class="w-3/4 text-right">{{formatMoney(bill.data.totalamount)}}</div>
            </div>

            <div class="mt-5">
                <select v-model="paymentform.method" class="p-2 rounded w-full">
                    <option value="">{{$t('c.selectPayment')}}</option>
                    <option v-for="(g) in pll" :value="g.id" :key="g.id">{{g.title}}</option>
                </select>
            </div>

            <div class="my-2" v-if="paymentform.method != ''">
                <div @click="doMakePayment" class="rounded cursor-pointer hover:bg-purple-800 m-1 bg-purple-500 text-white text-center p-2">{{$t('c.makepayment')}}</div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from "vue"
import { getBilling, createSubplacePayment } from '../api/index'
import loading from '@/components/cvui/loading.vue'
import moment from 'moment'
export default defineComponent({
  computed: {
    id () {
        return this.$route.params.id
    }
  },
  components: {
    loading,
  },
  mounted () {
    this.getBill()
  },
  methods: {
	add1p (p: any) {
		return (p + 1).toString()
	},
    doMakePayment() {
        createSubplacePayment(this.paymentform).then((res: any) => {
            if (res && res.data) {
				// convert to json
				try {
					let po = JSON.parse(res.data)
					if (po.error == "00") {
						window.location = po.data
					} else {
						alert("Error Payment 3")
					}
				} catch {
					alert("Error Payment 2")
				}
			} else {
				alert("Error Payment")
			}
        })
    },
    formatdate (p: any) {
        return moment(p).format('DD/MM/YYYY')
    },
    getBill () {
        getBilling({id: this.id}).then((res: any) => {
            this.bill = res
            this.paymentform.id = this.id
        }).catch((err: any) => {
            this.bill = false
        })
    },
    formatMoney (p: any) {
        if (Number.isNaN(p)) {
            return '0.00'
        } else {
            return p.toFixed(2)
        }
    }
  },
  data () {
    let bill: any = undefined
    let pll: any = [{
			"ewallet": false,
			"fpx": false,
			"group": "credit",
			"id": "credit",
			"min": 2,
			"title": "Credit Card"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxbimb",
			"min": 2,
			"title": "Bank Islam"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxmbb",
			"maintenanceend": "00:15",
			"maintenancestart": "00:00",
			"min": 2,
			"title": "Maybank2u"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxpbb",
			"min": 2,
			"title": "Public Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxcimb",
			"min": 2,
			"title": "CIMBClicks"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxamb",
			"min": 2,
			"title": "AmOnline"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxhlb",
			"min": 2,
			"title": "Hong Leong"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxrhb",
			"maintenanceend": "00:10",
			"maintenancestart": "00:00",
			"min": 2,
			"title": "RHB Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxocbc",
			"min": 2,
			"title": "OCBC"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxscb",
			"min": 2,
			"title": "Standard Chartered"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxabb",
			"min": 2,
			"title": "Affin Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxbrb",
			"maintenanceend": "00:30",
			"maintenancestart": "00:00",
			"min": 2,
			"title": "Bank Rakyat"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxbmmb",
			"min": 2,
			"title": "Bank Muamalat"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxkfh",
			"min": 2,
			"title": "Kuwait Finance House"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxbsn",
			"maintenanceend": "00:15",
			"maintenancestart": "00:00",
			"min": 2,
			"title": "Bank Simpanan Nasional"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxabmb",
			"min": 2,
			"title": "Alliance Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxuob",
			"min": 2,
			"title": "United Overseas Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxhsbc",
			"min": 2,
			"title": "HSBC Bank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebanking",
			"id": "fpxagro",
			"min": 2,
			"title": "Agrobank"
		},
		{
			"ewallet": false,
			"fpx": true,
			"group": "onlinebankingenterprise",
			"id": "fpxbamb",
			"maintenanceend": "02:00",
			"maintenancestart": "00:00",
			"min": 2,
			"title": "AmBank"
		}]
    let paymentform: any = {
        method: '',
        id: '',
    }
    return {
        bill,
        paymentform,
        pll,
    }
  }
})
</script>