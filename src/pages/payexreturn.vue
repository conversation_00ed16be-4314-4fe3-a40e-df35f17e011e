<template>
    <div class="flex items-center justify-center mt-20 roboto">
        <div class="p-4 rounded shadow-xl">
            <div class="flex flex-col items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="text-green-600 w-28 h-28 bounce" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor" stroke-width="1">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h1 class="text-4xl font-bold">Thank You !</h1>
                <p class="mt-4">Thank you for your interest! Check your email for a link to the guide.</p>
                <div
                    class="inline-flex items-center mt-6 px-4 py-2 text-white bg-indigo-600 border border-indigo-600 rounded-full hover:bg-indigo-700 focus:outline-none focus:ring">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                    </svg>
                    <a href="/" class="text-sm font-medium">
                        Home
                    </a>
                </div>
            </div>
            <div class="text-center text-xs text-gray-500 py-10">
                Copyright © 2024 HIIFI (M) SDN BHD (1406319-P). All rights reserved.
            </div>
        </div>
    </div>
</template>
<style scoped>
.roboto {
    font-family: 'Roboto';
}
.bounce {
  animation: bounce 4s ease 0.5s;
}
@keyframes bounce {
    70% { transform:translateY(0%); }
    80% { transform:translateY(-15%); }
    90% { transform:translateY(0%); }
    95% { transform:translateY(-7%); }
    97% { transform:translateY(0%); }
    99% { transform:translateY(-3%); }
    100% { transform:translateY(0); }
}
</style>