import { createApp } from 'vue'
import App from './App.vue'
import './style/index.css'
import { setupRouter } from './router'
import { setupI18n } from './i18n'
// @ts-ignore
import enUS from './locales/en-US.yaml'
import { authStore } from './store/auth-store'
const i18n = setupI18n({
  legacy: false,
  globalInjection: true,
  locale: 'en-US',
  fallbackLocale: 'en-US',
  messages: {
    enUS
  }
})

const router = setupRouter(i18n)

createApp(App).use(i18n).use(router).provide("authStore", authStore).mount('#app')
