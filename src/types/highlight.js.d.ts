declare module 'highlight.js' {
  export default hljs;

  interface HighlightResult {
    relevance: number;
    language: string;
    value: string;
  }

  interface LanguageRegistration {
    register: (language: any) => void;
  }

  class hljs {
    static highlight(name: string, value: string, ignore_illegals?: boolean, continuation?: any): HighlightResult;
    static highlightAuto(value: string, languageSubset?: string[]): HighlightResult;
    static fixMarkup(value: string): string;
    static highlightBlock(block: HTMLElement): void;
    static configure(options: any): void;
    static initHighlighting(): void;
    static initHighlightingOnLoad(): void;
    static registerLanguage(name: string, language: any): void;
    static listLanguages(): string[];
    static getLanguage(name: string): any;
    static registerAliases(aliases: string | string[], { languageName }: { languageName: string }): void;
  }
}
