export default {
  landing: {
    default: [
      // { pathname: 'home', title: 'navigations.home' },
      /*
      { pathname: 'about', title: 'navigations.about', icon: 'about' },
      { pathname: 'pricing', title: 'navigations.pricing', icon: 'pricing' },
      { pathname: 'priviledges', title: 'navigations.priviledges', icon: 'priviledges' },
      */
    ],
    profile: [
      { pathname: 'dashboard', title: 'navigations.dashboard', icon: 'dashboard' },
      { pathname: 'logout', title: 'navigations.logout', icon: 'logout' }
    ]
  },
  auth: {
    admin: [
      { pathname: 'dashboard', title: 'navigations.dashboard', icon: 'dashboard' },
      { pathname: 'users', title: 'navigations.users', icon: 'users', scopes: ['dev', 'admin', 'moderator', 'supportl1'] },
      { pathname: 'buildings', title: 'navigations.buildings', icon: 'warehouse', scopes: ['dev'] },
      { pathname: 'subscriptiongroups', title: 'navigations.subscriptiongroups', icon: 'customers2', scopes: ['dev'] },
      { pathname: 'plans', title: 'navigations.plans', icon: 'collection', scopes: ['dev', 'admin'] },
      { pathname: 'subscriptions', title: 'navigations.subscriptions', icon: 'projects', scopes: ['dev', 'admin', 'moderator', 'agent'] },
      { pathname: 'subscriptionsl1', title: 'navigations.subscriptionsl1', icon: 'projects', scopes: ['dev', 'admin', 'moderator', 'agent', 'supportl1'] },
      { pathname: 'techorder', title: 'navigations.technicalinstallationorder', icon: 'report2', scopes: ['dev', 'admin', 'moderator', 'technical'] },
      { pathname: 'billings', title: 'navigations.billings', icon: 'invoice', scopes: ['dev', 'admin', 'moderator', 'finance'] },
      { pathname: 'prebills', title: 'navigations.prebills', icon: 'invoice', scopes: ['dev', 'admin', 'moderator', 'finance'] },
      { pathname: 'ticketsCRUD', title: 'navigations.tickets', icon: 'ticket', scopes: ['dev', 'admin', 'moderator', 'support', 'supportl1'] },
      { pathname: 'ticketsFinance', title: 'navigations.financeTickets', icon: 'ticket', scopes: ['dev', 'admin', 'moderator', 'finance', 'supportl1'] },
      { pathname: 'report', title: 'navigations.report', icon: 'report', scopes: ['dev', 'admin', 'moderator', 'finance'] },
      { pathname: 'guides', title: 'navigations.guides', icon: 'documenttext', scopes: ['dev', 'admin', 'moderator', 'finance'] },
    ],
    customer: [
      { pathname: 'dashboard', title: 'navigations.dashboard', icon: 'dashboard' },
      // { pathname: 'faq', title: 'navigations.faq', icon: '' },
      { pathname: 'settings', title: 'navigations.settings', icon: '' },
      // { pathname: 'ticketsCustomer', title: 'navigations.tickets', icon: 'ticket' },
    ],
    profile: [
      { pathname: 'home', title: 'navigations.home', icon: 'home' },
      { pathname: 'logout', title: 'navigations.logout', icon: 'logout' }
    ]
  }
  /* adminlist:
  adminprofile:  */
}
