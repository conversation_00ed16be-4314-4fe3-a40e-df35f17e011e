<template>
  <div class="navbar-menu relative" :class="{ 'hidden': !profile }">
    <!-- <div v-if="authStoreState.boolMobileMenu" class="navbar-backdrop fixed inset-0 bg-gray-800 opacity-10 z-20"></div> -->
    <nav class="fixed top-0 left-0 right-0 bottom-0 flex flex-col lg:w-80 sm:max-w-xs pt-6 pb-8 bg-white border-r overflow-y-auto z-20">
      <div class="flex justify-between w-full items-center px-6 pb-6 lg:border-b border-blue-50">
        <!-- <router-link :to="{ name: 'home' }" class="text-xl text-white font-semibold"> -->
          <img src="/favicon.png" alt="" width="150" height="150">
          <button @click="toggleMobileMenu" class="lg:hidden text-3xl text-gray-500 mb-3">&times;</button>
        <!-- </router-link> -->
      </div>
      <div class="px-4 pb-6">
        <!-- <div class="mb-3">
          <div v-if="(boolLogin && boolAdmin) || boolLogin" class="mb-5 text-center">
            <div class="mr-2">
              <img class="w-20 h-20 m-auto rounded-full object-cover object-right" src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-1.2.1&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=1050&amp;q=80" alt="">
            </div>
            <div class="mr-3">
              <p class="text-sm">Wong Benjamin</p>
              <p class="text-sm text-gray-400">Premium User</p>
            </div>
          </div>
          <ul class="flex items-center justify-center space-x-6 mb-3">
            <li>
              <a class="text-gray-400 hover:text-gray-500" href="#">
                <svg class="h-5 w-5" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20.7 19.3L17 15.6C20.1 11.7 19.5 6 15.6 2.9C11.7 -0.2 5.99999 0.5 2.89999 4.3C-0.200006 8.2 0.499995 13.9 4.29999 17C7.59999 19.6 12.3 19.6 15.6 17L19.3 20.7C19.7 21.1 20.3 21.1 20.7 20.7C21.1 20.3 21.1 19.7 20.7 19.3ZM9.99999 17C6.09999 17 2.99999 13.9 2.99999 10C2.99999 6.1 6.09999 3 9.99999 3C13.9 3 17 6.1 17 10C17 13.9 13.9 17 9.99999 17Z" fill="currentColor"></path>
                </svg>
              </a>
            </li>
            <li>
              <a class="text-gray-400 hover:text-gray-500" href="#">
                <svg class="h-5 w-5" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 0H3C2.20435 0 1.44129 0.316071 0.87868 0.87868C0.316071 1.44129 0 2.20435 0 3V14C0 14.7956 0.316071 15.5587 0.87868 16.1213C1.44129 16.6839 2.20435 17 3 17H5.59L8.29 19.71C8.38344 19.8027 8.49426 19.876 8.61609 19.9258C8.73793 19.9755 8.86839 20.0008 9 20C9.23834 20 9.46886 19.9149 9.65 19.76L12.87 17H15C15.7956 17 16.5587 16.6839 17.1213 16.1213C17.6839 15.5587 18 14.7956 18 14V3C18 2.20435 17.6839 1.44129 17.1213 0.87868C16.5587 0.316071 15.7956 0 15 0ZM16 14C16 14.2652 15.8946 14.5196 15.7071 14.7071C15.5196 14.8946 15.2652 15 15 15H12.5C12.2617 15 12.0311 15.0851 11.85 15.24L9.05 17.64L6.71 15.29C6.61656 15.1973 6.50574 15.124 6.38391 15.0742C6.26207 15.0245 6.13161 14.9992 6 15H3C2.73478 15 2.48043 14.8946 2.29289 14.7071C2.10536 14.5196 2 14.2652 2 14V3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H15C15.2652 2 15.5196 2.10536 15.7071 2.29289C15.8946 2.48043 16 2.73478 16 3V14Z" fill="currentColor"></path>
                </svg>
              </a>
            </li>
            <li>
              <a class="text-gray-400 hover:text-gray-500" href="#">
                <svg class="h-5 w-5" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14 11.18V8C13.9986 6.58312 13.4958 5.21247 12.5806 4.13077C11.6655 3.04908 10.3971 2.32615 9 2.09V1C9 0.734784 8.89464 0.48043 8.70711 0.292893C8.51957 0.105357 8.26522 0 8 0C7.73478 0 7.48043 0.105357 7.29289 0.292893C7.10536 0.48043 7 0.734784 7 1V2.09C5.60294 2.32615 4.33452 3.04908 3.41939 4.13077C2.50425 5.21247 2.00144 6.58312 2 8V11.18C1.41645 11.3863 0.910998 11.7681 0.552938 12.2729C0.194879 12.7778 0.00173951 13.3811 0 14V16C0 16.2652 0.105357 16.5196 0.292893 16.7071C0.48043 16.8946 0.734784 17 1 17H4.14C4.37028 17.8474 4.873 18.5954 5.5706 19.1287C6.26819 19.6621 7.1219 19.951 8 19.951C8.8781 19.951 9.73181 19.6621 10.4294 19.1287C11.127 18.5954 11.6297 17.8474 11.86 17H15C15.2652 17 15.5196 16.8946 15.7071 16.7071C15.8946 16.5196 16 16.2652 16 16V14C15.9983 13.3811 15.8051 12.7778 15.4471 12.2729C15.089 11.7681 14.5835 11.3863 14 11.18ZM4 8C4 6.93913 4.42143 5.92172 5.17157 5.17157C5.92172 4.42143 6.93913 4 8 4C9.06087 4 10.0783 4.42143 10.8284 5.17157C11.5786 5.92172 12 6.93913 12 8V11H4V8ZM8 18C7.65097 17.9979 7.30857 17.9045 7.00683 17.7291C6.70509 17.5536 6.45451 17.3023 6.28 17H9.72C9.54549 17.3023 9.29491 17.5536 8.99317 17.7291C8.69143 17.9045 8.34903 17.9979 8 18ZM14 15H2V14C2 13.7348 2.10536 13.4804 2.29289 13.2929C2.48043 13.1054 2.73478 13 3 13H13C13.2652 13 13.5196 13.1054 13.7071 13.2929C13.8946 13.4804 14 13.7348 14 14V15Z" fill="currentColor"></path>
                </svg>
              </a>
            </li>
          </ul>
          <a
              v-if="boolLogin"
              @click="boolLogin = !boolLogin"
              class="flex items-center pl-3 py-3 pr-2 rounded w-1/2 m-auto text-white bg-red-500 hover:bg-red-300" href="#">
            <span class="inline-block mr-4">
              <svg class="text-gray-200 w-5 h-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.33368 9.99996C3.33368 10.221 3.42148 10.4329 3.57776 10.5892C3.73404 10.7455 3.946 10.8333 4.16701 10.8333H10.492L8.57535 12.7416C8.49724 12.8191 8.43524 12.9113 8.39294 13.0128C8.35063 13.1144 8.32885 13.2233 8.32885 13.3333C8.32885 13.4433 8.35063 13.5522 8.39294 13.6538C8.43524 13.7553 8.49724 13.8475 8.57535 13.925C8.65281 14.0031 8.74498 14.0651 8.84653 14.1074C8.94808 14.1497 9.057 14.1715 9.16701 14.1715C9.27702 14.1715 9.38594 14.1497 9.48749 14.1074C9.58904 14.0651 9.68121 14.0031 9.75868 13.925L13.092 10.5916C13.1679 10.5124 13.2273 10.4189 13.267 10.3166C13.3504 10.1137 13.3504 9.88618 13.267 9.68329C13.2273 9.581 13.1679 9.48755 13.092 9.40829L9.75868 6.07496C9.68098 5.99726 9.58874 5.93563 9.48722 5.89358C9.3857 5.85153 9.27689 5.82988 9.16701 5.82988C9.05713 5.82988 8.94832 5.85153 8.8468 5.89358C8.74529 5.93563 8.65304 5.99726 8.57535 6.07496C8.49765 6.15266 8.43601 6.2449 8.39396 6.34642C8.35191 6.44794 8.33027 6.55674 8.33027 6.66663C8.33027 6.77651 8.35191 6.88532 8.39396 6.98683C8.43601 7.08835 8.49765 7.18059 8.57535 7.25829L10.492 9.16663H4.16701C3.946 9.16663 3.73404 9.25442 3.57776 9.4107C3.42148 9.56698 3.33368 9.77895 3.33368 9.99996ZM14.167 1.66663H5.83368C5.17064 1.66663 4.53475 1.93002 4.06591 2.39886C3.59707 2.8677 3.33368 3.50358 3.33368 4.16663V6.66663C3.33368 6.88764 3.42148 7.0996 3.57776 7.25588C3.73404 7.41216 3.946 7.49996 4.16701 7.49996C4.38803 7.49996 4.59999 7.41216 4.75627 7.25588C4.91255 7.0996 5.00035 6.88764 5.00035 6.66663V4.16663C5.00035 3.94561 5.08814 3.73365 5.24442 3.57737C5.4007 3.42109 5.61267 3.33329 5.83368 3.33329H14.167C14.388 3.33329 14.6 3.42109 14.7563 3.57737C14.9125 3.73365 15.0003 3.94561 15.0003 4.16663V15.8333C15.0003 16.0543 14.9125 16.2663 14.7563 16.4225C14.6 16.5788 14.388 16.6666 14.167 16.6666H5.83368C5.61267 16.6666 5.4007 16.5788 5.24442 16.4225C5.08814 16.2663 5.00035 16.0543 5.00035 15.8333V13.3333C5.00035 13.1123 4.91255 12.9003 4.75627 12.744C4.59999 12.5878 4.38803 12.5 4.16701 12.5C3.946 12.5 3.73404 12.5878 3.57776 12.744C3.42148 12.9003 3.33368 13.1123 3.33368 13.3333V15.8333C3.33368 16.4963 3.59707 17.1322 4.06591 17.6011C4.53475 18.0699 5.17064 18.3333 5.83368 18.3333H14.167C14.8301 18.3333 15.4659 18.0699 15.9348 17.6011C16.4036 17.1322 16.667 16.4963 16.667 15.8333V4.16663C16.667 3.50358 16.4036 2.8677 15.9348 2.39886C15.4659 1.93002 14.8301 1.66663 14.167 1.66663Z" fill="currentColor"></path>
              </svg>
            </span>
            <span>Log Out</span>
          </a>
          <router-link v-else :to="{ name: 'login' }" class="flex items-center pl-3 py-3 pr-2 rounded w-1/2 m-auto text-white bg-blue-500 hover:bg-blue-300">
            <span class="inline-block mr-4">
              <svg class="text-gray-200 w-5 h-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.33368 9.99996C3.33368 10.221 3.42148 10.4329 3.57776 10.5892C3.73404 10.7455 3.946 10.8333 4.16701 10.8333H10.492L8.57535 12.7416C8.49724 12.8191 8.43524 12.9113 8.39294 13.0128C8.35063 13.1144 8.32885 13.2233 8.32885 13.3333C8.32885 13.4433 8.35063 13.5522 8.39294 13.6538C8.43524 13.7553 8.49724 13.8475 8.57535 13.925C8.65281 14.0031 8.74498 14.0651 8.84653 14.1074C8.94808 14.1497 9.057 14.1715 9.16701 14.1715C9.27702 14.1715 9.38594 14.1497 9.48749 14.1074C9.58904 14.0651 9.68121 14.0031 9.75868 13.925L13.092 10.5916C13.1679 10.5124 13.2273 10.4189 13.267 10.3166C13.3504 10.1137 13.3504 9.88618 13.267 9.68329C13.2273 9.581 13.1679 9.48755 13.092 9.40829L9.75868 6.07496C9.68098 5.99726 9.58874 5.93563 9.48722 5.89358C9.3857 5.85153 9.27689 5.82988 9.16701 5.82988C9.05713 5.82988 8.94832 5.85153 8.8468 5.89358C8.74529 5.93563 8.65304 5.99726 8.57535 6.07496C8.49765 6.15266 8.43601 6.2449 8.39396 6.34642C8.35191 6.44794 8.33027 6.55674 8.33027 6.66663C8.33027 6.77651 8.35191 6.88532 8.39396 6.98683C8.43601 7.08835 8.49765 7.18059 8.57535 7.25829L10.492 9.16663H4.16701C3.946 9.16663 3.73404 9.25442 3.57776 9.4107C3.42148 9.56698 3.33368 9.77895 3.33368 9.99996ZM14.167 1.66663H5.83368C5.17064 1.66663 4.53475 1.93002 4.06591 2.39886C3.59707 2.8677 3.33368 3.50358 3.33368 4.16663V6.66663C3.33368 6.88764 3.42148 7.0996 3.57776 7.25588C3.73404 7.41216 3.946 7.49996 4.16701 7.49996C4.38803 7.49996 4.59999 7.41216 4.75627 7.25588C4.91255 7.0996 5.00035 6.88764 5.00035 6.66663V4.16663C5.00035 3.94561 5.08814 3.73365 5.24442 3.57737C5.4007 3.42109 5.61267 3.33329 5.83368 3.33329H14.167C14.388 3.33329 14.6 3.42109 14.7563 3.57737C14.9125 3.73365 15.0003 3.94561 15.0003 4.16663V15.8333C15.0003 16.0543 14.9125 16.2663 14.7563 16.4225C14.6 16.5788 14.388 16.6666 14.167 16.6666H5.83368C5.61267 16.6666 5.4007 16.5788 5.24442 16.4225C5.08814 16.2663 5.00035 16.0543 5.00035 15.8333V13.3333C5.00035 13.1123 4.91255 12.9003 4.75627 12.744C4.59999 12.5878 4.38803 12.5 4.16701 12.5C3.946 12.5 3.73404 12.5878 3.57776 12.744C3.42148 12.9003 3.33368 13.1123 3.33368 13.3333V15.8333C3.33368 16.4963 3.59707 17.1322 4.06591 17.6011C4.53475 18.0699 5.17064 18.3333 5.83368 18.3333H14.167C14.8301 18.3333 15.4659 18.0699 15.9348 17.6011C16.4036 17.1322 16.667 16.4963 16.667 15.8333V4.16663C16.667 3.50358 16.4036 2.8677 15.9348 2.39886C15.4659 1.93002 14.8301 1.66663 14.167 1.66663Z" fill="currentColor"></path>
              </svg>
            </span>
            {{ $t('navigations.login') }}
          </router-link>
        </div> -->
        <div class="mt-8" v-if="profile && profile.customer">
          <h3 class="mb-2 text-xs uppercase text-gray-500 font-medium">{{$t('c.customer')}}</h3>
          <ul class="text-sm font-medium">
            <li v-for="(item, index) in customerlist" :key="index">
              <router-link :to="{ name: pathme(item) }" :class="isCurrentMenu(pathme(item)) ? 'text-white bg-blue-500' : 'text-gray-500 bg-white'" class="flex items-center pl-3 py-3 pr-4 rounded" @click="toggleMobileMenu">
                <svg v-if="item.pathname === 'faq'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-2 w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
                </svg>
                <svg v-else-if="item.pathname === 'settings'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-2 w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75a4.5 4.5 0 0 1-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 1 1-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 0 1 6.336-4.486l-3.276 3.276a3.004 3.004 0 0 0 2.25 2.25l3.276-3.276c.256.565.398 1.192.398 1.852Z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M4.867 19.125h.008v.008h-.008v-.008Z" />
                </svg>
                <svgCollection v-else :icon="iconme(item)" class="mr-2"></svgCollection>
                <span>{{ $t(ptitle(item)) }}</span>
                <span class="inline-block ml-auto">
                  <svg :class="isCurrentMenu(pathme(item)) ? 'text-white' : 'text-gray-400'" width="24px" height="24px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z" />
                  </svg>
                </span>
              </router-link>
            </li>
          </ul>
        </div>
        <div class="mt-8" v-if="profile && !profile.customer">
          <h3 class="mb-2 text-xs uppercase text-gray-500 font-medium">{{$t('c.admin')}}</h3>
          <ul class="text-sm font-medium">
            <li v-for="(item, index) in adminlist" :key="index">
              <router-link v-if="hasScopes(itemscope(item)) && canViewMenu(item)" :to="{ name: pathme(item) }" :class="isCurrentMenu(pathme(item)) ? 'text-white bg-blue-500' : 'text-gray-500 bg-white'" class="flex items-center pl-3 py-3 pr-4 rounded" @click="toggleMobileMenu">
                <svgCollection :icon="iconme(item)" class="mr-2"></svgCollection>
                <span>{{ $t(ptitle(item)) }}</span>
                <span class="inline-block ml-auto">
                  <svg :class="isCurrentMenu(pathme(item)) ? 'text-white' : 'text-gray-400'" width="24px" height="24px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z" />
                  </svg>
                </span>
              </router-link>
            </li>
          </ul>
        </div>
        <!-- hide -->
        <!-- <template v-if="isDev">
          <div class="mt-8">
            <h3 class="text-xs uppercase text-gray-500 font-medium">Dev Control</h3>
            <ul class="text-sm font-medium">
              <li v-for="(item, index) in devlist" :key="String(index)">
              <router-link :to="{ name: pathme(item) }" :class="isCurrentMenu(pathme(item)) ? 'text-white bg-blue-500' : 'text-gray-500 bg-white'" class="flex items-center pl-3 py-3 pr-4 rounded" @click="toggleMobileMenu">
                <svgCollection :icon="iconme(item)" class="mr-2"></svgCollection>
                <span>{{ $t(ptitle(item)) }}</span>
                <span class="inline-block ml-auto">
                  <svg :class="isCurrentMenu(pathme(item)) ? 'text-white' : 'text-gray-400'" width="24px" height="24px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z" />
                  </svg>
                </span>
              </router-link>
            </li>
            </ul>
          </div>
        </template> -->
        <div class="mt-8" v-if="profile">
          <h3 class="text-xs uppercase text-gray-500 font-medium">Others</h3>

          <router-link v-if="isDev" :to="{name: 'autocount'}" class="flex items-center pl-3 py-3 pr-2 text-gray-500 hover:bg-blue-50 rounded">
            <span class="inline-block mr-4">
              <svgCollection icon="cash"></svgCollection>
            </span>
            <span>{{$t('navigations.autocount')}}</span>
          </router-link>

          <router-link v-if="isDev" :to="{name: 'adminpanel'}" class="flex items-center pl-3 py-3 pr-2 text-gray-500 hover:bg-blue-50 rounded">
            <span class="inline-block mr-4">
              <svgCollection icon="settings2"></svgCollection>
            </span>
            <span>{{$t('navigations.adminsettings')}}</span>
          </router-link>

          <a @click="logoutfunc" class="flex items-center pl-3 py-3 pr-2 text-red-500 hover:bg-red-50 rounded" href="#">
            <span class="inline-block mr-4">
              <svgCollection icon="logout"></svgCollection>
            </span>
            <span>{{$t('navigations.logout')}}</span>
          </a>

        </div>
        <div v-if="!profile">
          <router-link :to="{name: 'login'}" class="flex items-center pl-3 py-3 pr-2 text-red-500 hover:bg-red-50 rounded">
            <span class="inline-block mr-4">
              <svgCollection icon="logout"></svgCollection>
            </span>
            <span>{{$t('navigations.login')}}</span>
          </router-link>
          <router-link :to="{name: 'forgot'}" class="flex items-center pl-3 py-3 pr-2 text-red-500 hover:bg-red-50 rounded">
            <span class="inline-block mr-4">
              <svgCollection icon="logout"></svgCollection>
            </span>
            <span>{{$t('navigations.forgotpass')}}</span>
          </router-link>
        </div>
      </div>
      <div class="text-xs text-gray-400 p-5">
        Copyright © 2024 <br/>HIIFI (M) SDN BHD (1406319-P). <br/>All rights reserved.
      </div>
    </nav>
  </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject, onBeforeMount } from 'vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import { authStore } from "../../store/auth-store"
import { auth2Store } from '../../store/auth2-store'
export default defineComponent({
  setup() {
    const auth2State = auth2Store.getState()
    const authStore = inject("authStore")
    onBeforeMount(async () => await authStore.init())
    const authState = authStore.getState()

    return {
        logoutSuccess: computed(() => auth2Store.getState().logoutSuccess ),
        logoutError: computed(() => auth2Store.getState().logoutError ),
        profile: computed(() => auth2State.profile ),
        showCustomer: false,
        authStore: authStore,
        authState
    }
  },
  props: {
    adminlist: Array,
    customerlist: Array,
    boolAdmin: {
      type: Boolean,
      dafault: false
    },
    boolLogin: {
      type: Boolean,
      dafault: false
    }
  },
  components: {
    svgCollection
  },
  methods: {
    toggleMobileMenu () {
      setTimeout(() => {
        this.authStore.setMobileMenu()        
      }, 100);            
    },
    itemscope( p: any) {
      return (p && p.scopes) || false
    },
    isCurrentMenu (m: any) {
      return m == this.$route.name
    },
    logoutfunc () {
      authStore.logout()
    },
    iconme(p: any) {
      return p && p.icon
    },
    pathme (p: any) {
      return p && p.pathname
    },
    ptitle (p: any) {
      return p && p.title
    },
    hasScopes (p: any) {
      if (!p) {
        return true
      } else {
        let g: boolean = false
        if (this.scopes) {
          if (Array.isArray(p)) {
            for (let k = 0; k < this.scopes.length; k++) {
              if (p.indexOf(this.scopes[k]) > -1) {
                return true
              }
            }
          } else {
            return this.scopes.indexOf(p) > -1
          }          
        }
        return g
      }
    },
    canViewMenu (menu: any) {
      let profile: any = auth2Store.getState().profile
      if (menu.requireSupportL1) {
        if (profile.supportL1 && profile.supportL1 == true) {
          return true;
        } else {
          return false
        }
      } else if (menu.requireSupportL1 == false) {
        if (profile.supportL1 && profile.supportL1 == true) {
          return false;
        } else {
          return true
        }
      }

      return true;
    }
  },
  watch: {
    logoutSuccess (p: any) {
      if (p) {
        this.$router.push({ name: 'home' })
      }
    },
    logoutError (p: any) {
      if (p) {}
    }
  },
  computed: {
    scopes () {
      let s: any = auth2Store.getState().profile
      return  s && s.scopes 
    },
    devlist () {
      return [
        {
          pathname: 'ipaytest',
          title: 'navigations.ipaypayments',
          icon: 'invoice'
        },
        {
          pathname: 'paytest',
          title: 'navigations.paytest',
          icon: 'invoice'
        },
        {
          pathname: 'tailwindfix',
          title: 'navigations.tailwindfix',
          icon: 'invoice'
        }
      ]
    },
    authStoreState () {
      return authStore.getState()
    },
    isDev () {
      let p: any = this.scopes
      return p && p.includes('dev')
    },
  }
})
</script>
