<template>
  <button
      @click="toggleMobileMenu"
      :class="authState.boolMobileMenu ? 'z-50' : ''"
      class="hidden flex items-center rounded focus:outline-none absolute top-5 right-5 z-20">
    <svg class="text-blue-500 bg-blue-100 block h-8 w-8 p-2 rounded" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
      <title>Mobile menu</title>
      <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"></path>
    </svg>
  </button>
</template>
<script>
import { defineComponent, computed, inject, onBeforeMount } from 'vue'
export default defineComponent({
  setup() {
      const authStore = inject("authStore")
      onBeforeMount(async () => await authStore.init())
      const authState = authStore.getState()
      return {
          authStore: authStore,
          authState
      }
  },
  methods: {
    toggleMobileMenu () {
      this.authStore.setMobileMenu()
    }
  }
})
</script>
