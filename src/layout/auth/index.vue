<template>
  <dheader></dheader>
  <sidebar
      :adminlist="menulist.auth.admin"
      :customerlist="menulist.auth.customer"
      :boolAdmin="true"
      :boolLogin="true"
      class="hidden lg:block"></sidebar>
  <sidebar
      :adminlist="menulist.auth.admin"
      :customerlist="menulist.auth.customer"
      v-if="authStoreState.boolMobileMenu"
      :boolAdmin="true"
      :boolLogin="true"
      class="block lg:hidden"></sidebar>
  <div class="mx-auto lg:ml-80 pb-5 min-h-screen" style="background-color: #f1f5fb;">
    <router-view />
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import dheader from './header.vue'
import menulist from '../components/menulist'
import sidebar from '../components/sidebar.vue'
import { authStore } from "@/store/auth-store"
export default defineComponent({
  components: {
    dheader,
    sidebar
  },
  data () {
    return {
      menulist
    }
  },
  computed: {
    authStoreState () {
      return authStore.getState()
    }
  }
})
</script>
