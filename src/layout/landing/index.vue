<template>
  <div class="h-screen" style="background-color: #f1f5fb;">
    <dheader
        :adminlist="menulist.landing"
        :profile="profile || {}"></dheader>
    <sidebar
        :adminlist="menulist.landing.default"
        :boolLogin="boolLogin"
        v-if="authState.boolMobileMenu"></sidebar>
    <div class="p-2">
        <router-view />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject, onBeforeMount } from 'vue'
import dheader from './header.vue'
import menulist from '../components/menulist'
import sidebar from '../components/sidebar.vue'
import { auth2Store } from '../../store/auth2-store'

export default defineComponent({
  setup() {
      const authStore:any = inject("authStore")
      onBeforeMount(async () => await authStore.init())
      const auth2State = auth2Store.getState()
      const authState = authStore.getState()
      return {
          authStore: authStore,
          authState,
          profile: computed(() => auth2State.profile),
      }
  },
  components: {
    dheader,
    sidebar
  },
  data () {
    let boolLogin = false
    return {
      menulist,
      boolLogin
    }
  }
})
</script>
