import { nextTick } from 'vue'
import { createI18n } from 'vue-i18n'

import type { I18n, I18nOptions, Composer } from 'vue-i18n'

export const SUPPORT_LOCALES = ['en-US', 'ms-MY', 'zh-CN']

export function setupI18n(options: I18nOptions = { locale: 'en-US' }): I18n {
  const i18n = createI18n(options) as I18n
  setI18nLanguage(i18n, options.locale!)
  return i18n
}

export function setI18nLanguage(i18n: I18n, locale: string): void {
  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale
  } else {
    ;((i18n.global as unknown) as Composer).locale.value = locale
  }
  const htmlObj = document.querySelector('html')
  if (htmlObj) {
    htmlObj.setAttribute('lang', locale)
  }    
}

export async function loadLocaleMessages(i18n: I18n, locale: string) {
  // load locale messages
  const messages = await import(/* @vite-ignore */ `./locales/${locale}.yaml`)

  // set locale and locale message
  i18n.global.setLocaleMessage(locale, messages.default)

  return nextTick()
}