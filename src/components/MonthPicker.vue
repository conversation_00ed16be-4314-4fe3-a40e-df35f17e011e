<template>
    <div class="relative" v-if="refresh">
      <Datepicker
          inputFormat="dd-MM-yyyy"
          v-model="dateitem"
          :disabled="disabled"
          class="border-0 p-4 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full"
          :class="'focus:ring-' + defaultColor + '-400'"
          :starting-view="'month'"
          :minimum-view="'month'"
          :placeholder="$t('c.selectMonth')"/>
      <span
          v-if="clearable && dateitem !== undefined && !disabled"
          @click="clearValueFunc"
          title="Clear"
          class="absolute top-1/2 transform -translate-y-1/2 right-4 cursor-pointer">
        <svg version="1.1" class="w-4 h-4 text-gray-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
             viewBox="0 0 252 252" xml:space="preserve">
            <path stroke="currentColor" d="M126,0C56.523,0,0,56.523,0,126s56.523,126,126,126s126-56.523,126-126S195.477,0,126,0z M126,234
                c-59.551,0-108-48.449-108-108S66.449,18,126,18s108,48.449,108,108S185.551,234,126,234z"/>
            <path stroke="currentColor" d="M164.612,87.388c-3.515-3.515-9.213-3.515-12.728,0L126,113.272l-25.885-25.885c-3.515-3.515-9.213-3.515-12.728,0
                c-3.515,3.515-3.515,9.213,0,12.728L113.272,126l-25.885,25.885c-3.515,3.515-3.515,9.213,0,12.728
                c1.757,1.757,4.061,2.636,6.364,2.636s4.606-0.879,6.364-2.636L126,138.728l25.885,25.885c1.757,1.757,4.061,2.636,6.364,2.636
                s4.606-0.879,6.364-2.636c3.515-3.515,3.515-9.213,0-12.728L138.728,126l25.885-25.885
                C168.127,96.601,168.127,90.902,164.612,87.388z"/>
        </svg>
      </span>
    </div>
  </template>
  <script lang="ts">
  import { defineComponent } from 'vue'
  import Datepicker from 'vue3-datepicker'
  export default defineComponent({
    props: {
      modelValue: [Date, String],
      disabled: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: false
      },
      boolSetToday: {
        type: Boolean,
        default: true
      },
      todayText: {
        type: String,
        default: 'Today'
      },
      defaultColor: {
          type: String,
          default: 'green'
      }
    },
    components: {
      Datepicker
    },
    mounted () {
      this.updateDateItem(this.modelValue)
    },
    data () {
      let dateitem: any = undefined
      let refresh: boolean = true
      return {
        dateitem,
        refresh
      }
    },
    watch: {
      dateitem (p) {
        if (p && p.toString().trim().length > 0) {
          this.$emit('update:modelValue', p)
        }
      },
      modelValue (p) {
        this.updateDateItem(p)
      }
    },
    methods: {
      refreshFunc () {
        this.refresh = false
        this.$nextTick(()=> {
          this.refresh = true
        })
      },
      clearValueFunc () {
        this.dateitem = undefined
        this.$emit('update:modelValue', undefined)
      },
      updateDateItem(value: any) {
        if (typeof value === 'string') {
          this.dateitem = new Date(value)
        } else {
          this.dateitem = value
        }
        this.refreshFunc()
      }
    }
  })
  </script>
  