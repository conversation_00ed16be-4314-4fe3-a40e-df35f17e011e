<template>
  <div class="relative">
    <div class="flex items-center">
      <div class="mr-3 text-right">
        <p class="text-sm">{{profile.name || profile.email}}</p>
        <p class="text-sm text-gray-400">{{profile && profile.customer ? $t('c.customer') : $t('c.admin') }}</p>
      </div>
      <div class="mr-2">
        <savatar class="cursor-pointer" @click="toggleMenu" :email="profile.email" :size="40" />
      </div>
    </div>
    <ul v-if="boolLoggedMenu" class="absolute shadow border rounded-lg py-1 px-2 right-0 top-12 text-right bg-white z-50">
      <li :key="String(loggedindex)" v-for="(loggeditem, loggedindex) in menulist.auth.profile">
        <button @click="goto(loggeditem.pathname)" class="bg-white px-6 py-0.5 my-1 hover:bg-blue-500 hover:text-white border w-full">
          {{ $t(loggeditem.title) }}
        </button>
      </li>
    </ul>
  </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject, onBeforeMount } from 'vue'
import { auth2Store } from '../store/auth2-store'
import menulist from '../layout/components/menulist'
import savatar from'./savatar.vue'
export default defineComponent({
  setup() {
      const authStore: any = inject("authStore")
      const auth2State = auth2Store.getState()
      return {
          authStore: authStore,
          profile: computed(() => auth2State.profile )
      }
  },
  mounted () {
    
  },
  components: {
    savatar,
  },
  data () {
    let m: any = menulist
    return {
      boolLoggedMenu: false,
      menulist: m
    }
  },
  methods: {
    toggleMenu () {
      this.boolLoggedMenu = !this.boolLoggedMenu
    },
    goto (pathname: any) {
      if (pathname === 'logout') {
        if (this.authStore) {
            this.authStore.logout()
        }
      } else {
        this.$router.push({ name: pathname })
      }
    }
  }
})
</script>
