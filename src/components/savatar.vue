<template>
    <div class="inline-block uppercase font-bold" :style="`width: ${size}px;height: ${size}px; padding-top: ${(size/2) - (fontsize - fontsize/3)}px; font-size: ${fontsize}px; color: ${txtColor}; border-radius: ${size/2}px; background: ${bgColor};`">
        <div class=" dcenter">{{email[0]}}</div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
    props: {
        email: {type: String, default: ''},
        size: {type: Number, default: 100},
        bgColor: {type: String, default: '#000'},
        txtColor: {type: String, default: '#fff'},
        fontsize: {type: Number, default: 16}
    },
})
</script>
<style scoped>
.dcenter {
  margin: auto;
  width: 50%;
  text-align: center;
}
</style>