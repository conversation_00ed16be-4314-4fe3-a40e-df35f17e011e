<template>
  <section class="py-5 px-6 bg-white shadow">
    <nav class="relative">
      <div class="flex items-center justify-between">
        <div class="flex items-center mr-auto">
          <button @click="toggleMobileMenu" class="flex items-center">
            <span class="flex justify-center items-center mr-3 w-10 h-10 bg-blue-500 text-sm text-white rounded-full">EP</span>
            <button @click="backFunction" v-if="backButton" class="flex items-center gap-1 text-sm font-medium mr-2 bg-gray-500 hover:bg-gray-700 text-white px-2 py-1 rounded shadow">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
              </svg>
              {{ backTitle }}
            </button>
            <p class="text-sm font-medium mr-2">{{title}}</p>
            <span class="inline-block -mb-px">
              <svg class="text-gray-500" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.8596 9.52667L7.99958 12.3933L5.13958 9.52667C5.01404 9.40114 4.84378 9.33061 4.66624 9.33061C4.48871 9.33061 4.31845 9.40114 4.19291 9.52667C4.06738 9.65221 3.99685 9.82247 3.99685 10C3.99685 10.1775 4.06738 10.3478 4.19291 10.4733L7.52624 13.8067C7.65115 13.9308 7.82012 14.0005 7.99624 14.0005C8.17237 14.0005 8.34134 13.9308 8.46624 13.8067L11.7996 10.4733C11.8617 10.4112 11.911 10.3374 11.9447 10.2562C11.9783 10.175 11.9956 10.0879 11.9956 10C11.9956 9.9121 11.9783 9.82505 11.9447 9.74384C11.911 9.66262 11.8617 9.58883 11.7996 9.52667C11.7374 9.46451 11.6636 9.41521 11.5824 9.38157C11.5012 9.34793 11.4142 9.33061 11.3262 9.33061C11.2383 9.33061 11.1513 9.34793 11.0701 9.38157C10.9889 9.41521 10.9151 9.46451 10.8529 9.52667H10.8596ZM5.13958 6.47334L7.99958 3.60667L10.8596 6.47334C10.9845 6.59751 11.1535 6.6672 11.3296 6.6672C11.5057 6.6672 11.6747 6.59751 11.7996 6.47334C11.9237 6.34843 11.9934 6.17946 11.9934 6.00334C11.9934 5.82722 11.9237 5.65825 11.7996 5.53334L8.46624 2.20001C8.40427 2.13752 8.33053 2.08792 8.2493 2.05408C8.16806 2.02023 8.08092 2.00281 7.99291 2.00281C7.9049 2.00281 7.81777 2.02023 7.73653 2.05408C7.65529 2.08792 7.58155 2.13752 7.51958 2.20001L4.18624 5.53334C4.06159 5.65976 3.99227 5.83052 3.99352 6.00805C3.99477 6.18559 4.06649 6.35535 4.19291 6.48001C4.31933 6.60466 4.49009 6.67398 4.66762 6.67273C4.84516 6.67148 5.01493 6.59976 5.13958 6.47334Z" fill="currentColor"></path>
              </svg>
            </span>
          </button>
        </div>
        <div class="flex justify-end items-center space-x-4">
          <button v-if="addFunction" @click="addFuncMethod" class="flex items-center py-2 px-2 rounded-full bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium" href="#">
            <svgCollection icon="add-circle"></svgCollection>
          </button>
          <button v-if="reloadFunction" @click="reloadFuncMethod" class="flex items-center py-2 px-2 rounded-full bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium" href="#">
            <svgCollection icon="reload"></svgCollection>
          </button>
          <userpicheader></userpicheader>
        </div>
      </div>
    </nav>
  </section>
</template>
<script>
import { defineComponent, inject, onBeforeMount } from 'vue'
import userpicheader from '@/components/userpicheader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
  props: {
    title: {
      type: String,
      default: 'Example Project'
    },
    backButton: {
      type: Boolean,
      default: false
    },
    backTitle: {
      type: String,
      default: 'Back'
    },
    backFunction: Function,
    addFunction: Function,
    reloadFunction: Function
  },
  components: {
    userpicheader,
    svgCollection
  },
  setup() {
    const authStore = inject("authStore")
    onBeforeMount(async () => await authStore.init())
    const authState = authStore.getState()
    return {
        authStore: authStore,
        authState
    }
  },
  methods: {
    addFuncMethod (e) {
        this.addFunction(e)
    },
    reloadFuncMethod (e) {
        this.reloadFunction(e)
    },
    toggleMobileMenu () {
      this.authStore.setMobileMenu()
    }
  }
})
</script>
