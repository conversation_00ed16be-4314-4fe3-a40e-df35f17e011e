<template>
  <div>
    <div class="upload">
      <ul>
        <li class="inline-block w-24 my-5 mr-3 text-center" v-for="file in files.filter((g)=> !params(g, 'success'))" :key="params(file, 'id')">
          <div>
            <img class="w-16 h-16 mx-auto object-cover" v-if="params(file, 'thumb')" :src="params(file, 'thumb')" />
            <template v-else>
              <img class="w-16 h-16 mx-auto object-cover" src="/images/paper.png" />
            </template>
          </div>
          <div class="text-xs truncate p-1">{{params(file, 'name')}}</div>
          <div class="truncate" v-if="params(file, 'error')">{{$t('errs.' + params(file,'error'))}}</div>
          <div class="text-xs truncate " v-else-if="params(file, 'success')">{{$t('c.success')}}</div>
          <div class="text-xs truncate " v-else-if="params(file,'active')">{{$t('c.uploading')}}</div>
          <div class="text-xs truncate " v-else-if="!!params(file, 'error')">{{$t('errs.' + params(file, 'error'))}}</div>
          <div class="text-xs truncate bg-gray-500 rounded text-white" v-else>{{$t('c.pending')}}</div>
          <div class="mt-2 text-xs text-red-600 text-right">
            <button
                @click.prevent="removeFile($refs.upload, file)"
                title="Remove"
                class="hover:text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </li>
      </ul>
      <div v-if="files.length > 0" class="m-1 text-right">
        <button type="button" class="bg-green-500 py-1 text-xs px-2 rounded text-white" v-if="!$refs.upload || !params($refs.upload, 'active')" @click.prevent="setParams2($refs.upload, 'active', true)">
          {{$t('upload.start')}}
        </button>
        <button type="button" class="bg-red-500 py-1 text-xs px-2 rounded text-white"  v-else @click.prevent="setParams2($refs.upload, 'active', false)">
          {{$t('upload.stop')}}
        </button>
      </div>
      <div v-if="inputFile" class="bg-gray-100 w-full block rounded text-center cursor-pointer h-50">
        <file-upload
          :input-id="type"
          class="w-full"
          :style="'height: ' + uploadheightpx + 'px;'"
          :drop="true"
          :post-action="basePath"
          :headers="{'Authorization': 'Bearer ' + token}"
          extensions="gif,jpg,jpeg,svg,png,xls,xlsx,webp,pdf,doc,docx,ppt,pptx"
          accept="image/png,image/gif,image/svg+xml,image/jpeg,image/webp,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-powerpoint"
          :multiple="multiple"
          :size="1024 * 1024 * 100"
          v-model="files"
          @input-filter="inputFilter"
          @input-file="inputFile"
          ref="upload">
          <div
              class="text-sm italic text-gray-500"
              :style="'margin-top: ' + textmtpx + 'px;'">
            <div class="mb-2">{{$t('upload.dropfiletoupload')}}</div>
            <div class="mb-2">{{$t('upload.or')}}</div>
            <div class="mb-2">{{$t('upload.selectfiles')}}</div>
          </div>
        </file-upload>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {ref, defineComponent} from 'vue'
import FileUpload from 'vue-upload-component'
export default defineComponent({
  components: {
    FileUpload,
  },
  props: {
    token: { type: String },
    addFile: { type: Function, required: true },
    basePath: { type: String, required: true },
    multiple: { type: Boolean, default: true },
    uploadheightpx: { type: String, default: '165' },
    textmtpx: { type: String, default: '48' },
    type: {type: String}
  },
  data () {
    return {
      files: [],
      inswo: [],
      insst: [],
      insfws: []
    }
  },
  methods: {
    setParams (p: any, i: any) {
      p = i
    },
    setParams2 (p: any, q: any, i: any) {
      p[q] = i
    },
    params (p: any, i: any) {
      return p[i]
    },
    removeFile(f: any, file: any) {
      f.remove(file)
    },
    inputFilter(newFile: any, oldFile: any, prevent: any) {
      if (newFile && !oldFile) {
        if (!(/\.(gif?|jpg?|jpeg?|svg?|xlsx?|xls?|csv?|png?|webp?|pdf?|doc?|docx?|ppt?|pptx?)$/i.test(newFile.name))) {
          return prevent()
        }

        if (/\.(gif?|jpg?|jpeg?|png?|webp?)$/i.test(newFile.name)) {
          if (newFile && newFile.error === "" && newFile.file && (!oldFile || newFile.file !== oldFile.file)) {
            newFile.blob = ''
            let URL = (window.URL || window.webkitURL)
            if (URL) {
              newFile.blob = URL.createObjectURL(newFile.file)
            }
            newFile.thumb = ''
            if (newFile.blob && newFile.type.substr(0, 6) === 'image/') {
              newFile.thumb = newFile.blob
            }
          }
        }
      }
    },
    inputFile(newFile: any, oldFile: any) {
      const addFile : any = this.addFile
      if (newFile && oldFile) {
        if (newFile.success) {
          addFile(newFile.response)
        }
      }
      if (!newFile && oldFile) {
        // remove
        console.log('remove', oldFile)
      }
    }
  },
  setup(props: any, context: any) {
    const upload = ref(null)
    return {
      upload,
    }
  }
})
</script>
