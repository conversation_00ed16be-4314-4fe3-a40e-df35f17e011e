<template>
  <div>
    <div class="upload">
      <ul>
        <li class="inline-block w-24 my-5 mr-3 text-center" v-for="file in files.filter((g)=> !g.success)" :key="file.id">
          <div>
            <img class="w-16 h-16 mx-auto object-cover" v-if="file.thumb" :src="file.thumb" />
            <template v-else>
              <img class="w-16 h-16 mx-auto object-cover" src="/images/paper.png" />
            </template>
          </div>
          <div class="text-xs truncate p-1">{{file.name}}</div>
          <div class="truncate" v-if="file.error">{{$t('errs.' + file.error)}}</div>
          <div class="text-xs truncate " v-else-if="file.success">{{$t('c.success')}}</div>
          <div class="text-xs truncate " v-else-if="file.active">{{$t('c.uploading')}}</div>
          <div class="text-xs truncate bg-gray-500 rounded text-white" v-else>{{$t('c.pending')}}</div>
          <div class="mt-2 text-xs text-red-600 text-right">
            <button
                @click.prevent="removeFile(file)"
                title="Remove"
                class="hover:text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </li>
      </ul>
      <div v-if="files.length > 0" class="m-1 text-right">
        <button type="button" class="bg-green-500 py-1 text-xs px-2 rounded text-white" v-if="!isUploading" @click.prevent="startUpload">
          {{$t('upload.start')}}
        </button>
        <button type="button" class="bg-red-500 py-1 text-xs px-2 rounded text-white" v-else @click.prevent="stopUpload">
          {{$t('upload.stop')}}
        </button>
      </div>
      <div class="bg-gray-100 w-full block rounded text-center cursor-pointer h-50">
        <div
          class="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
          :style="'height: ' + uploadheightpx + 'px;'"
          @drop="onDrop"
          @dragover.prevent
          @dragenter.prevent
          @click="triggerFileInput">
          <input
            ref="fileInput"
            type="file"
            :multiple="multiple"
            :accept="acceptTypes"
            @change="onFileSelect"
            class="hidden">
          <div
            class="text-sm italic text-gray-500 flex flex-col justify-center h-full"
            :style="'margin-top: ' + textmtpx + 'px;' + 'margin-bottom: ' + textmtpx + 'px;'">
            <div class="mb-2">{{$t('upload.dropfiletoupload')}}</div>
            <div class="mb-2">{{$t('upload.or')}}</div>
            <div class="mb-2">{{$t('upload.selectfiles')}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>

</style>

<script lang="ts">
import {ref, defineComponent} from 'vue'
import axios from 'axios'

interface FileItem {
  id: string
  name: string
  file: File
  thumb?: string
  error?: string
  success?: boolean
  active?: boolean
  response?: any
}

export default defineComponent({
  props: {
    token: { type: String },
    addFile: { type: Function, required: true },
    basePath: { type: String, required: true },
    multiple: { type: Boolean, default: true },
    uploadheightpx: { type: String, default: '165' },
    textmtpx: { type: String, default: '48' }
  },
  setup(props: any) {
    const fileInput = ref<HTMLInputElement | null>(null)
    const files = ref<FileItem[]>([])
    const isUploading = ref(false)

    const acceptTypes = "image/png,image/gif,image/svg+xml,image/jpeg,image/webp,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-powerpoint"

    const generateId = () => {
      return Math.random().toString(36).substring(2, 11)
    }

    const isValidFile = (file: File): boolean => {
      const validExtensions = /\.(gif|jpg|jpeg|svg|xlsx|xls|csv|png|webp|pdf|doc|docx|ppt|pptx)$/i
      return validExtensions.test(file.name) && file.size <= 1024 * 1024 * 100 // 100MB limit
    }

    const createFileItem = async (file: File): Promise<FileItem> => {
      const fileItem: FileItem = {
        id: generateId(),
        name: file.name,
        file: file,
        active: false,
        success: false
      }

      // Create thumbnail for images
      if (/\.(gif|jpg|jpeg|png|webp)$/i.test(file.name)) {
        try {
          const URL = window.URL || window.webkitURL
          if (URL) {
            fileItem.thumb = URL.createObjectURL(file)
          }
        } catch (error) {
          console.warn('Could not create thumbnail:', error)
        }
      }

      return fileItem
    }

    const addFiles = async (fileList: FileList) => {
      const newFiles: FileItem[] = []

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i]
        if (isValidFile(file)) {
          const fileItem = await createFileItem(file)
          newFiles.push(fileItem)
        }
      }

      if (props.multiple) {
        files.value.push(...newFiles)
      } else {
        files.value = newFiles.slice(0, 1)
      }
    }

    const removeFile = (fileToRemove: FileItem) => {
      const index = files.value.findIndex(f => f.id === fileToRemove.id)
      if (index > -1) {
        // Revoke object URL to prevent memory leaks
        if (fileToRemove.thumb) {
          URL.revokeObjectURL(fileToRemove.thumb)
        }
        files.value.splice(index, 1)
      }
    }

    const uploadFile = async (fileItem: FileItem): Promise<void> => {
      const formData = new FormData()
      formData.append('file', fileItem.file)

      const headers: any = {
        'Content-Type': 'multipart/form-data'
      }

      if (props.token) {
        headers['Authorization'] = `Bearer ${props.token}`
      }

      try {
        fileItem.active = true
        fileItem.error = undefined

        const response = await axios.post(props.basePath, formData, {
          headers
        })

        fileItem.active = false
        fileItem.success = true
        fileItem.response = response.data

        // Call the addFile callback
        if (props.addFile) {
          props.addFile(response.data)
        }
      } catch (error: any) {
        fileItem.active = false
        fileItem.error = error.response?.data?.message || 'upload_failed'
        console.error('Upload failed:', error)
      }
    }

    const startUpload = async () => {
      isUploading.value = true
      const pendingFiles = files.value.filter(f => !f.success && !f.active)

      for (const file of pendingFiles) {
        if (!isUploading.value) break // Stop if user clicked stop
        await uploadFile(file)
      }

      isUploading.value = false
    }

    const stopUpload = () => {
      isUploading.value = false
    }

    const triggerFileInput = () => {
      fileInput.value?.click()
    }

    const onFileSelect = (event: Event) => {
      const target = event.target as HTMLInputElement
      if (target.files && target.files.length > 0) {
        addFiles(target.files)
        target.value = '' // Reset input
      }
    }

    const onDrop = (event: DragEvent) => {
      event.preventDefault()
      if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
        addFiles(event.dataTransfer.files)
      }
    }

    return {
      fileInput,
      files,
      isUploading,
      acceptTypes,
      removeFile,
      startUpload,
      stopUpload,
      triggerFileInput,
      onFileSelect,
      onDrop
    }
  }
})
</script>
