<template>
    <div class="relative">
        <form onsubmit="return false">
            <div class="flex justify-between items-center" :class="inputClass" v-if="modelValue && modelValue.length > 0">
                <template v-if="item">
                    {{ itemValueFunc(item) }}
                </template>
                <template v-else>
                    ...{{ $t('c.loading') }}...
                </template>
                <span v-if="!readonly" @click="clearSelect" title="Clear"
                    class="h-full cursor-pointer flex items-center pl-2">
                    <svgcollection icon="x" dclass="h-5 w-5 text-red-400 hover:text-red-700" />
                </span>
            </div>

            <template v-else>
                <div v-if="readonly" class="px-2 shadow py-3 pb-4 rounded text-center text-gray-400">
                    - - - -
                </div>
                <div class="block relative w-full mb-2" v-else>
                    <span @click="searchNow" title="Search"
                        class="h-full cursor-pointer absolute inset-y-0 left-0 flex items-center pl-2">
                        <svg viewBox="0 0 24 24" class="h-4 w-4 fill-current text-gray-500">
                            <path
                                d="M10 4a6 6 0 100 12 6 6 0 000-12zm-8 6a8 8 0 1114.32 4.906l5.387 5.387a1 1 0 01-1.414 1.414l-5.387-5.387A8 8 0 012 10z">
                            </path>
                        </svg>
                    </span>
                    <input type="search" autocomplete="off" v-on:keyup.enter="searchNow" v-on:input="searchNow" v-on:focus="onFocus"
                        name="keywords" v-model="keywords" class="pl-8" :class="inputClass" :placeholder="$t(placeHolder)"
                        style="transition: all 0.15s ease 0s;" />
                </div>
            </template>
            
            <!-- Dropdown positioned absolutely -->
            <div class="absolute top-full left-0 right-0 z-20 bg-white p-1 shadow border" v-if="items && !isLoading">
                <template v-if="items.length > 0">
                    <template v-for="p, ip in items" :key="String(ip)">
                        <div @click.prevent="selectItem(p)"
                            class="text-center cursor-pointer text-gray-800 border-b bg-white hover:bg-blue-100 mb-1 text-sm py-2">
                            {{ itemValueFunc(p) }}
                        </div>
                    </template>
                </template>
                <template v-else>
                    <div class="text-center shadow bg-white mb-1 text-sm py-1">{{ $t('c.noItems') }}</div>
                </template>
                <div @click.prevent="clear"
                    class="cursor-pointer text-center text-xs bg-gray-100 py-3 hover:bg-gray-200 shadow text-red-600">
                    {{ $t('c.clear') }}</div>
            </div>
            
            <!-- Loading dropdown positioned absolutely -->
            <div class="absolute top-full left-0 right-0 z-20 bg-white p-1 shadow border" v-else-if="isLoading">
                <div class="text-center text-gray-800 text-sm bg-white mb-1 py-2">
                    Loading...
                </div>
            </div>
        </form>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import svgcollection from '@/components/cvui/svgcollection.vue'
export default defineComponent({
    props: {
        modelValue: { type: String },
        getItem: { type: Function, required: true },
        itemValue: { type: Function, required: true },
        itemId: { type: Function },
        searchItems: { type: Function, required: true },
        readonly: { type: Boolean, default: false },
        cclearSelect: { type: Boolean, default: false },
        placeHolder: { type: String, default: 'c.searchNow' },
        inputClass: { type: String, default: 'w-full px-2 py-2 border rounded' },
    },
    mounted() {
        if (this.modelValue && this.modelValue.trim().length > 0 && this.getItem) {
            let gp = this.getItem(this.modelValue)
            if (gp) {
                gp.then((rs: any) => {
                    if (rs) {
                        if (rs.data) {
                            this.item = rs.data
                        } else {
                            this.item = rs
                        }
                    } else {
                        this.clearSelect()
                    }
                }).catch(() => {
                    this.clearSelect()
                })
            }
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', this.handleClickOutside)
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside)
    },
    watch: {
        modelValue(p) {
            if (p) {
                if (!this.item || this.item.id != p) {
                    let gp = this.getItem(p)
                    if (gp) {
                        gp.then((rs: any) => {
                            if (rs && rs.data) {
                                this.item = rs.data
                            }
                        })
                    }
                }
            }
        }
    },
    methods: {
        handleClickOutside(event: Event) {
            if (!this.$el.contains(event.target as Node)) {
                this.clear()
            }
        },
        itemIdFunc(p: any) {
            if (this.itemId) {
                return this.itemId(p)
            } else {
                return p.id || p._id
            }
        },
        itemValueFunc(p: any) {
            if (this.itemValue) {
                return this.itemValue(p)
            } else {
                return p.title || p.name
            }
        },
        onFocus() {
            // Show initial results when user focuses on the input
            if (!this.items && !this.isLoading) {
                this.searchNow()
            }
        },
        searchNow() {
            this.isLoading = true
            this.items = undefined
            if (this.searchItems) {
                this.searchItems(this.keywords).then((ps: any) => {
                    if (ps) {
                        this.items = ps.data
                        this.isLoading = false
                    } else {
                        this.items = false
                        this.isLoading = false
                    }
                })
            }
        },
        clearSelect() {
            this.$emit('update:modelValue', '')
        },
        clear() {
            this.items = undefined
            this.keywords = ''
        },
        selectItem(p: any) {
            this.item = p
            this.$emit('update:modelValue', this.itemIdFunc(p))
            this.clear()
            if (this.cclearSelect) {
                setTimeout(() => {
                    this.item = undefined
                    this.$emit('update:modelValue', '')
                }, 100)
            }
        }
    },
    data() {
        let item: any = undefined
        let items: any = undefined
        let isLoading: boolean = false
        return {
            keywords: '',
            item,
            items,
            isLoading
        }
    },
    components: {
        svgcollection,
    }
})
</script>