<template>
  <div
      class="border-0 px-4 pb-2.5 pt-0.5 placeholder-gray-400 text-gray-700 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full"
      :class="'focus:ring-' + defaultColor + '-400'">
    <div class="flex items-center justify-between">
      <div class="flex items-end">
        <div>
          <small class="block">{{dateText}}</small>
          <Datepicker
              inputFormat="dd-MM-yyyy"
              v-model="dateitem"
              :disabled="disabled"
              :placeholder="selectDateText"
              style="width:100px;" />
        </div>
        <div>
          <small class="block">{{hoursText}}</small>
          <select
              name="hours"
              v-model="hoursitem"
              :disabled="disabled"
              class="w-full bg-transparent appearance-none outline-none"
              :class="disabled ? 'cursor-not-allowed' : 'cursor-pointer'">
            <option :value="undefined" :disabled="disabled">
              {{hoursText}}
            </option>
            <option
                v-for="hrs in 24"
                :key="hrs"
                :value="hrs - 1">
              {{hrs - 1 < 10 ? '0' + (hrs - 1) : hrs - 1}}
            </option>
          </select>
        </div>
        <span class="text-xl mx-3">:</span>
        <div>
          <small class="block">{{minutesText}}</small>
          <select
              name="minutes"
              v-model="minutesitem"
              :disabled="disabled"
              class="w-full bg-transparent appearance-none outline-none mr-4"
              :class="disabled ? 'cursor-not-allowed' : 'cursor-pointer'">
            <option :value="undefined" :disabled="disabled">
              {{minutesText}}
            </option>
            <option
                v-for="mins in 60"
                :key="mins"
                :value="mins - 1">
              {{mins - 1 < 10 ? '0' + (mins - 1) : mins - 1}}
            </option>
          </select>
        </div>
      </div>
      <div class="flex items-end" v-if="!disabled">
        <span v-if="boolSetToday" @click="setDate(new Date(), 'today')" class="cursor-pointer mr-5" :class="'text-' + defaultColor + '-500 hover:text-' + defaultColor + '-700'">{{todayText}}</span>
        <div v-else style="width:36px;"></div>
        <span
            v-if="clearable && fullitem !== undefined"
            @click="clearValueFunc"
            title="Clear"
            class="cursor-pointer">
          <svg version="1.1" class="w-4 h-4 text-gray-600 hover:text-gray-400" fill="currentColor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
             viewBox="0 0 252 252" xml:space="preserve">
            <path stroke="currentColor" d="M126,0C56.523,0,0,56.523,0,126s56.523,126,126,126s126-56.523,126-126S195.477,0,126,0z M126,234
              c-59.551,0-108-48.449-108-108S66.449,18,126,18s108,48.449,108,108S185.551,234,126,234z"/>
            <path stroke="currentColor" d="M164.612,87.388c-3.515-3.515-9.213-3.515-12.728,0L126,113.272l-25.885-25.885c-3.515-3.515-9.213-3.515-12.728,0
              c-3.515,3.515-3.515,9.213,0,12.728L113.272,126l-25.885,25.885c-3.515,3.515-3.515,9.213,0,12.728
              c1.757,1.757,4.061,2.636,6.364,2.636s4.606-0.879,6.364-2.636L126,138.728l25.885,25.885c1.757,1.757,4.061,2.636,6.364,2.636
              s4.606-0.879,6.364-2.636c3.515-3.515,3.515-9.213,0-12.728L138.728,126l25.885-25.885
              C168.127,96.601,168.127,90.902,164.612,87.388z"/>
          </svg>
        </span>
        <div v-else style="width:20px;"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import Datepicker from 'vue3-datepicker'
export default defineComponent({
  props: {
    modelValue: [Date, String],
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    boolSetToday: {
      type: Boolean,
      default: true
    },
    todayText: {
      type: String,
      default: 'Today'
    },
    dateText: {
      type: String,
      default: 'Date'
    },
    selectDateText: {
      type: String,
      default: 'Select Date'
    },
    hoursText: {
      type: String,
      default: 'Hours'
    },
    minutesText: {
      type: String,
      default: 'Minutes'
    },
    defaultColor: {
      type: String,
      default: 'green'
    }
  },
  components: {
    Datepicker
  },
  mounted () {
    if (this.modelValue && this.modelValue.toString().trim().length > 0) {
      this.setDate(this.modelValue)
    }
  },
  data () {
    let fullitem: any = undefined
    let dateitem: any = undefined
    let hoursitem: any = undefined
    let minutesitem: any = undefined
    return {
      fullitem,
      dateitem,
      hoursitem,
      minutesitem
    }
  },
  watch: {
    dateitem (p) {
      if (p && p.toString().trim().length > 0) {
        this.fullitem = p
      }
    },
    hoursitem (p) {
      if (p && p.toString().trim().length > 0) {
        this.fullitem.setHours(p)
      }
    },
    minutesitem (p) {
      if (p && p.toString().trim().length > 0) {
        this.fullitem.setMinutes(p)
      }
    },
    fullitem (p) {
      if ((p && p.toString().trim().length > 0) && (this.dateitem && this.dateitem.toString().trim().length > 0)) {
        this.$emit('update:modelValue', p)
      } else {
        this.$emit('update:modelValue', undefined)
      }
    },
    modelValue (p) {
      if (this.dateitem === null || this.dateitem === '') {
        this.dateitem = new Date(p)
      }
    }
  },
  methods: {
    setDate (indate: any, type = 'model') {
      let date = new Date(new Date(indate).toString().split('GMT')[0]+' UTC').toISOString()
      if (Number(date.toString().substring(0,4)) > 1000) {
        this.fullitem = new Date(date)
        this.dateitem = this.fullitem
        if (type === 'today') {
          this.hoursitem = '0'
          this.minutesitem = '0'
        } else {
          this.hoursitem = this.fullitem.getHours()
          this.minutesitem = this.fullitem.getMinutes()
        }
      }
    },
    clearValueFunc () {
      this.fullitem = undefined
      this.dateitem = undefined
      this.hoursitem = undefined
      this.minutesitem = undefined
    }
  }
})
</script>
