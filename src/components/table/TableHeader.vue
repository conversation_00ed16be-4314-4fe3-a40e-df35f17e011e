<template>
  <div class="flex flex-wrap items-center justify-between py-8 px-6 bg-white w-full -ml-2 -mt-2">
    <div class="w-full lg:w-auto flex items-center mb-4 lg:mb-0">
      <h2 class="text-2xl font-bold">{{title}}</h2>
      <!-- <span class="inline-block py-1 px-2 ml-2 rounded-full text-xs text-white bg-blue-500">1 Unpaid</span> -->
    </div>
    <div class="text-right">
      <button v-if="addFunction" @click="addFuncMethod" class="flex items-center py-2 px-4 rounded bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium" href="#">
        <span class="inline-block mr-1">
          <svg class="h-4 w-4 text-blue-300" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.6667 1.33334H3.33333C2.19999 1.33334 1.33333 2.20001 1.33333 3.33334V12.6667C1.33333 13.8 2.19999 14.6667 3.33333 14.6667H12.6667C13.8 14.6667 14.6667 13.8 14.6667 12.6667V3.33334C14.6667 2.20001 13.8 1.33334 12.6667 1.33334ZM10.6667 8.66668H8.66666V10.6667C8.66666 11.0667 8.4 11.3333 8 11.3333C7.6 11.3333 7.33333 11.0667 7.33333 10.6667V8.66668H5.33333C4.93333 8.66668 4.66666 8.40001 4.66666 8.00001C4.66666 7.60001 4.93333 7.33334 5.33333 7.33334H7.33333V5.33334C7.33333 4.93334 7.6 4.66668 8 4.66668C8.4 4.66668 8.66666 4.93334 8.66666 5.33334V7.33334H10.6667C11.0667 7.33334 11.3333 7.60001 11.3333 8.00001C11.3333 8.40001 11.0667 8.66668 10.6667 8.66668Z" fill="currentColor"></path>
          </svg>
        </span>
        <span>{{$t('c.addnew')}}</span>
      </button>
    </div>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
export default defineComponent({
  props: {
    title: String,
    addFunction: Function
  },
  methods: {
    addFuncMethod (e) {
        this.addFunction(e)
    }
  }
})
</script>
