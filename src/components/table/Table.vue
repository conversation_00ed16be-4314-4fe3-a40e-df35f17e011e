<template>
  <div class="py-6 h-full">
		<div class="container px-4 mx-auto py-6 bg-white shadow rounded">
			<!-- <div class="px-6 border-b">
				<div class="flex flex-wrap items-center mb-6">
					<h3 class="text-xl font-bold">Recent Transactions</h3>
					<a class="ml-auto flex items-center py-2 px-3 text-xs text-white bg-blue-500 hover:bg-blue-600 rounded" href="#">
						<span class="mr-1">
							<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M13 8.33337C12.6 8.33337 12.3333 8.60004 12.3333 9.00004V11.6667C12.3333 12.0667 12.0666 12.3334 11.6666 12.3334H2.33331C1.93331 12.3334 1.66665 12.0667 1.66665 11.6667V9.00004C1.66665 8.60004 1.39998 8.33337 0.99998 8.33337C0.59998 8.33337 0.333313 8.60004 0.333313 9.00004V11.6667C0.333313 12.8 1.19998 13.6667 2.33331 13.6667H11.6666C12.8 13.6667 13.6666 12.8 13.6666 11.6667V9.00004C13.6666 8.60004 13.4 8.33337 13 8.33337ZM4.79998 4.13337L6.33331 2.60004V9.00004C6.33331 9.40004 6.59998 9.66671 6.99998 9.66671C7.39998 9.66671 7.66665 9.40004 7.66665 9.00004V2.60004L9.19998 4.13337C9.46665 4.40004 9.86665 4.40004 10.1333 4.13337C10.4 3.86671 10.4 3.46671 10.1333 3.20004L7.46665 0.533374C7.19998 0.266707 6.79998 0.266707 6.53331 0.533374L3.86665 3.20004C3.59998 3.46671 3.59998 3.86671 3.86665 4.13337C4.13331 4.40004 4.53331 4.40004 4.79998 4.13337Z" fill="#AFABF1"></path>
							</svg>
						</span>
						<span>Export</span>
					</a>
				</div>
				<div><a class="inline-block px-4 pb-2 text-sm font-medium text-blue-500 border-b-2 border-blue-500" href="#">Invoices</a><a class="inline-block px-4 pb-2 text-sm font-medium text-gray-500 border-b-2 border-transparent" href="#">Payments</a></div>
			</div> -->
			<div>
				<table class="table-auto w-full">
					<thead v-if="columns && columns.length > 0">
						<tr class="text-xs text-gray-500 text-left">
							<th class="py-4 font-medium w-5">
                				<input class="mr-3" type="checkbox" name="" id="">
							</th>
							<th v-for="columnitem, ci in columns" 
								:key="String(ci)" 
								class="py-4 font-medium"
								:class="pr(columnitem, 'class')"
							>
								{{$t(pr(columnitem, 'title'))}}
							</th>
						</tr>
					</thead>
					<tbody v-if="data && data.data">
						<tr v-for="data_item, data_index in data.data"
                :key="String(data_index)"
                class="text-xs"
                :class="Number(data_index) % 2 == 0 ? 'bg-gray-50' : 'bg-white'">
							<td class="py-5 font-medium">
								<input class="mr-3" type="checkbox" name="" id="">
							</td>
							<td
                  v-for="(column_item2, column_index2) in columns"
                  :key="column_index2"
                  class="font-medium">
								<p :class="pr(column_item2, 'class')">
                  {{data_item[pr(column_item2, 'key')]}}
                </p>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  props: {
    data: {
      type: Object
    },
    columns: {
      type: Array,
      required: true
    }
  },
  methods: {
	pr(item: any, key: any) {
	  return item[key]
	}
  }
})
</script>
