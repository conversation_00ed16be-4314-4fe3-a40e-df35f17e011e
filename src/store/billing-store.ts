import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getBillings, getBilling, createBilling, updateBilling, deleteBilling } from '../api'
interface Billing extends Object {
    billing: any
    billingSuccess: string
    billingError: string
    billings: any
    billingsSuccess: string
    billingsError: string
    billingCreate: any
    billingCreateSuccess: string
    billingCreateError: string
    billingUpdate: any
    billingUpdateSuccess: string
    billingUpdateError: string
    billingDelete: any
    billingDeleteSuccess: string
    billingDeleteError: string
}

class BillingStore extends Store<Billing> {
    protected data(): Billing {
        return {
            billing: null,
            billingSuccess: '',
            billingError: '',
            billings: null,
            billingsSuccess: '',
            billingsError: '',
            billingCreate: null,
            billingCreateSuccess: '',
            billingCreateError: '',
            billingUpdate: null,
            billingUpdateSuccess: '',
            billingUpdateError: '',
            billingDelete: null,
            billingDeleteSuccess: '',
            billingDeleteError: ''
        }
    }
    billingRequest () {
        this.state.billing = null
        this.state.billingSuccess = ''
        this.state.billingError = ''
    }
    billingsRequest () {
        this.state.billings = null
        this.state.billingsSuccess = ''
        this.state.billingsError = ''
    }
    billingCreateRequest () {
        this.state.billingCreate = null
        this.state.billingCreateSuccess = ''
        this.state.billingCreateError = ''
    }
    billingUpdateRequest () {
        this.state.billingUpdate = null
        this.state.billingUpdateSuccess = ''
        this.state.billingUpdateError = ''
    }
    billingDeleteRequest () {
        this.state.billingDelete = null
        this.state.billingDeleteSuccess = ''
        this.state.billingDeleteError = ''
    }
    async getBilling (data: any)  {
        this.billingRequest()
        return new Promise((resolve, reject) => {
            getBilling(data).then((res: any) => {
                this.state.billing = res
                this.state.billingSuccess = 'billing.successLoadingBilling'
                this.state.billingError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.billingSuccess = ''
                this.state.billingError = 'billing.errLoadingBilling'
            })
        })
    }
    async getBillings (data: any)  {
        this.billingsRequest()
        return new Promise((resolve, reject) => {
            getBillings(data).then((res: any) => {
                this.state.billings = res
                this.state.billingsSuccess = 'billing.successLoadingBillings'
                this.state.billingsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.billingsSuccess = ''
                this.state.billingsError = 'billing.errLoadingBillings'
                reject(err)
            })
        })
    }
    async createBilling (data: any)  {
        this.billingCreateRequest()
        return new Promise((resolve, reject) => {
            createBilling(data).then((res: any) => {
                this.state.billingCreate = {...res.data, "new": true}
                if (!this.state.billings) {
                    this.state.billings = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.billings && this.state.billings.data.length > 0) {
                    this.state.billings.data.push(this.state.billingCreate)
                    this.state.billings.total ++
                }
                this.state.billingCreateSuccess = 'billing.createSuccess'
                this.state.billingCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.billingCreateSuccess = ''
                this.state.billingCreateError = 'billing.createError'
                reject(err)
            })
        })
    }
    async updateBilling (data: any)  {
        this.billingUpdateRequest()
        return new Promise((resolve, reject) => {            
            updateBilling(data).then((res: any) => {
                this.state.billingUpdate = {...res.data, "new": true}
                if (this.state.billings && this.state.billings.data.length > 0) {
                    const p = this.state.billings.data.filter((pp: any) => {
                        return pp.ID == this.state.billingUpdate.id || pp.id == this.state.billingUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.billings.data.indexOf(p[0])
                        this.state.billings.data.splice(i, 1, this.state.billingUpdate)
                    }
                }

                this.state.billingUpdateSuccess = 'billing.updateSuccess'
                this.state.billingUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.billingUpdateSuccess = ''
                this.state.billingUpdateError = 'billing.updateError'
                reject(err)
            })
        })
    }
    async deleteBilling (data: any)  {
        this.billingDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteBilling(data).then((res: any) => {
                this.state.billingDelete = {...res.data, "delete": true}
                if (this.state.billings && this.state.billings.data.length > 0) {
                    const p = this.state.billings.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.billings.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.billings.data.splice(i, 1)
                            this.state.billings.total - 1
                        }
                    }
                }
                this.state.billingDeleteSuccess = 'billing.deleteSuccess'
                this.state.billingDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.billingDeleteSuccess = ''
                this.state.billingDeleteError = 'billing.deleteError'
                reject(err)
            })
        })
    }

}

export const billingStore = new BillingStore(USER_STORE_NAME);
