import {Store} from "./main";
import {CROSS_STORE_NAME} from "./store-names";

interface Cross extends Object {
    modalmsg: any
    notimsg: any
}

class CrossStore extends Store<Cross> {
    protected data(): Cross {
        return {
            modalmsg: null,
            notimsg: null,
        };
    }
    SetModalmsg (modal: any) {
        this.state.modalmsg = modal
    }
    SetNotmsg (msg: any) {
        this.state.notimsg = msg
    }
}

export const crossStore = new CrossStore(CROSS_STORE_NAME);
