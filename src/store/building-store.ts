import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import { getBuildings, getBuilding, createBuilding, updateBuilding } from '../api'
interface Building extends Object {
    building: any
    buildingSuccess: string
    buildingError: string
    buildings: any
    buildingsSuccess: string
    buildingsError: string
    buildingCreate: any
    buildingCreateSuccess: string
    buildingCreateError: string
    buildingUpdate: any
    buildingUpdateSuccess: string
    buildingUpdateError: string
    buildingDelete: any
    buildingDeleteSuccess: string
    buildingDeleteError: string
}

class BuildingStore extends Store<Building> {
    protected data(): Building {
        return {
            building: null,
            buildingSuccess: '',
            buildingError: '',
            buildings: null,
            buildingsSuccess: '',
            buildingsError: '',
            buildingCreate: null,
            buildingCreateSuccess: '',
            buildingCreateError: '',
            buildingUpdate: null,
            buildingUpdateSuccess: '',
            buildingUpdateError: '',
            buildingDelete: null,
            buildingDeleteSuccess: '',
            buildingDeleteError: ''
        }
    }
    buildingRequest () {
        this.state.building = null
        this.state.buildingSuccess = ''
        this.state.buildingError = ''
    }
    buildingsRequest () {
        this.state.buildings = null
        this.state.buildingsSuccess = ''
        this.state.buildingsError = ''
    }
    buildingCreateRequest () {
        this.state.buildingCreate = null
        this.state.buildingCreateSuccess = ''
        this.state.buildingCreateError = ''
    }
    buildingUpdateRequest () {
        this.state.buildingUpdate = null
        this.state.buildingUpdateSuccess = ''
        this.state.buildingUpdateError = ''
    }
    buildingDeleteRequest () {
        this.state.buildingDelete = null
        this.state.buildingDeleteSuccess = ''
        this.state.buildingDeleteError = ''
    }
    async getBuilding (data: any)  {
        this.buildingRequest()
        return new Promise((resolve, reject) => {
            getBuilding(data).then((res: any) => {
                this.state.building = res
                this.state.buildingSuccess = 'building.successLoadingBuilding'
                this.state.buildingError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.buildingSuccess = ''
                this.state.buildingError = 'building.errLoadingBuilding'
            })
        })
    }
    async getBuildings (data: any)  {
        this.buildingsRequest()
        return new Promise((resolve, reject) => {
            getBuildings(data).then((res: any) => {
                this.state.buildings = res
                this.state.buildingsSuccess = 'building.successLoadingBuildings'
                this.state.buildingsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.buildingsSuccess = ''
                this.state.buildingsError = 'building.errLoadingBuildings'
                reject(err)
            })
        })
    }
    async createBuilding (data: any)  {
        this.buildingCreateRequest()
        return new Promise((resolve, reject) => {
            createBuilding(data).then((res: any) => {
                this.state.buildingCreate = {...res.data, "new": true}
                if (!this.state.buildings) {
                    this.state.buildings = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.buildings && this.state.buildings.data.length > 0) {
                    this.state.buildings.data.push(this.state.buildingCreate)
                    this.state.buildings.total ++
                }
                this.state.buildingCreateSuccess = 'building.createSuccess'
                this.state.buildingCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.buildingCreateSuccess = ''
                this.state.buildingCreateError = 'building.createError'
                reject(err)
            })
        })
    }
    async updateBuilding (data: any)  {
        this.buildingUpdateRequest()
        return new Promise((resolve, reject) => {            
            updateBuilding(data).then((res: any) => {
                this.state.buildingUpdate = {...res.data, "new": true}
                if (this.state.buildings && this.state.buildings.data.length > 0) {
                    const p = this.state.buildings.data.filter((pp: any) => {
                        return pp.ID == this.state.buildingUpdate.id || pp.id == this.state.buildingUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.buildings.data.indexOf(p[0])
                        this.state.buildings.data.splice(i, 1, this.state.buildingUpdate)
                    }
                }

                this.state.buildingUpdateSuccess = 'building.updateSuccess'
                this.state.buildingUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.buildingUpdateSuccess = ''
                this.state.buildingUpdateError = 'building.updateError'
                reject(err)
            })
        })
    }
}

export const buildingStore = new BuildingStore(USER_STORE_NAME);
