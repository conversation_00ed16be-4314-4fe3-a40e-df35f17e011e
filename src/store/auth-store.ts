import {PersistentStore, Store} from "./main"
import {AUTH_STORE_NAME} from "./store-names"
import { login, logout, forgot } from "../api"
import {auth2Store} from "./auth2-store";
interface Auth extends Object {
    token: string,
    locale: string,
    boolMobileMenu: boolean,
    forgotSuccess: string,
    forgotError: string
}

class AuthStore extends PersistentStore<Auth> {
    protected data(): Auth {
        return {
            token: '',
            locale: localStorage.locale || 'en-US',
            boolMobileMenu: false,
            forgotSuccess: '',
            forgotError: ''
        }
    }
    setLocale(p: string) {
        this.state.locale = p
        localStorage.locale = p
    }
    setToken(p: string) {
        this.state.token = p
    }
    forgot (email: string) {
        // return new Promise((resolve, reject) => {        
            forgot(email).then((res: any) => {
                console.log(res)
                this.state.forgotSuccess = 'forgotSuccess'
                this.state.forgotError = ''
                // resolve('forgotSuccess')
            }).catch((err: any) => {
                this.state.forgotSuccess = ''
                this.state.forgotError = 'forgotError'
                // reject('forgotError')
            })
        // })
    }
    forgotToken () {
        this.state.token = ''
    }
    login (data: any) {
        auth2Store.loginRequest()
        this.setToken('')
        login(data).then((res: any) => {
            this.setToken(res.token)
            auth2Store.setProfile(res.user)
            auth2Store.setLoginSuccess("successLogin")
        }).catch((err: any) => {
            if (err.response && err.response.data && err.response.data) {
                auth2Store.setLoginError(err.response.data)
            } else {
                auth2Store.setLoginError('unknownError')
            }
        })
    }
    logout () {
        auth2Store.logoutRequest()
        return new Promise((resolve, reject) => {
            logout(this.state.token).then((res: any) => {
                console.log(res)
                this.state.token = ''
                auth2Store.setLogoutSuccess('logoutSuccess')
                resolve('logoutSuccess')
            }).catch((err: any) => {
                console.log(err)
                auth2Store.setLogoutError('logoutError')
                reject('logoutError')
            })
        })
    }
    setMobileMenu () {
      this.state.boolMobileMenu = !this.state.boolMobileMenu
    }
}

export const authStore = new AuthStore(AUTH_STORE_NAME)
