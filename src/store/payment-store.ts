import {Store} from "./main"
import {PAYMENT_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getPayments, getPayment, createPayment, updatePayment, deletePayment } from '../api'
interface Payment extends Object {
    payment: any
    paymentSuccess: string
    paymentError: string
    payments: any
    paymentsSuccess: string
    paymentsError: string
    paymentCreate: any
    paymentCreateSuccess: string
    paymentCreateError: string
    paymentUpdate: any
    paymentUpdateSuccess: string
    paymentUpdateError: string
    paymentDelete: any
    paymentDeleteSuccess: string
    paymentDeleteError: string
}

class PaymentStore extends Store<Payment> {
    protected data(): Payment {
        return {
            payment: null,
            paymentSuccess: '',
            paymentError: '',
            payments: null,
            paymentsSuccess: '',
            paymentsError: '',
            paymentCreate: null,
            paymentCreateSuccess: '',
            paymentCreateError: '',
            paymentUpdate: null,
            paymentUpdateSuccess: '',
            paymentUpdateError: '',
            paymentDelete: null,
            paymentDeleteSuccess: '',
            paymentDeleteError: ''
        }
    }
    paymentRequest () {
        this.state.payment = null
        this.state.paymentSuccess = ''
        this.state.paymentError = ''
    }
    paymentsRequest () {
        this.state.payments = null
        this.state.paymentsSuccess = ''
        this.state.paymentsError = ''
    }
    paymentCreateRequest () {
        this.state.paymentCreate = null
        this.state.paymentCreateSuccess = ''
        this.state.paymentCreateError = ''
    }
    paymentUpdateRequest () {
        this.state.paymentUpdate = null
        this.state.paymentUpdateSuccess = ''
        this.state.paymentUpdateError = ''
    }
    paymentDeleteRequest () {
        this.state.paymentDelete = null
        this.state.paymentDeleteSuccess = ''
        this.state.paymentDeleteError = ''
    }
    async getPayment (data: any)  {
        this.paymentRequest()
        return new Promise((resolve, reject) => {
            getPayment(data).then((res: any) => {
                this.state.payment = res
                this.state.paymentSuccess = 'payment.successLoadingPayment'
                this.state.paymentError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.paymentSuccess = ''
                this.state.paymentError = 'payment.errLoadingPayment'
            })
        })
    }
    async getPayments (data: any)  {
        this.paymentsRequest()
        return new Promise((resolve, reject) => {
            getPayments(data).then((res: any) => {
                this.state.payments = res
                this.state.paymentsSuccess = 'payment.successLoadingPayments'
                this.state.paymentsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.paymentsSuccess = ''
                this.state.paymentsError = 'payment.errLoadingPayments'
                reject(err)
            })
        })
    }
    async createPayment (data: any)  {
        this.paymentCreateRequest()
        return new Promise((resolve, reject) => {
            createPayment(data).then((res: any) => {
                this.state.paymentCreate = {...res.data, "new": true}
                if (!this.state.payments) {
                    this.state.payments = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.payments && this.state.payments.data.length > 0) {
                    this.state.payments.data.push(this.state.paymentCreate)
                    this.state.payments.total ++
                }
                this.state.paymentCreateSuccess = 'payment.createSuccess'
                this.state.paymentCreateError = ''
                this.state.payments = this.state.payments
                resolve(res)
            }).catch((err: any) => {
                this.state.paymentCreateSuccess = ''
                this.state.paymentCreateError = 'payment.createError'
                reject(err)
            })
        })
    }
    async updatePayment (data: any)  {
        this.paymentUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updatePayment(data).then((res: any) => {
                this.state.paymentUpdate = {...res.data, "new": true}
                if (this.state.payments && this.state.payments.data.length > 0) {
                    const p = this.state.payments.data.filter((pp: any) => {
                        return pp.ID == this.state.paymentUpdate.id || pp.id == this.state.paymentUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.payments.data.indexOf(p[0])
                        this.state.payments.data.splice(i, 1, this.state.paymentUpdate)
                    }
                }

                this.state.paymentUpdateSuccess = 'payment.updateSuccess'
                this.state.paymentUpdateError = ''
                this.state.payments = this.state.payments
                resolve(res)
            }).catch((err: any) => {
                this.state.paymentUpdateSuccess = ''
                this.state.paymentUpdateError = 'payment.updateError'
                reject(err)
            })
        })
    }
    async deletePayment (data: any)  {
        this.paymentDeleteRequest()
        return new Promise((resolve, reject) => {
            deletePayment(data).then((res: any) => {
                this.state.paymentDelete = {...res.data, "delete": true}
                if (this.state.payments && this.state.payments.data.length > 0) {
                    const p = this.state.payments.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.payments.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.payments.data.splice(i, 1)
                            this.state.payments.total - 1
                        }
                    }
                }
                this.state.paymentDeleteSuccess = 'payment.deleteSuccess'
                this.state.paymentDeleteError = ''
                this.state.payments = this.state.payments
                resolve(res)
            }).catch((err: any) => {
                this.state.paymentDeleteSuccess = ''
                this.state.paymentDeleteError = 'payment.deleteError'
                reject(err)
            })
        })
    }

}

export const paymentStore = new PaymentStore(PAYMENT_STORE_NAME);
