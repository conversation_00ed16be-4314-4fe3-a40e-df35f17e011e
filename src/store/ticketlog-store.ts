import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getTicketlogs, getTicketlog, createTicketlog, updateTicketlog, deleteTicketlog } from '../api'
interface Ticketlog extends Object {
    ticketlog: any
    ticketlogSuccess: string
    ticketlogError: string
    ticketlogs: any
    ticketlogsSuccess: string
    ticketlogsError: string
    ticketlogCreate: any
    ticketlogCreateSuccess: string
    ticketlogCreateError: string
    ticketlogUpdate: any
    ticketlogUpdateSuccess: string
    ticketlogUpdateError: string
    ticketlogDelete: any
    ticketlogDeleteSuccess: string
    ticketlogDeleteError: string
}

class TicketlogStore extends Store<Ticketlog> {
    protected data(): Ticketlog {
        return {
            ticketlog: null,
            ticketlogSuccess: '',
            ticketlogError: '',
            ticketlogs: null,
            ticketlogsSuccess: '',
            ticketlogsError: '',
            ticketlogCreate: null,
            ticketlogCreateSuccess: '',
            ticketlogCreateError: '',
            ticketlogUpdate: null,
            ticketlogUpdateSuccess: '',
            ticketlogUpdateError: '',
            ticketlogDelete: null,
            ticketlogDeleteSuccess: '',
            ticketlogDeleteError: ''
        }
    }
    ticketlogRequest () {
        this.state.ticketlog = null
        this.state.ticketlogSuccess = ''
        this.state.ticketlogError = ''
    }
    ticketlogsRequest () {
        this.state.ticketlogs = null
        this.state.ticketlogsSuccess = ''
        this.state.ticketlogsError = ''
    }
    ticketlogCreateRequest () {
        this.state.ticketlogCreate = null
        this.state.ticketlogCreateSuccess = ''
        this.state.ticketlogCreateError = ''
    }
    ticketlogUpdateRequest () {
        this.state.ticketlogUpdate = null
        this.state.ticketlogUpdateSuccess = ''
        this.state.ticketlogUpdateError = ''
    }
    ticketlogDeleteRequest () {
        this.state.ticketlogDelete = null
        this.state.ticketlogDeleteSuccess = ''
        this.state.ticketlogDeleteError = ''
    }
    async getTicketlog (data: any)  {
        this.ticketlogRequest()
        return new Promise((resolve, reject) => {
            getTicketlog(data).then((res: any) => {
                this.state.ticketlog = res
                this.state.ticketlogSuccess = 'ticketlog.successLoadingTicketlog'
                this.state.ticketlogError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketlogSuccess = ''
                this.state.ticketlogError = 'ticketlog.errLoadingTicketlog'
            })
        })
    }
    async getTicketlogs (data: any)  {
        this.ticketlogsRequest()
        return new Promise((resolve, reject) => {
            getTicketlogs(data).then((res: any) => {
                this.state.ticketlogs = res
                this.state.ticketlogsSuccess = 'ticketlog.successLoadingTicketlogs'
                this.state.ticketlogsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketlogsSuccess = ''
                this.state.ticketlogsError = 'ticketlog.errLoadingTicketlogs'
                reject(err)
            })
        })
    }
    async createTicketlog (data: any)  {
        this.ticketlogCreateRequest()
        return new Promise((resolve, reject) => {
            createTicketlog(data).then((res: any) => {
                this.state.ticketlogCreate = {...res.data, "new": true}
                if (!this.state.ticketlogs) {
                    this.state.ticketlogs = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.ticketlogs && this.state.ticketlogs.data.length > 0) {
                    this.state.ticketlogs.data.push(this.state.ticketlogCreate)
                    this.state.ticketlogs.total ++
                }
                this.state.ticketlogCreateSuccess = 'ticketlog.createSuccess'
                this.state.ticketlogCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketlogCreateSuccess = ''
                this.state.ticketlogCreateError = 'ticketlog.createError'
                reject(err)
            })
        })
    }
    async updateTicketlog (data: any)  {
        this.ticketlogUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateTicketlog(data).then((res: any) => {
                this.state.ticketlogUpdate = {...res.data, "new": true}
                if (this.state.ticketlogs && this.state.ticketlogs.data.length > 0) {
                    const p = this.state.ticketlogs.data.filter((pp: any) => {
                        return pp.ID == this.state.ticketlogUpdate.id || pp.id == this.state.ticketlogUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.ticketlogs.data.indexOf(p[0])
                        this.state.ticketlogs.data.splice(i, 1, this.state.ticketlogUpdate)
                    }
                }

                this.state.ticketlogUpdateSuccess = 'ticketlog.updateSuccess'
                this.state.ticketlogUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketlogUpdateSuccess = ''
                this.state.ticketlogUpdateError = 'ticketlog.updateError'
                reject(err)
            })
        })
    }
    async deleteTicketlog (data: any)  {
        this.ticketlogDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteTicketlog(data).then((res: any) => {
                this.state.ticketlogDelete = {...res.data, "delete": true}
                if (this.state.ticketlogs && this.state.ticketlogs.data.length > 0) {
                    const p = this.state.ticketlogs.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.ticketlogs.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.ticketlogs.data.splice(i, 1)
                            this.state.ticketlogs.total - 1
                        }
                    }
                }
                this.state.ticketlogDeleteSuccess = 'ticketlog.deleteSuccess'
                this.state.ticketlogDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketlogDeleteSuccess = ''
                this.state.ticketlogDeleteError = 'ticketlog.deleteError'
                reject(err)
            })
        })
    }

}

export const ticketlogStore = new TicketlogStore(USER_STORE_NAME);
