import {PersistentStore, Store} from "./main";
import {AUTH2_STORE_NAME} from "./store-names";
import { getProfile, updateProfile } from "../api"
// forgotPassword, resetPassword, getUserName
import { authStore } from "./auth-store";

interface Auth2 extends Object {
    loginSuccess: string
    loginError: string
    profile: any
    profileError: string
    logoutError: string
    logoutSuccess: string
    forgotSuccess: string
    forgotError: string
    resetSuccess: string
    resetError: string
    usersname: any
    profileUpdate: any
    profileUpdateSuccess: string
    profileUpdateError: string
}

class Auth2Store extends Store<Auth2> {
    protected data(): Auth2 {
        return {
            loginSuccess: '',
            loginError: '',
            profile: null,
            profileError: '',
            logoutError: '',
            logoutSuccess: '',
            forgotSuccess: '',
            forgotError: '',
            resetSuccess: '',
            resetError: '',
            usersname: {},
            profileUpdate: null,
            profileUpdateSuccess: '',
            profileUpdateError: '',
        }
    }
    setProfile(p: any) {
        this.state.profile = p
    }
    setLoginSuccess(p: string) {
        this.state.loginSuccess = p
    }
    setLoginError(p: string) {
        this.state.loginError = p
    }
    loginRequest () {
        this.setLoginError('')
        this.setLoginSuccess('')
        this.state.profile = null
    }
    setProfileError (err: string) {
        this.state.profileError = err
    }
    hasProfile () {
        return !!this.state.profile
    }
    logoutRequest () {
        this.setLoginError('')
        this.setLoginSuccess('')
        this.setLogoutError('')
        this.setLogoutSuccess('')
        this.state.profile = null
    }
    setLogoutSuccess (p: string) {
        this.state.logoutSuccess = p
    }
    setLogoutError (p: string) {
        this.state.logoutError = p
    }
    async getProfile (token: string)  {
        this.setProfile(null)
        return new Promise((resolve, reject) => {
            getProfile({ token }).then((p: any) => {
                this.setProfile(p)
                resolve(p)
            }).catch((err: any) => {
                this.logoutRequest()
                this.setProfileError('errorProfile')
                authStore.forgotToken()
                reject(err)
            })
        })
    }
    profileUpdateRequest () {
        this.state.profileUpdate = null
        this.state.profileUpdateSuccess = ''
        this.state.profileUpdateError = ''
    }
    async updateProfile (data: any)  {
        this.profileUpdateRequest()
        return new Promise((resolve, reject) => {
            updateProfile(data).then((res: any) => {
                this.state.profileUpdate = {...res.data, "new": true}
                // get profile
                getProfile({token: authStore.getState().token})
                this.state.profileUpdateSuccess = 'profile.updateSuccess'
                this.state.profileUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.profileUpdateSuccess = ''
                this.state.profileUpdateError = 'profile.updateError'
                reject(err)
            })
        })
    }
}

export const auth2Store = new Auth2Store(AUTH2_STORE_NAME);
