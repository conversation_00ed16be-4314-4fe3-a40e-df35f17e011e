import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getPlans, getPlan, createPlan, updatePlan, deletePlan } from '../api'
interface Plan extends Object {
    plan: any
    planSuccess: string
    planError: string
    plans: any
    plansSuccess: string
    plansError: string
    planCreate: any
    planCreateSuccess: string
    planCreateError: string
    planUpdate: any
    planUpdateSuccess: string
    planUpdateError: string
    planDelete: any
    planDeleteSuccess: string
    planDeleteError: string
}

class PlanStore extends Store<Plan> {
    protected data(): Plan {
        return {
            plan: null,
            planSuccess: '',
            planError: '',
            plans: null,
            plansSuccess: '',
            plansError: '',
            planCreate: null,
            planCreateSuccess: '',
            planCreateError: '',
            planUpdate: null,
            planUpdateSuccess: '',
            planUpdateError: '',
            planDelete: null,
            planDeleteSuccess: '',
            planDeleteError: ''
        }
    }
    planRequest () {
        this.state.plan = null
        this.state.planSuccess = ''
        this.state.planError = ''
    }
    plansRequest () {
        this.state.plans = null
        this.state.plansSuccess = ''
        this.state.plansError = ''
    }
    planCreateRequest () {
        this.state.planCreate = null
        this.state.planCreateSuccess = ''
        this.state.planCreateError = ''
    }
    planUpdateRequest () {
        this.state.planUpdate = null
        this.state.planUpdateSuccess = ''
        this.state.planUpdateError = ''
    }
    planDeleteRequest () {
        this.state.planDelete = null
        this.state.planDeleteSuccess = ''
        this.state.planDeleteError = ''
    }
    async getPlan (data: any)  {
        this.planRequest()
        return new Promise((resolve, reject) => {
            getPlan(data).then((res: any) => {
                this.state.plan = res
                this.state.planSuccess = 'plan.successLoadingPlan'
                this.state.planError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.planSuccess = ''
                this.state.planError = 'plan.errLoadingPlan'
            })
        })
    }
    async getPlans (data: any)  {
        this.plansRequest()
        return new Promise((resolve, reject) => {
            getPlans(data).then((res: any) => {
                this.state.plans = res
                this.state.plansSuccess = 'plan.successLoadingPlans'
                this.state.plansError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.plansSuccess = ''
                this.state.plansError = 'plan.errLoadingPlans'
                reject(err)
            })
        })
    }
    async createPlan (data: any)  {
        this.planCreateRequest()
        return new Promise((resolve, reject) => {
            createPlan(data).then((res: any) => {
                this.state.planCreate = {...res.data, "new": true}
                if (!this.state.plans) {
                    this.state.plans = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.plans && this.state.plans.data.length > 0) {
                    this.state.plans.data.push(this.state.planCreate)
                    this.state.plans.total ++
                }
                this.state.planCreateSuccess = 'plan.createSuccess'
                this.state.planCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.planCreateSuccess = ''
                this.state.planCreateError = 'plan.createError'
                reject(err)
            })
        })
    }
    async updatePlan (data: any)  {
        this.planUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updatePlan(data).then((res: any) => {
                this.state.planUpdate = {...res.data, "new": true}
                if (this.state.plans && this.state.plans.data.length > 0) {
                    const p = this.state.plans.data.filter((pp: any) => {
                        return pp.ID == this.state.planUpdate.id || pp.id == this.state.planUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.plans.data.indexOf(p[0])
                        this.state.plans.data.splice(i, 1, this.state.planUpdate)
                    }
                }

                this.state.planUpdateSuccess = 'plan.updateSuccess'
                this.state.planUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.planUpdateSuccess = ''
                this.state.planUpdateError = 'plan.updateError'
                reject(err)
            })
        })
    }
    async deletePlan (data: any)  {
        this.planDeleteRequest()
        return new Promise((resolve, reject) => {
            deletePlan(data).then((res: any) => {
                this.state.planDelete = {...res.data, "delete": true}
                if (this.state.plans && this.state.plans.data.length > 0) {
                    const p = this.state.plans.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.plans.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.plans.data.splice(i, 1)
                            this.state.plans.total - 1
                        }
                    }
                }
                this.state.planDeleteSuccess = 'plan.deleteSuccess'
                this.state.planDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.planDeleteSuccess = ''
                this.state.planDeleteError = 'plan.deleteError'
                reject(err)
            })
        })
    }

}

export const planStore = new PlanStore(USER_STORE_NAME);
