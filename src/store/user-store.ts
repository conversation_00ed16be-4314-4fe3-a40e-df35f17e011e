import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getUsers, getUser, createUser, createUserByAdmin, updateUser } from '../api'
interface User extends Object {
    user: any
    userSuccess: string
    userError: string
    users: any
    usersSuccess: string
    usersError: string
    userCreate: any
    userCreateSuccess: string
    userCreateError: string

    userCreateByAdmin: any
    userCreateByAdminSuccess: string
    userCreateByAdminError: string

    userUpdate: any
    userUpdateSuccess: string
    userUpdateError: string

    // userDelete: any
    // userDeleteSuccess: string
    // userDeleteError: string
}

class UserStore extends Store<User> {
    protected data(): User {
        return {
            user: null,
            userSuccess: '',
            userError: '',
            users: null,
            usersSuccess: '',
            usersError: '',
            userCreate: null,
            userCreateSuccess: '',
            userCreateError: '',
            userCreateByAdmin: null,
            userCreateByAdminSuccess: '',
            userCreateByAdminError: '',
            userUpdate: null,
            userUpdateSuccess: '',
            userUpdateError: '',
            // userDelete: null,
            // userDeleteSuccess: '',
            // userDeleteError: ''
        }
    }
    userRequest () {
        this.state.user = null
        this.state.userSuccess = ''
        this.state.userError = ''
    }
    usersRequest () {
        this.state.users = null
        this.state.usersSuccess = ''
        this.state.usersError = ''
    }
    userCreateRequest () {
        this.state.userCreate = null
        this.state.userCreateSuccess = ''
        this.state.userCreateError = ''
    }
    userCreateByAdminRequest () {
        this.state.userCreateByAdmin = null
        this.state.userCreateByAdminSuccess = ''
        this.state.userCreateByAdminError = ''
    }
    userUpdateRequest () {
        this.state.userUpdate = null
        this.state.userUpdateSuccess = ''
        this.state.userUpdateError = ''
    }
    // userDeleteRequest () {
    //     this.state.userDelete = null
    //     this.state.userDeleteSuccess = ''
    //     this.state.userDeleteError = ''
    // }
    async getUser (data: any)  {
        this.userRequest()
        return new Promise((resolve, reject) => {
            getUser(data).then((res: any) => {
                this.state.user = res
                this.state.userSuccess = 'user.successLoadingUser'
                this.state.userError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.userSuccess = ''
                this.state.userError = 'user.errLoadingUser'
            })
        })
    }
    async getUsers (data: any)  {
        this.usersRequest()
        return new Promise((resolve, reject) => {
            getUsers(data).then((res: any) => {
                this.state.users = res
                this.state.usersSuccess = 'user.successLoadingUsers'
                this.state.usersError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.usersSuccess = ''
                this.state.usersError = 'user.errLoadingUsers'
                reject(err)
            })
        })
    }
    async createUser (data: any)  {
        this.userCreateRequest()
        return new Promise((resolve, reject) => {
            createUser(data).then((res: any) => {
                this.state.userCreate = {...res.data, "new": true}
                if (!this.state.users) {
                    this.state.users = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.users && this.state.users.data.length > 0) {
                    this.state.users.data.push(this.state.userCreate)
                    this.state.users.total ++
                }
                this.state.userCreateSuccess = 'user.createSuccess'
                this.state.userCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.userCreateSuccess = ''
                this.state.userCreateError = 'user.createError'
                reject(err)
            })
        })
    }
    async createUserByAdmin (data: any)  {
        this.userCreateByAdminRequest()
        return new Promise((resolve, reject) => {
            createUserByAdmin(data).then((res: any) => {
                this.state.userCreateByAdmin = {...res.data, "new": true}
                if (!this.state.users) {
                    this.state.users = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.users && this.state.users.data.length > 0) {
                    this.state.users.data.push(this.state.userCreateByAdmin)
                    this.state.users.total ++
                }
                this.state.userCreateByAdminSuccess = 'user.createSuccess'
                this.state.userCreateByAdminError = ''
                resolve(res)
            }).catch((err: any) => {
                let err2: any = err.response.data
                this.state.userCreateByAdminSuccess = ''                
                this.state.userCreateByAdminError = (err2 && err2.error) || "unknw error"
                reject(err)
            })
        })
    }
    async updateUser (data: any)  {
        this.userUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateUser(data).then((res: any) => {
                this.state.userUpdate = {...res.data, "new": true}
                if (this.state.users && this.state.users.data.length > 0) {
                    const p = this.state.users.data.filter((pp: any) => {
                        return pp.ID == this.state.userUpdate.id || pp.id == this.state.userUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.users.data.indexOf(p[0])
                        this.state.users.data.splice(i, 1, this.state.userUpdate)
                    }
                }

                this.state.userUpdateSuccess = 'user.updateSuccess'
                this.state.userUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.userUpdateSuccess = ''
                this.state.userUpdateError = 'user.updateError'
                reject(err)
            })
        })
    }
    // async deleteUser (data: any)  {
    //     this.userDeleteRequest()
    //     return new Promise((resolve, reject) => {
    //         deleteUser(data).then((res: any) => {
    //             this.state.userDelete = {...res.data, "delete": true}
    //             if (this.state.users && this.state.users.data.length > 0) {
    //                 const p = this.state.users.data.filter((pp: any) => {
    //                     return pp.ID == data.id || pp.id == data.id
    //                 })
    //                 if (p && p.length > 0) {
    //                     const i = this.state.users.data.indexOf(p[0])
    //                     if (i > -1) {
    //                         this.state.users.data.splice(i, 1)
    //                         this.state.users.total - 1
    //                     }
    //                 }
    //             }
    //             this.state.userDeleteSuccess = 'user.deleteSuccess'
    //             this.state.userDeleteError = ''
    //             resolve(res)
    //         }).catch((err: any) => {
    //             this.state.userDeleteSuccess = ''
    //             this.state.userDeleteError = 'user.deleteError'
    //             reject(err)
    //         })
    //     })
    // }

}

export const userStore = new UserStore(USER_STORE_NAME);
