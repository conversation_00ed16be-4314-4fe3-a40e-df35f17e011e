import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getTickets, getTicket, createTicket, updateTicket, deleteTicket, getFinanceTickets, getFinanceTicket, createFinanceTicket, updateFinanceTicket, deleteFinanceTicket } from '../api'
interface Ticket extends Object {
    ticket: any
    ticketSuccess: string
    ticketError: string
    tickets: any
    ticketsSuccess: string
    ticketsError: string
    ticketCreate: any
    ticketCreateSuccess: string
    ticketCreateError: string
    ticketUpdate: any
    ticketUpdateSuccess: string
    ticketUpdateError: string
    ticketDelete: any
    ticketDeleteSuccess: string
    ticketDeleteError: string
    // Finance ticket properties
    financeTicket: any
    financeTicketSuccess: string
    financeTicketError: string
    financeTickets: any
    financeTicketsSuccess: string
    financeTicketsError: string
    financeTicketCreate: any
    financeTicketCreateSuccess: string
    financeTicketCreateError: string
    financeTicketUpdate: any
    financeTicketUpdateSuccess: string
    financeTicketUpdateError: string
    financeTicketDelete: any
    financeTicketDeleteSuccess: string
    financeTicketDeleteError: string
}

class TicketStore extends Store<Ticket> {
    protected data(): Ticket {
        return {
            ticket: null,
            ticketSuccess: '',
            ticketError: '',
            tickets: null,
            ticketsSuccess: '',
            ticketsError: '',
            ticketCreate: null,
            ticketCreateSuccess: '',
            ticketCreateError: '',
            ticketUpdate: null,
            ticketUpdateSuccess: '',
            ticketUpdateError: '',
            ticketDelete: null,
            ticketDeleteSuccess: '',
            ticketDeleteError: '',
            // Finance ticket properties
            financeTicket: null,
            financeTicketSuccess: '',
            financeTicketError: '',
            financeTickets: null,
            financeTicketsSuccess: '',
            financeTicketsError: '',
            financeTicketCreate: null,
            financeTicketCreateSuccess: '',
            financeTicketCreateError: '',
            financeTicketUpdate: null,
            financeTicketUpdateSuccess: '',
            financeTicketUpdateError: '',
            financeTicketDelete: null,
            financeTicketDeleteSuccess: '',
            financeTicketDeleteError: ''
        }
    }
    ticketRequest () {
        this.state.ticket = null
        this.state.ticketSuccess = ''
        this.state.ticketError = ''
    }
    ticketsRequest () {
        this.state.tickets = null
        this.state.ticketsSuccess = ''
        this.state.ticketsError = ''
    }
    ticketCreateRequest () {
        this.state.ticketCreate = null
        this.state.ticketCreateSuccess = ''
        this.state.ticketCreateError = ''
    }
    ticketUpdateRequest () {
        this.state.ticketUpdate = null
        this.state.ticketUpdateSuccess = ''
        this.state.ticketUpdateError = ''
    }
    ticketDeleteRequest () {
        this.state.ticketDelete = null
        this.state.ticketDeleteSuccess = ''
        this.state.ticketDeleteError = ''
    }
    // Finance ticket request methods
    financeTicketRequest () {
        this.state.financeTicket = null
        this.state.financeTicketSuccess = ''
        this.state.financeTicketError = ''
    }
    financeTicketsRequest () {
        this.state.financeTickets = null
        this.state.financeTicketsSuccess = ''
        this.state.financeTicketsError = ''
    }
    financeTicketCreateRequest () {
        this.state.financeTicketCreate = null
        this.state.financeTicketCreateSuccess = ''
        this.state.financeTicketCreateError = ''
    }
    financeTicketUpdateRequest () {
        this.state.financeTicketUpdate = null
        this.state.financeTicketUpdateSuccess = ''
        this.state.financeTicketUpdateError = ''
    }
    financeTicketDeleteRequest () {
        this.state.financeTicketDelete = null
        this.state.financeTicketDeleteSuccess = ''
        this.state.financeTicketDeleteError = ''
    }
    async getTicket (data: any)  {
        this.ticketRequest()
        return new Promise((resolve, reject) => {
            getTicket(data).then((res: any) => {
                this.state.ticket = res
                this.state.ticketSuccess = 'ticket.successLoadingTicket'
                this.state.ticketError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketSuccess = ''
                this.state.ticketError = 'ticket.errLoadingTicket'
            })
        })
    }
    async getTickets (data: any)  {
        this.ticketsRequest()
        return new Promise((resolve, reject) => {
            getTickets(data).then((res: any) => {
                this.state.tickets = res
                this.state.ticketsSuccess = 'ticket.successLoadingTickets'
                this.state.ticketsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketsSuccess = ''
                this.state.ticketsError = 'ticket.errLoadingTickets'
                reject(err)
            })
        })
    }
    async createTicket (data: any)  {
        this.ticketCreateRequest()
        return new Promise((resolve, reject) => {
            createTicket(data).then((res: any) => {
                this.state.ticketCreate = {...res.data, "new": true}
                if (!this.state.tickets) {
                    this.state.tickets = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    this.state.tickets.data.push(this.state.ticketCreate)
                    this.state.tickets.total ++
                }
                this.state.ticketCreateSuccess = 'ticket.createSuccess'
                this.state.ticketCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketCreateSuccess = ''
                this.state.ticketCreateError = 'ticket.createError'
                reject(err)
            })
        })
    }
    async updateTicket (data: any)  {
        this.ticketUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateTicket(data).then((res: any) => {
                this.state.ticketUpdate = {...res.data, "new": true}
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    const p = this.state.tickets.data.filter((pp: any) => {
                        return pp.ID == this.state.ticketUpdate.id || pp.id == this.state.ticketUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.tickets.data.indexOf(p[0])
                        this.state.tickets.data.splice(i, 1, this.state.ticketUpdate)
                    }
                }

                this.state.ticketUpdateSuccess = 'ticket.updateSuccess'
                this.state.ticketUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketUpdateSuccess = ''
                this.state.ticketUpdateError = 'ticket.updateError'
                reject(err)
            })
        })
    }
    async deleteTicket (data: any)  {
        this.ticketDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteTicket(data).then((res: any) => {
                this.state.ticketDelete = {...res.data, "delete": true}
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    const p = this.state.tickets.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.tickets.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.tickets.data.splice(i, 1)
                            this.state.tickets.total - 1
                        }
                    }
                }
                this.state.ticketDeleteSuccess = 'ticket.deleteSuccess'
                this.state.ticketDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketDeleteSuccess = ''
                this.state.ticketDeleteError = 'ticket.deleteError'
                reject(err)
            })
        })
    }

    // Finance ticket methods
    async getFinanceTicket (data: any)  {
        this.financeTicketRequest()
        return new Promise((resolve, reject) => {
            getFinanceTicket(data).then((res: any) => {
                this.state.financeTicket = res
                this.state.financeTicketSuccess = 'financeTicket.successLoadingFinanceTicket'
                this.state.financeTicketError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.financeTicketSuccess = ''
                this.state.financeTicketError = 'financeTicket.errLoadingFinanceTicket'
                reject(err)
            })
        })
    }
    async getFinanceTickets (data: any)  {
        this.financeTicketsRequest()
        return new Promise((resolve, reject) => {
            getFinanceTickets(data).then((res: any) => {
                this.state.financeTickets = res
                this.state.financeTicketsSuccess = 'financeTicket.successLoadingFinanceTickets'
                this.state.financeTicketsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.financeTicketsSuccess = ''
                this.state.financeTicketsError = 'financeTicket.errLoadingFinanceTickets'
                reject(err)
            })
        })
    }
    async createFinanceTicket (data: any)  {
        this.financeTicketCreateRequest()
        return new Promise((resolve, reject) => {
            createFinanceTicket(data).then((res: any) => {
                this.state.financeTicketCreate = {...res.data, "new": true}
                if (!this.state.financeTickets) {
                    this.state.financeTickets = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.financeTickets && this.state.financeTickets.data.length > 0) {
                    this.state.financeTickets.data.push(this.state.financeTicketCreate)
                    this.state.financeTickets.total ++
                }
                this.state.financeTicketCreateSuccess = 'financeTicket.createSuccess'
                this.state.financeTicketCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.financeTicketCreateSuccess = ''
                this.state.financeTicketCreateError = 'financeTicket.createError'
                reject(err)
            })
        })
    }
    async updateFinanceTicket (data: any)  {
        this.financeTicketUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateFinanceTicket(data).then((res: any) => {
                this.state.financeTicketUpdate = {...res.data, "new": true}
                if (this.state.financeTickets && this.state.financeTickets.data.length > 0) {
                    const p = this.state.financeTickets.data.filter((pp: any) => {
                        return pp.ID == this.state.financeTicketUpdate.id || pp.id == this.state.financeTicketUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.financeTickets.data.indexOf(p[0])
                        this.state.financeTickets.data.splice(i, 1, this.state.financeTicketUpdate)
                    }
                }

                this.state.financeTicketUpdateSuccess = 'financeTicket.updateSuccess'
                this.state.financeTicketUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.financeTicketUpdateSuccess = ''
                this.state.financeTicketUpdateError = 'financeTicket.updateError'
                reject(err)
            })
        })
    }
    async deleteFinanceTicket (data: any)  {
        this.financeTicketDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteFinanceTicket(data).then((res: any) => {
                this.state.financeTicketDelete = {...res.data, "delete": true}
                if (this.state.financeTickets && this.state.financeTickets.data.length > 0) {
                    const p = this.state.financeTickets.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.financeTickets.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.financeTickets.data.splice(i, 1)
                            this.state.financeTickets.total - 1
                        }
                    }
                }
                this.state.financeTicketDeleteSuccess = 'financeTicket.deleteSuccess'
                this.state.financeTicketDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.financeTicketDeleteSuccess = ''
                this.state.financeTicketDeleteError = 'financeTicket.deleteError'
                reject(err)
            })
        })
    }

}

export const ticketStore = new TicketStore(USER_STORE_NAME);
