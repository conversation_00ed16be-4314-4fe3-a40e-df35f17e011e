import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getPrebill, getPrebills, createPrebills, updatePrebills, deletePrebills } from '../api'
interface Prebills extends Object {
    prebills: any
    prebillsSuccess: string
    prebillsError: string
    prebillss: any
    prebillssSuccess: string
    prebillssError: string
    prebillsCreate: any
    prebillsCreateSuccess: string
    prebillsCreateError: string
    prebillsUpdate: any
    prebillsUpdateSuccess: string
    prebillsUpdateError: string
    prebillsDelete: any
    prebillsDeleteSuccess: string
    prebillsDeleteError: string
}

class PrebillsStore extends Store<Prebills> {
    protected data(): Prebills {
        return {
            prebills: null,
            prebillsSuccess: '',
            prebillsError: '',
            prebillss: null,
            prebillssSuccess: '',
            prebillssError: '',
            prebillsCreate: null,
            prebillsCreateSuccess: '',
            prebillsCreateError: '',
            prebillsUpdate: null,
            prebillsUpdateSuccess: '',
            prebillsUpdateError: '',
            prebillsDelete: null,
            prebillsDeleteSuccess: '',
            prebillsDeleteError: ''
        }
    }
    prebillsRequest () {
        this.state.prebills = null
        this.state.prebillsSuccess = ''
        this.state.prebillsError = ''
    }
    prebillssRequest () {
        this.state.prebillss = null
        this.state.prebillssSuccess = ''
        this.state.prebillssError = ''
    }
    prebillsCreateRequest () {
        this.state.prebillsCreate = null
        this.state.prebillsCreateSuccess = ''
        this.state.prebillsCreateError = ''
    }
    prebillsUpdateRequest () {
        this.state.prebillsUpdate = null
        this.state.prebillsUpdateSuccess = ''
        this.state.prebillsUpdateError = ''
    }
    prebillsDeleteRequest () {
        this.state.prebillsDelete = null
        this.state.prebillsDeleteSuccess = ''
        this.state.prebillsDeleteError = ''
    }
    async getPrebills (data: any)  {
        this.prebillsRequest()
        return new Promise((resolve, reject) => {
            getPrebills(data).then((res: any) => {
                this.state.prebills = res
                this.state.prebillsSuccess = 'prebills.successLoadingPrebills'
                this.state.prebillsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.prebillsSuccess = ''
                this.state.prebillsError = 'prebills.errLoadingPrebills'
            })
        })
    }
    async getPrebill (data: any)  {
        this.prebillssRequest()
        return new Promise((resolve, reject) => {
            getPrebill(data).then((res: any) => {
                this.state.prebillss = res
                this.state.prebillssSuccess = 'prebills.successLoadingPrebillss'
                this.state.prebillssError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.prebillssSuccess = ''
                this.state.prebillssError = 'prebills.errLoadingPrebillss'
                reject(err)
            })
        })
    }
    async createPrebills (data: any)  {
        this.prebillsCreateRequest()
        return new Promise((resolve, reject) => {
            createPrebills(data).then((res: any) => {
                this.state.prebillsCreate = {...res.data, "new": true}
                if (!this.state.prebillss) {
                    this.state.prebillss = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.prebillss && this.state.prebillss.data.length > 0) {
                    this.state.prebillss.data.push(this.state.prebillsCreate)
                    this.state.prebillss.total ++
                }
                this.state.prebillsCreateSuccess = 'prebills.createSuccess'
                this.state.prebillsCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.prebillsCreateSuccess = ''
                this.state.prebillsCreateError = 'prebills.createError'
                reject(err)
            })
        })
    }
    async updatePrebills (data: any)  {
        this.prebillsUpdateRequest()
        return new Promise((resolve, reject) => {            
            updatePrebills(data).then((res: any) => {
                this.state.prebillsUpdate = {...res.data, "new": true}
                if (this.state.prebillss && this.state.prebillss.data.length > 0) {
                    const p = this.state.prebillss.data.filter((pp: any) => {
                        return pp.ID == this.state.prebillsUpdate.id || pp.id == this.state.prebillsUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.prebillss.data.indexOf(p[0])
                        this.state.prebillss.data.splice(i, 1, this.state.prebillsUpdate)
                    }
                }

                this.state.prebillsUpdateSuccess = 'prebills.updateSuccess'
                this.state.prebillsUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.prebillsUpdateSuccess = ''
                this.state.prebillsUpdateError = 'prebills.updateError'
                reject(err)
            })
        })
    }
    async deletePrebills (data: any)  {
        this.prebillsDeleteRequest()
        return new Promise((resolve, reject) => {
            deletePrebills(data).then((res: any) => {
                this.state.prebillsDelete = {...res.data, "delete": true}
                if (this.state.prebillss && this.state.prebillss.data.length > 0) {
                    const p = this.state.prebillss.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.prebillss.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.prebillss.data.splice(i, 1)
                            this.state.prebillss.total - 1
                        }
                    }
                }
                this.state.prebillsDeleteSuccess = 'prebills.deleteSuccess'
                this.state.prebillsDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.prebillsDeleteSuccess = ''
                this.state.prebillsDeleteError = 'prebills.deleteError'
                reject(err)
            })
        })
    }

}

export const prebillsStore = new PrebillsStore(USER_STORE_NAME);
