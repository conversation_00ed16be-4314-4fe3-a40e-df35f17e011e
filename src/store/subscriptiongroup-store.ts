import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import { getSubscriptionGroups, getSubscriptionGroup, createSubscriptionGroup, updateSubscriptionGroup } from '../api'
interface SubscriptionGroup extends Object {
    subscriptionGroup: any
    subscriptionGroupSuccess: string
    subscriptionGroupError: string
    subscriptionGroups: any
    subscriptionGroupsSuccess: string
    subscriptionGroupsError: string
    subscriptionGroupCreate: any
    subscriptionGroupCreateSuccess: string
    subscriptionGroupCreateError: string
    subscriptionGroupUpdate: any
    subscriptionGroupUpdateSuccess: string
    subscriptionGroupUpdateError: string
    subscriptionGroupDelete: any
    subscriptionGroupDeleteSuccess: string
    subscriptionGroupDeleteError: string
}

class SubscriptionGroupStore extends Store<SubscriptionGroup> {
    protected data(): SubscriptionGroup {
        return {
            subscriptionGroup: null,
            subscriptionGroupSuccess: '',
            subscriptionGroupError: '',
            subscriptionGroups: null,
            subscriptionGroupsSuccess: '',
            subscriptionGroupsError: '',
            subscriptionGroupCreate: null,
            subscriptionGroupCreateSuccess: '',
            subscriptionGroupCreateError: '',
            subscriptionGroupUpdate: null,
            subscriptionGroupUpdateSuccess: '',
            subscriptionGroupUpdateError: '',
            subscriptionGroupDelete: null,
            subscriptionGroupDeleteSuccess: '',
            subscriptionGroupDeleteError: ''
        }
    }
    subscriptionGroupRequest () {
        this.state.subscriptionGroup = null
        this.state.subscriptionGroupSuccess = ''
        this.state.subscriptionGroupError = ''
    }
    subscriptionGroupsRequest () {
        this.state.subscriptionGroups = null
        this.state.subscriptionGroupsSuccess = ''
        this.state.subscriptionGroupsError = ''
    }
    subscriptionGroupCreateRequest () {
        this.state.subscriptionGroupCreate = null
        this.state.subscriptionGroupCreateSuccess = ''
        this.state.subscriptionGroupCreateError = ''
    }
    subscriptionGroupUpdateRequest () {
        this.state.subscriptionGroupUpdate = null
        this.state.subscriptionGroupUpdateSuccess = ''
        this.state.subscriptionGroupUpdateError = ''
    }
    subscriptionGroupDeleteRequest () {
        this.state.subscriptionGroupDelete = null
        this.state.subscriptionGroupDeleteSuccess = ''
        this.state.subscriptionGroupDeleteError = ''
    }
    async getSubscriptionGroup (data: any)  {
        this.subscriptionGroupRequest()
        return new Promise((resolve, reject) => {
            getSubscriptionGroup(data).then((res: any) => {
                this.state.subscriptionGroup = res
                this.state.subscriptionGroupSuccess = 'subscriptionGroup.successLoadingSubscriptionGroup'
                this.state.subscriptionGroupError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionGroupSuccess = ''
                this.state.subscriptionGroupError = 'subscriptionGroup.errLoadingSubscriptionGroup'
            })
        })
    }
    async getSubscriptionGroups (data: any)  {
        this.subscriptionGroupsRequest()
        return new Promise((resolve, reject) => {
            getSubscriptionGroups(data).then((res: any) => {
                this.state.subscriptionGroups = res
                this.state.subscriptionGroupsSuccess = 'subscriptionGroup.successLoadingSubscriptionGroups'
                this.state.subscriptionGroupsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionGroupsSuccess = ''
                this.state.subscriptionGroupsError = 'subscriptionGroup.errLoadingSubscriptionGroups'
                reject(err)
            })
        })
    }
    async createSubscriptionGroup (data: any)  {
        this.subscriptionGroupCreateRequest()
        return new Promise((resolve, reject) => {
            createSubscriptionGroup(data).then((res: any) => {
                this.state.subscriptionGroupCreate = {...res.data, "new": true}
                if (!this.state.subscriptionGroups) {
                    this.state.subscriptionGroups = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.subscriptionGroups && this.state.subscriptionGroups.data.length > 0) {
                    this.state.subscriptionGroups.data.push(this.state.subscriptionGroupCreate)
                    this.state.subscriptionGroups.total ++
                }
                this.state.subscriptionGroupCreateSuccess = 'subscriptionGroup.createSuccess'
                this.state.subscriptionGroupCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionGroupCreateSuccess = ''
                this.state.subscriptionGroupCreateError = 'subscriptionGroup.createError'
                reject(err)
            })
        })
    }
    async updateSubscriptionGroup (data: any)  {
        this.subscriptionGroupUpdateRequest()
        return new Promise((resolve, reject) => {            
            updateSubscriptionGroup(data).then((res: any) => {
                this.state.subscriptionGroupUpdate = {...res.data, "new": true}
                if (this.state.subscriptionGroups && this.state.subscriptionGroups.data.length > 0) {
                    const p = this.state.subscriptionGroups.data.filter((pp: any) => {
                        return pp.ID == this.state.subscriptionGroupUpdate.id || pp.id == this.state.subscriptionGroupUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.subscriptionGroups.data.indexOf(p[0])
                        this.state.subscriptionGroups.data.splice(i, 1, this.state.subscriptionGroupUpdate)
                    }
                }

                this.state.subscriptionGroupUpdateSuccess = 'subscriptionGroup.updateSuccess'
                this.state.subscriptionGroupUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionGroupUpdateSuccess = ''
                this.state.subscriptionGroupUpdateError = 'subscriptionGroup.updateError'
                reject(err)
            })
        })
    }
}

export const subscriptionGroupStore = new SubscriptionGroupStore(USER_STORE_NAME);
