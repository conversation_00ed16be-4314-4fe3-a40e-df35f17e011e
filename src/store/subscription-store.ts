import {Store} from "./main"
import {SUBSCRIPTION_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getSubscriptions, getSubscription, createSubscription, updateSubscription, deleteSubscription } from '../api'
interface Subscription extends Object {
    subscription: any
    subscriptionSuccess: string
    subscriptionError: string
    subscriptions: any
    subscriptionsSuccess: string
    subscriptionsError: string
    subscriptionCreate: any
    subscriptionCreateSuccess: string
    subscriptionCreateError: string
    subscriptionUpdate: any
    subscriptionUpdateSuccess: string
    subscriptionUpdateError: string
    subscriptionDelete: any
    subscriptionDeleteSuccess: string
    subscriptionDeleteError: string
}

class SubscriptionStore extends Store<Subscription> {
    protected data(): Subscription {
        return {
            subscription: null,
            subscriptionSuccess: '',
            subscriptionError: '',
            subscriptions: null,
            subscriptionsSuccess: '',
            subscriptionsError: '',
            subscriptionCreate: null,
            subscriptionCreateSuccess: '',
            subscriptionCreateError: '',
            subscriptionUpdate: null,
            subscriptionUpdateSuccess: '',
            subscriptionUpdateError: '',
            subscriptionDelete: null,
            subscriptionDeleteSuccess: '',
            subscriptionDeleteError: ''
        }
    }
    subscriptionRequest () {
        this.state.subscription = null
        this.state.subscriptionSuccess = ''
        this.state.subscriptionError = ''
    }
    subscriptionsRequest () {
        this.state.subscriptions = null
        this.state.subscriptionsSuccess = ''
        this.state.subscriptionsError = ''
    }
    subscriptionCreateRequest () {
        this.state.subscriptionCreate = null
        this.state.subscriptionCreateSuccess = ''
        this.state.subscriptionCreateError = ''
    }
    subscriptionUpdateRequest () {
        this.state.subscriptionUpdate = null
        this.state.subscriptionUpdateSuccess = ''
        this.state.subscriptionUpdateError = ''
    }
    subscriptionDeleteRequest () {
        this.state.subscriptionDelete = null
        this.state.subscriptionDeleteSuccess = ''
        this.state.subscriptionDeleteError = ''
    }
    async getSubscription (data: any)  {
        this.subscriptionRequest()
        return new Promise((resolve, reject) => {
            getSubscription(data).then((res: any) => {
                this.state.subscription = res
                this.state.subscriptionSuccess = 'subscription.successLoadingSubscription'
                this.state.subscriptionError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionSuccess = ''
                this.state.subscriptionError = 'subscription.errLoadingSubscription'
            })
        })
    }
    async getSubscriptions (data: any)  {
        this.subscriptionsRequest()
        return new Promise((resolve, reject) => {
            getSubscriptions(data).then((res: any) => {
                this.state.subscriptions = res
                this.state.subscriptionsSuccess = 'subscription.successLoadingSubscriptions'
                this.state.subscriptionsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionsSuccess = ''
                this.state.subscriptionsError = 'subscription.errLoadingSubscriptions'
                reject(err)
            })
        })
    }
    async getSubscriptionByName(data: any) {
        return new Promise((resolve, reject) => {
          getSubscriptions(data).then((res: any) => {
            resolve(res.data);
          }).catch((err: any) => {
            reject(err);
          });
        });
    }
      
    async getAllSubscriptionsByCustomers(dataArray: any[]) {
        this.subscriptionsRequest();
        const allSubscriptions: any[] = [];
        const promises = dataArray.map((data: any) => this.getSubscriptionByName(data));
        
        Promise.all(promises).then((results) => {
            results.forEach((newSubscriptions: any) => {
                newSubscriptions.forEach((subscription: any) => {
                    allSubscriptions.push(subscription);
                });
            });
        
            this.state.subscriptions = {
                data: allSubscriptions,
                limit: 10,
                skip: 0,
                total: allSubscriptions.length
            };
            this.state.subscriptionsSuccess = 'subscription.successLoadingSubscriptions';
            this.state.subscriptionsError = '';
        }).catch((err) => {
            this.state.subscriptionsSuccess = '';
            this.state.subscriptionsError = 'subscription.errLoadingSubscriptions';
        });
    }
    async createSubscription (data: any)  {
        this.subscriptionCreateRequest()
        return new Promise((resolve, reject) => {
            createSubscription(data).then((res: any) => {
                this.state.subscriptionCreate = {...res.data, "new": true}
                if (!this.state.subscriptions) {
                    this.state.subscriptions = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.subscriptions && this.state.subscriptions.data.length > 0) {
                    this.state.subscriptions.data.push(this.state.subscriptionCreate)
                    this.state.subscriptions.total ++
                }
                this.state.subscriptionCreateSuccess = 'subscription.createSuccess'
                this.state.subscriptionCreateError = ''
                this.state.subscriptions = this.state.subscriptions
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionCreateSuccess = ''
                this.state.subscriptionCreateError = 'subscription.createError'
                reject(err)
            })
        })
    }
    async updateSubscription (data: any)  {
        this.subscriptionUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateSubscription(data).then((res: any) => {
                this.state.subscriptionUpdate = {...res.data, "new": true}
                if (this.state.subscriptions && this.state.subscriptions.data.length > 0) {
                    const p = this.state.subscriptions.data.filter((pp: any) => {
                        return pp.ID == this.state.subscriptionUpdate.id || pp.id == this.state.subscriptionUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.subscriptions.data.indexOf(p[0])
                        this.state.subscriptions.data.splice(i, 1, this.state.subscriptionUpdate)
                    }
                }

                this.state.subscriptionUpdateSuccess = 'subscription.updateSuccess'
                this.state.subscriptionUpdateError = ''
                this.state.subscriptions = this.state.subscriptions
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionUpdateSuccess = ''
                this.state.subscriptionUpdateError = 'subscription.updateError'
                reject(err)
            })
        })
    }
    async deleteSubscription (data: any)  {
        this.subscriptionDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteSubscription(data).then((res: any) => {
                this.state.subscriptionDelete = {...res.data, "delete": true}
                if (this.state.subscriptions && this.state.subscriptions.data.length > 0) {
                    const p = this.state.subscriptions.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.subscriptions.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.subscriptions.data.splice(i, 1)
                            this.state.subscriptions.total - 1
                        }
                    }
                }
                this.state.subscriptionDeleteSuccess = 'subscription.deleteSuccess'
                this.state.subscriptionDeleteError = ''
                this.state.subscriptions = this.state.subscriptions
                resolve(res)
            }).catch((err: any) => {
                this.state.subscriptionDeleteSuccess = ''
                this.state.subscriptionDeleteError = 'subscription.deleteError'
                reject(err)
            })
        })
    }

}

export const subscriptionStore = new SubscriptionStore(SUBSCRIPTION_STORE_NAME);
