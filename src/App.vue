<template>
  <div v-if="isInitialized">
    <modalShow :item="modalmsg" v-if="modalmsg" :cancelFunc="hideModal"></modalShow>
    <notifyShow :item="notimsg" v-if="notimsg" :hideItem="hideNot"></notifyShow>
    <router-view />
  </div>
  <teleport to="head">
    <title>{{ pageTitle }}</title>
    <meta property="og:description" :content="pageDescription">
    <link rel="icon" href="/favicon.png" />
  </teleport>
</template>

<script>
import { defineComponent, defineAsyncComponent, watch, ref, onBeforeMount, inject, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { SUPPORT_LOCALES } from './i18n'
import modalShow from '@/components/cvui/ModalShow.vue'
import notifyShow from '@/components/cvui/NotifyShow.vue'
import { crossStore } from '@/store/cross-store'
import { auth2Store } from '@/store/auth2-store'
import config from "@/config"
export default defineComponent({
  name: 'App',
  components: {
    modalShow,
    notifyShow,
  },
  methods: {
    hideNot() {
      crossStore.SetNotmsg(null)
    },
    hideModal() {
      crossStore.SetModalmsg(null)
    },
  },
  watch: {
    profileError (p) {
      if (p) {
        this.$router.push({ name: 'home', params: { locale: this.locale }})
      }
    }
  },
  computed: {
    profileError () {
      return this.auth2State.profileError
    }
  },
  setup() {
    const authStore = inject("authStore")
    const crossState = crossStore.getState()
    onBeforeMount(async () => await authStore.init())
    const router = useRouter()
    const { t, locale } = useI18n({ useScope: 'global' })

    const cstate = crossStore.getState()
    // config.setTitle('')
    return {
      pageTitle: config.title,
      pageDescription: '',
      t,
      locale,
      authStore: authStore,
      authState: authStore.getState(),
      auth2State: auth2Store.getState(),
      isInitialized: authStore.getIsInitialized(),
      modalmsg: computed(() => cstate.modalmsg),
      notimsg: computed(() => cstate.notimsg),
    }
  },
})
</script>

<style scoped>
nav {
  display: inline-flex;
}
.navigation {
  margin-right: 1rem;
}
.language label {
  margin-right: 1rem;
}
</style>
