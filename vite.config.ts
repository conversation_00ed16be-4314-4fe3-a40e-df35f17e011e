import path from 'path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueI18n from '@intlify/vite-plugin-vue-i18n'
import { nodePolyfills } from 'vite-plugin-node-polyfills';
const fs = require('fs')
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    nodePolyfills(),
    vueI18n({
      include: path.resolve(__dirname, './src/locales/**')
    })],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '/src'),
      },
    },
    server: {
      port: 8055,
      open: true,
      https: {
        key: fs.readFileSync('certs/localhost.key'),
        cert: fs.readFileSync('certs/localhost.crt'),
      },
    }
})
