<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Static Test Page</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f0f0;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 10px 0;
      cursor: pointer;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Static Test Page</h1>
    <p>This is a completely static HTML page that doesn't rely on Vue or any other framework.</p>
    <p>If you can see this page, it means your browser can render basic HTML content from the server.</p>
    <button id="testButton">Click Me</button>
    <p id="result"></p>
  </div>

  <script>
    document.getElementById('testButton').addEventListener('click', function() {
      document.getElementById('result').textContent = 'Button clicked at: ' + new Date().toLocaleTimeString();
    });
  </script>
</body>
</html>
