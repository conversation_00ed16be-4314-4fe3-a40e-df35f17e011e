<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue Test Page</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f0f0;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 10px 0;
      cursor: pointer;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container">
      <h1>{{ title }}</h1>
      <p>This is a minimal Vue 3 application loaded directly from a CDN.</p>
      <p>If you can see this page with the title "Vue Test Page" above, it means Vue is working correctly in your browser.</p>
      <button @click="count++">Count: {{ count }}</button>
      <p>Current time: {{ currentTime }}</p>
    </div>
  </div>

  <script>
    const { createApp, ref, computed } = Vue;
    
    createApp({
      setup() {
        const title = ref('Vue Test Page');
        const count = ref(0);
        
        const currentTime = computed(() => {
          return new Date().toLocaleTimeString();
        });
        
        return {
          title,
          count,
          currentTime
        };
      }
    }).mount('#app');
  </script>
</body>
</html>
